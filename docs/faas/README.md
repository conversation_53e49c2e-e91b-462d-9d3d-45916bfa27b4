# FaaS (Function as a Service) 功能文档

## 概述

PaaS 平台的 FaaS 功能提供了一个完整的无服务器函数执行环境，支持多种编程语言运行时，具备容器池管理、资源限制、监控指标等企业级特性。

## 功能特性

### 🚀 核心功能
- **多运行时支持**: Node.js、Python、Go、Java
- **同步/异步执行**: 支持同步和异步函数执行模式
- **容器池管理**: 智能容器复用和预热机制
- **资源限制**: CPU、内存、磁盘使用限制
- **执行监控**: 详细的执行指标和日志记录

### 🛡️ 企业级特性
- **高可用性**: 容器故障自动恢复
- **负载均衡**: 智能容器分配和负载均衡
- **安全隔离**: 容器级别的安全隔离
- **监控告警**: Prometheus 指标集成
- **日志追踪**: 完整的执行日志和链路追踪

### 📊 性能优化
- **容器预热**: 减少冷启动时间
- **代码缓存**: 函数代码智能缓存
- **连接池**: 数据库连接池管理
- **资源复用**: 容器和资源智能复用

## 架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Gateway   │───▶│  FaaS Service   │───▶│ Container Pool  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                                ▼                       ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   Monitoring    │    │ Docker Engine   │
                       └─────────────────┘    └─────────────────┘
```

### 组件说明

1. **API Gateway**: 请求路由和负载均衡
2. **FaaS Service**: 函数执行器核心服务
3. **Container Pool**: 容器池管理器
4. **Docker Engine**: 容器运行时环境
5. **Monitoring**: 监控和指标收集

## 快速开始

### 1. 启动 FaaS 服务

```bash
# 使用默认配置启动
./scripts/start-faas-service.sh

# 使用开发环境配置启动
./scripts/start-faas-service.sh -d

# 使用 Docker 启动
./scripts/start-faas-service.sh --docker
```

### 2. 健康检查

```bash
# 基本健康检查
./scripts/health-check-faas.sh

# 完整健康检查
./scripts/health-check-faas.sh --full

# 查看服务指标
./scripts/health-check-faas.sh --metrics
```

### 3. 功能测试

```bash
# 测试所有功能
./scripts/test-faas-functions.sh

# 测试特定运行时
./scripts/test-faas-functions.sh --nodejs
./scripts/test-faas-functions.sh --python

# 测试异步执行
./scripts/test-faas-functions.sh --async
```

## API 文档

### 基础信息
- **服务端口**: 8087
- **API 前缀**: `/api/v1/faas`
- **内容类型**: `application/json`

### 主要接口

#### 1. 同步执行函数
```http
POST /api/v1/faas/functions/execute
Content-Type: application/json

{
    "function_id": "my-function",
    "function_name": "My Function",
    "runtime": "nodejs",
    "handler": "main",
    "code": "function main(params) { return 'Hello, ' + params.name; }",
    "parameters": {
        "name": "World"
    },
    "timeout": "30s",
    "memory_limit": "512m"
}
```

#### 2. 异步执行函数
```http
POST /api/v1/faas/functions/async-execute
Content-Type: application/json

{
    "function_id": "long-running-task",
    "function_name": "Long Running Task",
    "runtime": "python",
    "handler": "main",
    "code": "def main(params):\n    import time\n    time.sleep(10)\n    return 'Task completed'",
    "parameters": {},
    "timeout": "60s"
}
```

#### 3. 获取执行指标
```http
GET /api/v1/faas/functions/metrics
```

#### 4. 获取容器池状态
```http
GET /api/v1/faas/functions/pool/stats
```

#### 5. 健康检查
```http
GET /api/v1/faas/functions/health
```

### 响应格式

#### 成功响应
```json
{
    "status": "success",
    "request_id": "req-123456",
    "function_id": "my-function",
    "result": "Hello, World",
    "execution_time": "150ms",
    "memory_usage": "45MB",
    "logs": ["[10:30:15] 函数开始执行", "[10:30:15] 函数执行完成"],
    "timestamp": "2024-01-15T10:30:15Z"
}
```

#### 错误响应
```json
{
    "status": "error",
    "request_id": "req-123456",
    "function_id": "my-function",
    "error": "函数执行超时",
    "error_code": "EXECUTION_TIMEOUT",
    "logs": ["[10:30:15] 函数开始执行", "[10:30:45] 执行超时"],
    "timestamp": "2024-01-15T10:30:45Z"
}
```

## 支持的运行时

### Node.js
- **镜像**: `node:18-alpine`
- **文件扩展名**: `.js`
- **默认处理函数**: `main`
- **示例代码**:
```javascript
function main(params) {
    const name = params.name || 'World';
    return {
        message: `Hello, ${name}!`,
        timestamp: new Date().toISOString()
    };
}
```

### Python
- **镜像**: `python:3.11-alpine`
- **文件扩展名**: `.py`
- **默认处理函数**: `main`
- **示例代码**:
```python
def main(params):
    import datetime
    name = params.get('name', 'World')
    return {
        'message': f'Hello, {name}!',
        'timestamp': datetime.datetime.now().isoformat()
    }
```

### Go
- **镜像**: `golang:1.21-alpine`
- **文件扩展名**: `.go`
- **默认处理函数**: `main`
- **示例代码**:
```go
func main(params map[string]interface{}) (interface{}, error) {
    name, ok := params["name"].(string)
    if !ok {
        name = "World"
    }
    
    return map[string]interface{}{
        "message": fmt.Sprintf("Hello, %s!", name),
        "timestamp": time.Now().Format(time.RFC3339),
    }, nil
}
```

### Java
- **镜像**: `openjdk:11-jre-slim`
- **文件扩展名**: `.java`
- **默认处理函数**: `handle`
- **示例代码**:
```java
public static Object handle(Map<String, Object> params) {
    String name = (String) params.getOrDefault("name", "World");
    Map<String, Object> result = new HashMap<>();
    result.put("message", "Hello, " + name + "!");
    result.put("timestamp", Instant.now().toString());
    return result;
}
```

## 配置说明

### 服务配置
配置文件位置: `configs/faas-service.yaml`

```yaml
# 执行器配置
executor:
  max_concurrent_executions: 100    # 最大并发执行数
  default_timeout: 30s              # 默认超时时间
  max_execution_time: 300s          # 最大执行时间
  container_pool_size: 20           # 容器池大小
  prewarm_containers: 5             # 预热容器数量
  cleanup_interval: 300s            # 清理间隔
  
  # 资源限制
  resource_limits:
    cpu_limit: "1.0"                # CPU 限制
    memory_limit: "1Gi"             # 内存限制
    disk_limit: "2Gi"               # 磁盘限制
```

### 环境变量
- `FAAS_SERVICE_CONFIG_FILE`: 配置文件路径
- `FAAS_SERVICE_LOG_LEVEL`: 日志级别
- `FAAS_SERVICE_PORT`: 服务端口

## 监控和日志

### Prometheus 指标
- `faas_executions_total`: 总执行次数
- `faas_executions_duration_seconds`: 执行时间分布
- `faas_container_pool_size`: 容器池大小
- `faas_active_executions`: 当前活跃执行数

### 日志格式
```json
{
    "timestamp": "2024-01-15T10:30:15Z",
    "level": "info",
    "service": "faas-executor",
    "request_id": "req-123456",
    "function_id": "my-function",
    "runtime": "nodejs",
    "execution_time": "150ms",
    "memory_usage": "45MB",
    "message": "函数执行完成"
}
```

## 故障排除

### 常见问题

1. **容器启动失败**
   - 检查 Docker 守护进程状态
   - 验证镜像是否存在
   - 检查资源限制配置

2. **函数执行超时**
   - 增加超时时间配置
   - 优化函数代码性能
   - 检查容器资源使用情况

3. **内存不足**
   - 增加内存限制配置
   - 优化函数内存使用
   - 检查容器池大小设置

### 调试命令
```bash
# 查看服务状态
./scripts/start-faas-service.sh --status

# 查看服务日志
./scripts/start-faas-service.sh --logs

# 执行健康检查
./scripts/health-check-faas.sh -v --full

# 查看容器状态
docker ps | grep faas
```

## 最佳实践

### 函数开发
1. **保持函数简单**: 单一职责原则
2. **避免全局状态**: 使用无状态设计
3. **合理设置超时**: 根据业务需求设置合适的超时时间
4. **错误处理**: 完善的错误处理和日志记录

### 性能优化
1. **预热容器**: 为常用运行时预热容器
2. **代码优化**: 减少函数执行时间
3. **资源配置**: 合理配置 CPU 和内存限制
4. **监控告警**: 设置合适的监控指标和告警

### 安全考虑
1. **输入验证**: 严格验证函数输入参数
2. **资源限制**: 设置合理的资源使用限制
3. **网络隔离**: 限制函数的网络访问权限
4. **日志脱敏**: 避免在日志中记录敏感信息

## 更新日志

### v1.0.0 (2024-01-15)
- 初始版本发布
- 支持 Node.js、Python、Go、Java 运行时
- 实现容器池管理和资源限制
- 集成监控和日志功能
- 提供完整的 API 接口
