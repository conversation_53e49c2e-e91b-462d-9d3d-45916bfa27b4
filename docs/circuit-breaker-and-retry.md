# 断路器和重试机制

## 📋 概述

本文档介绍了 PaaS 平台 API 网关中新实现的断路器和重试机制。这些弹性组件提供了完整的服务容错能力，包括断路器模式、智能重试策略、服务降级功能等，能够显著提升系统的可靠性和容错能力，确保在部分服务故障时系统仍能正常运行。

## 🏗️ 系统架构

```
客户端请求 → 弹性中间件 → 断路器检查 → 重试执行 → 服务调用 → 响应处理
     ↓           ↓           ↓          ↓         ↓
[弹性管理器] [断路器管理器] [重试管理器] [降级处理] [统计监控]
```

### 核心组件

1. **CircuitBreaker** (`pkg/circuitbreaker/circuit_breaker.go`)
   - 断路器核心实现
   - 状态管理（关闭/开启/半开）
   - 失败率和慢调用检测

2. **Retrier** (`pkg/retry/retry.go`)
   - 重试器核心实现
   - 多种重试策略
   - 智能退避算法

3. **Manager** (`pkg/circuitbreaker/manager.go`, `pkg/retry/manager.go`)
   - 断路器和重试器管理
   - 配置管理和生命周期
   - 健康检查和指标收集

4. **ResilienceMiddleware** (`pkg/middleware/resilience.go`)
   - Gin 中间件集成
   - 弹性策略组合
   - 降级和错误处理

## ✅ 功能特性

### 1. 断路器功能
- ✅ **三种状态**：关闭（正常）、开启（熔断）、半开（探测）
- ✅ **多种熔断条件**：失败率、连续失败数、慢调用率、最小请求阈值
- ✅ **智能恢复**：半开状态探测和自动恢复
- ✅ **强制控制**：支持强制开启和关闭
- ✅ **统计监控**：详细的统计信息和状态监控

### 2. 重试机制
- ✅ **多种策略**：固定间隔、线性递增、指数退避、随机间隔、自定义策略
- ✅ **智能退避**：支持抖动和最大间隔限制
- ✅ **条件重试**：可配置的重试条件和错误类型
- ✅ **超时控制**：总超时和单次尝试超时
- ✅ **回调支持**：重试过程回调和统计信息

### 3. 弹性中间件
- ✅ **策略组合**：断路器+重试的组合使用
- ✅ **服务降级**：自动降级和自定义降级响应
- ✅ **路径配置**：支持服务级和路径级配置
- ✅ **动态配置**：运行时配置更新和管理
- ✅ **监控集成**：完整的监控和日志记录

### 4. 管理功能
- ✅ **生命周期管理**：创建、更新、删除、重置
- ✅ **健康检查**：定期健康状态检查
- ✅ **指标收集**：自动指标收集和统计
- ✅ **配置管理**：灵活的配置管理和验证

## 🔧 配置说明

### 1. 断路器配置

```go
type Config struct {
    Name                string        // 断路器名称
    MaxRequests         uint32        // 半开状态最大请求数
    Interval            time.Duration // 统计时间窗口
    Timeout             time.Duration // 开启状态超时时间
    FailureThreshold    uint32        // 失败阈值
    SuccessThreshold    uint32        // 成功阈值
    MinRequestThreshold uint32        // 最小请求阈值
    FailureRate         float64       // 失败率阈值
    SlowCallThreshold   time.Duration // 慢调用阈值
    SlowCallRate        float64       // 慢调用率阈值
}
```

### 2. 重试配置

```go
type Config struct {
    Name              string        // 重试器名称
    MaxAttempts       int           // 最大重试次数
    InitialInterval   time.Duration // 初始间隔
    MaxInterval       time.Duration // 最大间隔
    Multiplier        float64       // 乘数（指数退避）
    RandomFactor      float64       // 随机因子
    Strategy          Strategy      // 重试策略
    Timeout           time.Duration // 总超时时间
    PerAttemptTimeout time.Duration // 单次尝试超时
    Jitter            bool          // 是否启用抖动
    RetryableErrors   []string      // 可重试的错误类型
    NonRetryableErrors []string     // 不可重试的错误类型
}
```

### 3. 弹性中间件配置

```go
type ResilienceConfig struct {
    Enabled              bool                      // 是否启用
    EnableCircuitBreaker bool                      // 启用断路器
    EnableRetry          bool                      // 启用重试
    DefaultCBConfig      circuitbreaker.Config     // 默认断路器配置
    DefaultRetryConfig   retry.Config              // 默认重试配置
    ServiceConfigs       map[string]*ServiceConfig // 服务特定配置
    PathConfigs          map[string]*PathConfig    // 路径特定配置
    SkipPaths            []string                  // 跳过的路径
    OnlyPaths            []string                  // 仅处理的路径
    FallbackResponse     *FallbackResponse         // 降级响应
    AddHeaders           bool                      // 添加响应头
    LogRequests          bool                      // 记录请求日志
    LogFailures          bool                      // 记录失败日志
}
```

## 🚀 使用示例

### 1. 基础断路器使用

```go
// 创建断路器配置
config := circuitbreaker.Config{
    Name:                "user-service",
    MaxRequests:         1,
    Interval:            60 * time.Second,
    Timeout:             60 * time.Second,
    FailureThreshold:    5,
    SuccessThreshold:    1,
    MinRequestThreshold: 10,
    FailureRate:         0.5,
}

// 创建断路器
cb := circuitbreaker.NewCircuitBreaker(config, logger)

// 执行函数
result, err := cb.Execute(ctx, func() (interface{}, error) {
    return callUserService()
})

// 带降级的执行
result, err := cb.ExecuteWithFallback(ctx, 
    func() (interface{}, error) {
        return callUserService()
    },
    func() (interface{}, error) {
        return getDefaultUserData(), nil
    },
)
```

### 2. 基础重试使用

```go
// 创建重试配置
config := retry.Config{
    Name:            "api-retry",
    MaxAttempts:     3,
    InitialInterval: 1 * time.Second,
    MaxInterval:     30 * time.Second,
    Multiplier:      2.0,
    Strategy:        retry.StrategyExponential,
    Jitter:          true,
}

// 创建重试器
retrier := retry.NewRetrier(config, logger)

// 执行重试
err := retrier.Execute(ctx, func() error {
    return callExternalAPI()
})

// 带返回值的重试
result, err := retrier.ExecuteWithResult(ctx, func() (interface{}, error) {
    return callExternalAPI()
})
```

### 3. 弹性中间件使用

```go
// 在 API 网关中集成
func setupResilienceMiddleware(logger logger.Logger) *middleware.ResilienceMiddleware {
    // 创建断路器管理器
    cbManager := circuitbreaker.NewManager(
        circuitbreaker.DefaultManagerConfig(), 
        logger,
    )
    
    // 创建重试管理器
    retryManager := retry.NewManager(
        retry.DefaultManagerConfig(), 
        logger,
    )
    
    // 创建弹性中间件
    resilienceMiddleware := middleware.NewResilienceMiddleware(
        cbManager,
        retryManager,
        logger,
        middleware.DefaultResilienceConfig(),
    )
    
    return resilienceMiddleware
}

// 使用中间件
router.Use(resilienceMiddleware.Handler())
```

### 4. 高级配置示例

```go
// 服务特定配置
resilienceConfig := middleware.DefaultResilienceConfig()

// 用户服务配置
resilienceConfig.ServiceConfigs["user-service"] = &middleware.ServiceConfig{
    CircuitBreakerConfig: &circuitbreaker.Config{
        Name:                "user-service-cb",
        FailureThreshold:    3,
        FailureRate:         0.3,
        Timeout:             30 * time.Second,
    },
    RetryConfig: &retry.Config{
        Name:        "user-service-retry",
        MaxAttempts: 2,
        Strategy:    retry.StrategyFixed,
    },
    FallbackResponse: &middleware.FallbackResponse{
        StatusCode: 200,
        Body: map[string]interface{}{
            "code": 0,
            "message": "使用缓存数据",
            "data": []interface{}{},
        },
    },
}

// 路径特定配置
resilienceConfig.PathConfigs["/api/v1/critical"] = &middleware.PathConfig{
    CircuitBreakerConfig: &circuitbreaker.Config{
        Name:                "critical-path-cb",
        FailureThreshold:    1,
        FailureRate:         0.1,
        Timeout:             10 * time.Second,
    },
    RetryConfig: &retry.Config{
        Name:        "critical-path-retry",
        MaxAttempts: 5,
        Strategy:    retry.StrategyExponential,
    },
}
```

## 📊 监控和统计

### 1. 断路器统计

```go
// 获取断路器统计信息
stats := cb.GetStats()
fmt.Printf("断路器状态: %s\n", stats.State)
fmt.Printf("总请求数: %d\n", stats.Counts.Requests)
fmt.Printf("成功数: %d\n", stats.Counts.TotalSuccesses)
fmt.Printf("失败数: %d\n", stats.Counts.TotalFailures)
fmt.Printf("连续失败数: %d\n", stats.Counts.ConsecutiveFailures)
fmt.Printf("慢调用数: %d\n", stats.Counts.SlowCalls)
```

### 2. 重试统计

```go
// 获取重试统计信息
stats := retrier.GetStats()
fmt.Printf("总尝试次数: %d\n", stats.TotalAttempts)
fmt.Printf("成功次数: %d\n", stats.SuccessAttempts)
fmt.Printf("失败次数: %d\n", stats.FailureAttempts)
fmt.Printf("总耗时: %v\n", stats.TotalDuration)
fmt.Printf("平均延迟: %v\n", stats.AverageDelay)
```

### 3. 健康状态检查

```go
// 断路器健康状态
cbHealth := cbManager.GetHealthStatus()
fmt.Printf("总断路器数: %d\n", cbHealth.TotalBreakers)
fmt.Printf("开启状态断路器数: %d\n", cbHealth.OpenBreakers)
fmt.Printf("健康状态: %t\n", cbHealth.Healthy)

// 重试器健康状态
retryHealth := retryManager.GetHealthStatus()
fmt.Printf("总重试器数: %d\n", retryHealth.TotalRetriers)
fmt.Printf("活跃重试器数: %d\n", retryHealth.ActiveRetriers)
```

## 🔍 断路器状态说明

### 状态转换图

```
    [关闭] ──失败率达到阈值──> [开启]
       ↑                        ↓
       │                    超时后自动
   成功数达到阈值                ↓
       │                    [半开]
       └──────────────────────┘
```

### 状态详解

1. **关闭状态（CLOSED）**
   - 正常状态，所有请求正常通过
   - 统计失败率和慢调用率
   - 达到熔断条件时转为开启状态

2. **开启状态（OPEN）**
   - 熔断状态，所有请求被拒绝
   - 执行降级逻辑或返回错误
   - 超时后自动转为半开状态

3. **半开状态（HALF_OPEN）**
   - 探测状态，允许少量请求通过
   - 根据请求结果决定下一步状态
   - 成功则转为关闭，失败则转为开启

## 🔄 重试策略说明

### 1. 固定间隔（Fixed）
```
尝试1 ──1s──> 尝试2 ──1s──> 尝试3
```

### 2. 线性递增（Linear）
```
尝试1 ──1s──> 尝试2 ──2s──> 尝试3 ──3s──> 尝试4
```

### 3. 指数退避（Exponential）
```
尝试1 ──1s──> 尝试2 ──2s──> 尝试3 ──4s──> 尝试4 ──8s──> 尝试5
```

### 4. 随机间隔（Random）
```
尝试1 ──随机──> 尝试2 ──随机──> 尝试3
```

### 5. 自定义策略（Custom）
```go
customStrategy := func(attempt int, config retry.Config) time.Duration {
    // 自定义逻辑
    return time.Duration(attempt*attempt) * time.Second
}
```

## 🧪 测试

### 运行单元测试
```bash
# 断路器测试
go test ./pkg/circuitbreaker -v

# 重试器测试
go test ./pkg/retry -v
```

### 集成测试
```bash
# 启动 API 网关
go run cmd/api-gateway/main.go

# 测试断路器功能
for i in {1..10}; do
  curl -H "X-Service-Name: test-service" \
       http://localhost:8080/api/v1/test
done

# 测试重试功能
curl -H "X-Service-Name: retry-service" \
     http://localhost:8080/api/v1/retry-test
```

## 🔍 故障排除

### 常见问题

1. **断路器不生效**
   - 检查最小请求阈值设置
   - 确认失败率计算正确
   - 验证时间窗口配置

2. **重试次数过多**
   - 检查重试条件配置
   - 确认错误类型匹配
   - 验证超时设置

3. **降级不触发**
   - 检查降级配置
   - 确认断路器状态
   - 验证降级条件

### 调试技巧

1. **启用详细日志**：
   ```go
   config.LogRequests = true
   config.LogFailures = true
   ```

2. **监控统计信息**：
   ```go
   // 定期输出统计信息
   go func() {
       ticker := time.NewTicker(30 * time.Second)
       for range ticker.C {
           stats := cb.GetStats()
           log.Printf("断路器统计: %+v", stats)
       }
   }()
   ```

3. **健康检查**：
   ```go
   // 检查系统健康状态
   health := cbManager.GetHealthStatus()
   if !health.Healthy {
       log.Printf("断路器健康检查失败: %+v", health)
   }
   ```

## 📈 性能优化

1. **配置优化**：
   - 合理设置失败阈值和时间窗口
   - 避免过于频繁的重试
   - 使用适当的超时时间

2. **资源管理**：
   - 定期清理不活跃的断路器和重试器
   - 控制最大实例数量
   - 优化内存使用

3. **监控集成**：
   - 集成到监控系统
   - 设置告警规则
   - 定期分析统计数据

## 🔒 安全考虑

1. **资源保护**：防止断路器和重试器资源滥用
2. **配置验证**：严格验证配置参数
3. **访问控制**：控制管理接口的访问权限
4. **审计日志**：记录所有配置变更和状态转换

## 🚀 扩展功能

### 计划中的功能
- [ ] **分布式断路器**：跨实例的断路器状态同步
- [ ] **智能学习**：基于历史数据的自适应配置
- [ ] **可视化界面**：断路器和重试器的可视化管理
- [ ] **A/B 测试**：基于弹性组件的 A/B 测试支持

### 扩展策略

1. **插件化架构**：支持自定义断路器和重试策略
2. **云原生集成**：与 Kubernetes、Istio 等的集成
3. **多语言支持**：提供多语言客户端库
4. **标准化**：遵循行业标准和最佳实践

---

**注意**：断路器和重试机制是现代微服务架构的重要组成部分，能够显著提升系统的可靠性和用户体验。在使用时，请根据实际业务场景合理配置参数，避免过度保护影响正常业务流程。
