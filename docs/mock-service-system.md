# Mock 服务系统

## 📋 概述

本文档介绍了 PaaS 平台 API 网关中新实现的 Mock 服务系统。该系统提供了完整的 API Mock 能力，包括静态响应、动态数据生成、条件匹配、场景模拟等功能，支持前端开发、API 测试和系统集成测试，大大提升了开发效率和测试覆盖率。

## 🏗️ 系统架构

```
客户端请求 → Mock 中间件 → 规则匹配 → 响应生成 → 返回 Mock 响应
     ↓           ↓          ↓         ↓
[Mock管理器] [条件引擎] [数据生成器] [响应生成器]
```

### 核心组件

1. **MockManager** (`pkg/mock/mock_manager.go`)
   - Mock 规则管理和匹配
   - 条件评估和场景选择
   - 规则生命周期管理

2. **ResponseGenerator** (`pkg/mock/response_generator.go`)
   - 多种响应类型生成
   - 模板渲染和动态数据
   - 延迟和代理处理

3. **DataFaker** (`pkg/mock/faker.go`)
   - 智能数据生成器
   - 多种数据类型支持
   - 中文本地化数据

4. **MockMiddleware** (`pkg/middleware/mock.go`)
   - <PERSON><PERSON> 中间件集成
   - 请求拦截和处理
   - 错误处理和日志记录

5. **ConfigLoader** (`pkg/mock/config_loader.go`)
   - Mock 配置加载和验证
   - 文件监控和热更新
   - 配置导入导出

## ✅ 功能特性

### 1. Mock 规则管理
- ✅ **规则注册**：支持动态注册和更新 Mock 规则
- ✅ **路径匹配**：支持精确匹配、通配符、路径参数匹配
- ✅ **方法匹配**：支持所有 HTTP 方法和通配符匹配
- ✅ **优先级控制**：支持规则优先级排序和选择
- ✅ **启用控制**：支持规则的启用和禁用管理

### 2. 条件匹配系统
- ✅ **多种条件类型**：请求头、查询参数、请求体、路径、方法、User-Agent、IP、时间、随机
- ✅ **丰富操作符**：等于、不等于、包含、开始于、结束于、正则匹配、范围匹配等
- ✅ **复合条件**：支持多个条件的逻辑组合
- ✅ **大小写控制**：支持大小写敏感和不敏感匹配

### 3. 响应生成能力
- ✅ **多种响应类型**：JSON、XML、HTML、文本、二进制、模板、文件、代理
- ✅ **静态响应**：预定义的固定响应内容
- ✅ **动态响应**：基于请求参数的动态响应生成
- ✅ **模板响应**：支持 Go template 语法的响应模板
- ✅ **结构化响应**：基于 JSON Schema 的结构化数据生成

### 4. 数据生成器（Faker）
- ✅ **基础数据类型**：字符串、数字、布尔值、日期时间
- ✅ **身份信息**：姓名、邮箱、电话、身份证号
- ✅ **地址信息**：地址、城市、国家、邮编
- ✅ **公司信息**：公司名称、职位、部门
- ✅ **网络信息**：URL、域名、IP地址、MAC地址
- ✅ **中文本地化**：支持中文姓名、地址、公司等数据

### 5. 场景模拟系统
- ✅ **多场景支持**：一个规则支持多个响应场景
- ✅ **概率控制**：基于概率的场景触发
- ✅ **权重分配**：基于权重的场景选择
- ✅ **条件场景**：基于条件的场景匹配

### 6. 高级功能
- ✅ **延迟模拟**：固定延迟、随机延迟、正态分布延迟
- ✅ **代理模式**：Mock 失败时的代理降级
- ✅ **缓存支持**：响应缓存和性能优化
- ✅ **统计监控**：Mock 使用统计和性能监控

## 🔧 配置说明

### 1. Mock 规则配置

Mock 规则使用 JSON 格式配置，存放在 `configs/mocks/` 目录：

```json
{
  "id": "user-list-mock",
  "name": "用户列表 Mock",
  "description": "模拟用户列表API响应",
  "path": "/api/v1/users",
  "method": "GET",
  "enabled": true,
  "priority": 100,
  "conditions": [
    {
      "type": "query",
      "field": "page",
      "operator": "exists"
    }
  ],
  "response": {
    "status_code": 200,
    "headers": {
      "Content-Type": "application/json"
    },
    "body_type": "schema",
    "schema": {
      "type": "object",
      "properties": {
        "code": {"type": "integer", "example": 0},
        "message": {"type": "string", "example": "success"},
        "data": {
          "type": "array",
          "length": 10,
          "items": {
            "type": "object",
            "properties": {
              "id": {"type": "integer", "faker": "id"},
              "name": {"type": "string", "faker": "name"},
              "email": {"type": "string", "faker": "email"}
            }
          }
        }
      }
    }
  },
  "delay": {
    "type": "random",
    "min": 100,
    "max": 500
  },
  "scenarios": [
    {
      "name": "empty_result",
      "description": "空结果场景",
      "enabled": true,
      "probability": 0.1,
      "conditions": [
        {
          "type": "query",
          "field": "search",
          "operator": "equals",
          "value": "empty"
        }
      ],
      "response": {
        "status_code": 200,
        "body": {
          "code": 0,
          "message": "success",
          "data": []
        }
      }
    }
  ],
  "tags": ["user", "list"],
  "metadata": {
    "version": "1.0",
    "category": "user-management"
  }
}
```

### 2. Mock 中间件配置

```go
// 在 cmd/api-gateway/main.go 中
func setupMockMiddleware(logger logger.Logger) *middleware.MockMiddleware {
    // Mock 服务配置
    mockConfig := mock.DevelopmentMockConfig()
    mockManager := mock.NewMockManager(mockConfig, logger)
    
    // 加载 Mock 规则
    configLoader := mock.NewConfigLoader("configs/mocks", logger)
    configLoader.LoadAndApplyRules(mockManager)
    
    // 中间件配置
    middlewareConfig := middleware.DevelopmentMockMiddlewareConfig()
    return middleware.NewMockMiddleware(mockManager, logger, middlewareConfig)
}
```

## 🚀 使用示例

### 1. 基础 Mock 使用

```bash
# 启用 Mock 的请求
curl -H "X-Mock-Enable: true" http://localhost:8080/api/v1/users

# 响应示例
HTTP/1.1 200 OK
X-Mock-Matched: true
X-Mock-Rule-ID: user-list-mock
X-Mock-Rule-Name: 用户列表 Mock
X-Mock-Source: schema
Content-Type: application/json

{
  "code": 0,
  "message": "success",
  "data": [
    {
      "id": 12345,
      "name": "张三",
      "email": "<EMAIL>",
      "phone": "13812345678",
      "company": "阿里巴巴集团",
      "created_at": "2023-12-01T10:30:00Z"
    }
  ]
}
```

### 2. 条件 Mock 示例

```bash
# 触发空结果场景
curl -H "X-Mock-Enable: true" \
     "http://localhost:8080/api/v1/users?search=empty"

# 响应
{
  "code": 0,
  "message": "success",
  "data": []
}

# 触发错误场景
curl -H "X-Mock-Enable: true" \
     "http://localhost:8080/api/v1/users?error=true"

# 响应
HTTP/1.1 500 Internal Server Error
{
  "code": -1,
  "message": "内部服务器错误",
  "error": "数据库连接失败"
}
```

### 3. 模板响应示例

```json
{
  "response": {
    "body_type": "template",
    "template": "{\n  \"code\": 0,\n  \"message\": \"用户创建成功\",\n  \"data\": {\n    \"id\": {{faker.id}},\n    \"name\": \"{{request.body.name}}\",\n    \"email\": \"{{request.body.email}}\",\n    \"created_at\": \"{{time.now}}\"\n  }\n}"
  }
}
```

### 4. 动态数据生成示例

```json
{
  "response": {
    "body_type": "schema",
    "schema": {
      "type": "object",
      "properties": {
        "users": {
          "type": "array",
          "length": 5,
          "items": {
            "type": "object",
            "properties": {
              "id": {"faker": "id"},
              "name": {"faker": "name"},
              "email": {"faker": "email"},
              "phone": {"faker": "phone"},
              "company": {"faker": "company"},
              "address": {"faker": "address"},
              "created_at": {"faker": "datetime"}
            }
          }
        }
      }
    }
  }
}
```

## 📊 支持的数据类型

### Faker 数据类型

| 类型 | 描述 | 示例 |
|------|------|------|
| `name` | 中文姓名 | 张三 |
| `email` | 邮箱地址 | <EMAIL> |
| `phone` | 手机号码 | 13812345678 |
| `company` | 公司名称 | 阿里巴巴集团 |
| `address` | 详细地址 | 北京市朝阳区建国门外大街1号 |
| `city` | 城市名称 | 北京 |
| `job_title` | 职位名称 | 软件工程师 |
| `department` | 部门名称 | 技术部 |
| `url` | 网址 | https://example.com/api |
| `ip` | IP地址 | ************* |
| `uuid` | UUID | 550e8400-e29b-41d4-a716-************ |
| `date` | 日期 | 2023-12-01 |
| `datetime` | 日期时间 | 2023-12-01T10:30:00Z |
| `timestamp` | 时间戳 | 1701423000 |
| `sentence` | 句子 | 这是一个测试句子。 |
| `paragraph` | 段落 | 多个句子组成的段落 |
| `price` | 价格 | 99.99 |
| `percentage` | 百分比 | 85.5 |

### 响应体类型

| 类型 | 描述 | 用途 |
|------|------|------|
| `json` | JSON 响应 | API 数据响应 |
| `xml` | XML 响应 | XML 格式数据 |
| `html` | HTML 响应 | 网页内容 |
| `text` | 纯文本响应 | 简单文本内容 |
| `template` | 模板响应 | 动态模板渲染 |
| `schema` | 结构化响应 | 基于 Schema 生成 |
| `file` | 文件响应 | 静态文件内容 |
| `proxy` | 代理响应 | 代理到真实服务 |

## 🔍 条件匹配系统

### 条件类型

| 类型 | 描述 | 示例 |
|------|------|------|
| `header` | 请求头匹配 | Authorization: Bearer token |
| `query` | 查询参数匹配 | ?page=1&size=10 |
| `body` | 请求体匹配 | {"name": "test"} |
| `path` | 路径匹配 | /api/v1/users |
| `method` | HTTP 方法匹配 | GET, POST |
| `user_agent` | User-Agent 匹配 | Mozilla/5.0... |
| `ip` | IP 地址匹配 | ************* |
| `time` | 时间匹配 | 09:00:00-18:00:00 |
| `random` | 随机条件 | 概率匹配 |

### 操作符

| 操作符 | 描述 | 示例 |
|--------|------|------|
| `equals` | 等于 | value == "test" |
| `not_equals` | 不等于 | value != "test" |
| `contains` | 包含 | value.contains("test") |
| `not_contains` | 不包含 | !value.contains("test") |
| `starts_with` | 开始于 | value.startsWith("test") |
| `ends_with` | 结束于 | value.endsWith("test") |
| `regex` | 正则匹配 | value.matches("\\d+") |
| `in` | 在列表中 | value in ["a", "b", "c"] |
| `not_in` | 不在列表中 | value not in ["a", "b"] |
| `greater` | 大于 | value > 10 |
| `less` | 小于 | value < 10 |
| `exists` | 存在 | value != null |
| `not_exists` | 不存在 | value == null |

## 🧪 测试

### 运行单元测试
```bash
go test ./pkg/mock -v
```

### 集成测试
```bash
# 启动 API 网关
go run cmd/api-gateway/main.go

# 测试基础 Mock
curl -H "X-Mock-Enable: true" http://localhost:8080/api/v1/users

# 测试条件 Mock
curl -H "X-Mock-Enable: true" \
     "http://localhost:8080/api/v1/users?search=empty"

# 测试场景 Mock
curl -H "X-Mock-Enable: true" \
     "http://localhost:8080/api/v1/users?error=true"
```

## 🔍 监控和调试

### Mock 使用统计
```bash
# 查看 Mock 统计（需要实现管理接口）
curl http://localhost:8080/api/v1/mock/stats

# 查看特定规则统计
curl http://localhost:8080/api/v1/mock/rules/user-list-mock/stats
```

### 日志监控
```bash
# 查看 Mock 日志
tail -f logs/api-gateway.log | grep "Mock"

# 查看规则匹配日志
tail -f logs/api-gateway.log | grep "找到匹配的 Mock 规则"
```

## 🛠️ 故障排除

### 常见问题

1. **Mock 不生效**
   - 检查 Mock 中间件是否启用
   - 确认请求头 `X-Mock-Enable: true`
   - 验证规则路径和方法匹配

2. **规则不匹配**
   - 检查规则优先级设置
   - 确认条件配置正确
   - 查看规则启用状态

3. **数据生成异常**
   - 检查 Schema 配置语法
   - 确认 Faker 类型支持
   - 验证模板语法正确

### 调试技巧

1. **启用详细日志**：
   ```yaml
   mock:
     log_requests: true
     log_responses: true
   ```

2. **检查规则配置**：
   ```bash
   # 验证 JSON 配置语法
   cat configs/mocks/user-api-mocks.json | jq .
   ```

3. **测试规则匹配**：
   ```bash
   # 使用详细响应头查看匹配信息
   curl -v -H "X-Mock-Enable: true" http://localhost:8080/api/v1/users
   ```

## 📈 性能优化

1. **规则缓存**：Mock 规则会被缓存在内存中
2. **响应缓存**：支持可选的响应缓存
3. **延迟优化**：智能延迟计算和应用
4. **批量处理**：支持批量规则操作

## 🔒 安全考虑

1. **访问控制**：Mock 功能的访问权限控制
2. **数据安全**：生成数据的安全性保证
3. **审计日志**：记录所有 Mock 操作
4. **资源限制**：防止 Mock 服务资源滥用

## 🚀 扩展功能

### 计划中的功能
- [ ] **Mock 管理界面**：Web 界面管理 Mock 规则
- [ ] **规则导入导出**：支持规则的批量导入导出
- [ ] **A/B 测试**：基于 Mock 的 A/B 测试支持
- [ ] **性能测试**：Mock 数据的性能测试支持
- [ ] **智能推荐**：基于 API 文档的规则推荐

### Mock 演进策略

1. **渐进式 Mock**：从简单到复杂的 Mock 策略
2. **数据驱动**：基于真实数据的 Mock 生成
3. **智能匹配**：AI 辅助的规则匹配
4. **云端同步**：Mock 规则的云端同步

---

**注意**：Mock 服务系统是现代 API 开发的重要工具，能够显著提升开发效率和测试质量。在生产环境中使用时，请合理配置 Mock 规则，确保不会影响正常的业务流程。
