# WebSocket 网关

## 📋 概述

本文档介绍了 PaaS 平台 API 网关中新实现的 WebSocket 网关功能。该系统提供了完整的 WebSocket 协议支持，包括连接管理、消息路由、房间系统、用户管理、负载均衡和实时监控功能，能够支持大规模的实时通信应用，如聊天系统、实时协作、游戏、IoT 数据推送等场景。

## 🏗️ 系统架构

```
WebSocket 客户端 → WebSocket 网关 → 连接管理 → 消息路由 → 房间系统
       ↓              ↓           ↓         ↓         ↓
   [升级处理]    [连接池管理]  [消息分发]  [房间管理]  [用户管理]
       ↓              ↓           ↓         ↓         ↓
   [认证授权]    [心跳检测]    [广播系统]  [权限控制]  [在线状态]
```

### 核心组件

1. **WebSocketGateway** (`pkg/websocket/websocket.go`)
   - WebSocket 网关核心实现
   - 连接生命周期管理
   - 消息路由和分发

2. **Connection** (`pkg/websocket/websocket.go`)
   - WebSocket 连接抽象
   - 消息处理和状态管理
   - 心跳检测和超时处理

3. **Room** (`pkg/websocket/websocket.go`)
   - 房间系统实现
   - 多用户群组通信
   - 房间权限和大小控制

4. **WebSocketMiddleware** (`pkg/middleware/websocket.go`)
   - Gin 中间件集成
   - 请求升级和认证
   - CORS 和安全控制

5. **APIHandler** (`pkg/websocket/api.go`)
   - RESTful 管理 API
   - 连接和房间管理
   - 统计信息和监控

## ✅ 功能特性

### 1. 连接管理
- ✅ **自动升级**：HTTP 到 WebSocket 的自动协议升级
- ✅ **连接池**：高效的连接池管理和资源控制
- ✅ **心跳检测**：自动心跳检测和连接状态监控
- ✅ **超时处理**：连接超时自动清理和重连机制
- ✅ **负载控制**：最大连接数限制和负载均衡

### 2. 消息系统
- ✅ **多种消息类型**：文本、二进制、控制消息支持
- ✅ **消息路由**：点对点、广播、房间消息路由
- ✅ **消息队列**：异步消息处理和缓冲机制
- ✅ **消息确认**：消息送达确认和重试机制
- ✅ **消息过滤**：基于条件的消息过滤和转发

### 3. 房间系统
- ✅ **动态房间**：动态创建和销毁房间
- ✅ **房间权限**：公开/私有房间和权限控制
- ✅ **房间大小**：房间人数限制和管理
- ✅ **房间元数据**：自定义房间属性和配置
- ✅ **房间事件**：加入/离开/消息事件处理

### 4. 用户管理
- ✅ **用户识别**：基于用户ID和会话ID的用户识别
- ✅ **在线状态**：实时在线状态跟踪和查询
- ✅ **多连接**：单用户多连接支持和管理
- ✅ **用户元数据**：自定义用户属性和状态
- ✅ **用户事件**：上线/下线/状态变更事件

### 5. 安全功能
- ✅ **来源检查**：CORS 和来源验证
- ✅ **认证授权**：基于 Token 的认证和授权
- ✅ **访问控制**：基于角色的访问控制
- ✅ **消息验证**：消息格式验证和安全检查
- ✅ **速率限制**：连接和消息速率限制

### 6. 监控统计
- ✅ **实时统计**：连接数、消息数、房间数统计
- ✅ **性能监控**：延迟、吞吐量、错误率监控
- ✅ **健康检查**：系统健康状态检查和报告
- ✅ **日志记录**：详细的连接和消息日志
- ✅ **指标导出**：Prometheus 格式指标导出

## 🔧 配置说明

### 1. WebSocket 网关配置

```go
type Config struct {
    ReadBufferSize    int           // 读缓冲区大小
    WriteBufferSize   int           // 写缓冲区大小
    HandshakeTimeout  time.Duration // 握手超时时间
    ReadTimeout       time.Duration // 读超时时间
    WriteTimeout      time.Duration // 写超时时间
    PingInterval      time.Duration // Ping 间隔
    PongTimeout       time.Duration // Pong 超时时间
    MaxMessageSize    int64         // 最大消息大小
    MaxConnections    int           // 最大连接数
    EnableCompression bool          // 启用压缩
    CheckOrigin       bool          // 检查来源
    AllowedOrigins    []string      // 允许的来源
    EnableAuth        bool          // 启用认证
    AuthTimeout       time.Duration // 认证超时
    EnableRooms       bool          // 启用房间
    MaxRooms          int           // 最大房间数
    MaxRoomSize       int           // 最大房间大小
    EnableMetrics     bool          // 启用指标
    MetricsInterval   time.Duration // 指标收集间隔
}
```

### 2. 中间件配置

```go
type WebSocketMiddlewareConfig struct {
    Enabled         bool     // 是否启用
    Path            string   // WebSocket 路径
    SkipPaths       []string // 跳过的路径
    OnlyPaths       []string // 仅处理的路径
    RequireAuth     bool     // 是否需要认证
    AuthHeader      string   // 认证头名称
    UserIDHeader    string   // 用户ID头名称
    SessionIDHeader string   // 会话ID头名称
    LogConnections  bool     // 记录连接日志
    LogMessages     bool     // 记录消息日志
    AddHeaders      bool     // 添加响应头
    CorsEnabled     bool     // 启用CORS
    CorsOrigins     []string // CORS允许的来源
}
```

## 🚀 使用示例

### 1. 基础 WebSocket 连接

```javascript
// 客户端连接
const ws = new WebSocket('ws://localhost:8080/ws', [], {
    headers: {
        'X-User-ID': 'user123',
        'X-Session-ID': 'session456',
        'Authorization': 'Bearer your-token'
    }
});

ws.onopen = function(event) {
    console.log('WebSocket 连接已建立');
    
    // 发送 Ping 消息
    ws.send(JSON.stringify({
        type: 'ping',
        data: 'hello'
    }));
};

ws.onmessage = function(event) {
    const message = JSON.parse(event.data);
    console.log('收到消息:', message);
    
    switch(message.type) {
        case 'pong':
            console.log('收到 Pong 响应');
            break;
        case 'room_message':
            console.log('房间消息:', message.data);
            break;
        case 'private_message':
            console.log('私聊消息:', message.data);
            break;
    }
};

ws.onerror = function(error) {
    console.error('WebSocket 错误:', error);
};

ws.onclose = function(event) {
    console.log('WebSocket 连接已关闭');
};
```

### 2. 房间操作

```javascript
// 加入房间
ws.send(JSON.stringify({
    type: 'join_room',
    data: 'chat-room-1'
}));

// 发送房间消息
ws.send(JSON.stringify({
    type: 'room_message',
    room: 'chat-room-1',
    data: {
        text: 'Hello, everyone!',
        timestamp: new Date().toISOString()
    }
}));

// 离开房间
ws.send(JSON.stringify({
    type: 'leave_room',
    data: 'chat-room-1'
}));
```

### 3. 私聊消息

```javascript
// 发送私聊消息
ws.send(JSON.stringify({
    type: 'private_message',
    to: 'user456',
    data: {
        text: 'Hello, this is a private message!',
        timestamp: new Date().toISOString()
    }
}));
```

### 4. 服务端 API 使用

```bash
# 获取 WebSocket 统计信息
curl http://localhost:8080/api/v1/websocket/stats

# 获取所有连接
curl http://localhost:8080/api/v1/websocket/connections

# 获取所有房间
curl http://localhost:8080/api/v1/websocket/rooms

# 创建房间
curl -X POST http://localhost:8080/api/v1/websocket/rooms \
  -H "Content-Type: application/json" \
  -d '{
    "name": "聊天室1",
    "description": "这是一个测试聊天室",
    "max_size": 50,
    "is_private": false,
    "owner": "admin"
  }'

# 广播消息
curl -X POST http://localhost:8080/api/v1/websocket/broadcast \
  -H "Content-Type: application/json" \
  -d '{
    "type": "announcement",
    "data": {
      "title": "系统公告",
      "content": "系统将在今晚进行维护",
      "level": "info"
    }
  }'

# 发送用户消息
curl -X POST http://localhost:8080/api/v1/websocket/users/user123/send \
  -H "Content-Type: application/json" \
  -d '{
    "type": "notification",
    "data": {
      "title": "新消息",
      "content": "您有一条新的私信"
    }
  }'

# 发送房间消息
curl -X POST http://localhost:8080/api/v1/websocket/rooms/chat-room-1/send \
  -H "Content-Type: application/json" \
  -d '{
    "type": "system_message",
    "data": {
      "content": "欢迎新用户加入聊天室！"
    }
  }'
```

## 📊 消息格式

### 1. 标准消息格式

```json
{
  "id": "msg-uuid-123",
  "type": "message_type",
  "from": "sender_id",
  "to": "receiver_id",
  "room": "room_id",
  "data": {
    "content": "消息内容",
    "timestamp": "2023-12-01T10:30:00Z"
  },
  "metadata": {
    "priority": "high",
    "category": "chat"
  },
  "timestamp": "2023-12-01T10:30:00Z"
}
```

### 2. 消息类型

| 类型 | 描述 | 用途 |
|------|------|------|
| `ping` | Ping 消息 | 心跳检测 |
| `pong` | Pong 响应 | 心跳响应 |
| `join_room` | 加入房间 | 房间操作 |
| `leave_room` | 离开房间 | 房间操作 |
| `room_message` | 房间消息 | 群组通信 |
| `private_message` | 私聊消息 | 点对点通信 |
| `broadcast` | 广播消息 | 全局通信 |
| `notification` | 通知消息 | 系统通知 |
| `error` | 错误消息 | 错误处理 |
| `success` | 成功消息 | 操作确认 |

### 3. 系统事件

```json
// 用户上线事件
{
  "type": "user_online",
  "data": {
    "user_id": "user123",
    "timestamp": "2023-12-01T10:30:00Z"
  }
}

// 用户下线事件
{
  "type": "user_offline",
  "data": {
    "user_id": "user123",
    "timestamp": "2023-12-01T10:35:00Z"
  }
}

// 房间创建事件
{
  "type": "room_created",
  "data": {
    "room_id": "chat-room-1",
    "name": "聊天室1",
    "owner": "admin"
  }
}

// 房间删除事件
{
  "type": "room_deleted",
  "data": {
    "room_id": "chat-room-1",
    "reason": "房间已被管理员删除"
  }
}
```

## 📈 监控和统计

### 1. 统计信息

```json
{
  "total_connections": 1500,
  "active_connections": 1200,
  "total_messages": 50000,
  "messages_sent": 25000,
  "messages_received": 25000,
  "total_rooms": 50,
  "active_rooms": 35,
  "bytes_sent": 1048576,
  "bytes_received": 2097152,
  "connection_errors": 10,
  "message_errors": 5,
  "last_updated": "2023-12-01T10:30:00Z",
  "average_latency": 15.5,
  "peak_connections": 1800,
  "connections_per_second": 2.5
}
```

### 2. 健康检查

```bash
# WebSocket 健康检查
curl http://localhost:8080/ws/health

# 响应示例
{
  "status": "healthy",
  "websocket": {
    "active_connections": 1200,
    "active_rooms": 35,
    "total_messages": 50000,
    "last_updated": "2023-12-01T10:30:00Z"
  }
}
```

### 3. 性能指标

| 指标 | 描述 | 正常范围 |
|------|------|----------|
| 连接数 | 当前活跃连接数 | < 最大连接数的80% |
| 消息延迟 | 消息处理平均延迟 | < 100ms |
| 错误率 | 连接和消息错误率 | < 1% |
| 内存使用 | WebSocket 服务内存使用 | < 系统内存的70% |
| CPU 使用 | WebSocket 服务 CPU 使用 | < 80% |

## 🧪 测试

### 运行单元测试
```bash
go test ./pkg/websocket -v
```

### 集成测试
```bash
# 启动 API 网关
go run cmd/api-gateway/main.go

# 测试 WebSocket 连接
wscat -c ws://localhost:8080/ws

# 测试房间功能
wscat -c ws://localhost:8080/ws -H "X-User-ID: user123"
> {"type": "join_room", "data": "test-room"}
> {"type": "room_message", "room": "test-room", "data": "Hello, room!"}

# 测试 API 接口
curl http://localhost:8080/api/v1/websocket/stats
curl http://localhost:8080/api/v1/websocket/connections
curl http://localhost:8080/api/v1/websocket/rooms
```

## 🔍 故障排除

### 常见问题

1. **连接失败**
   - 检查 WebSocket 路径配置
   - 确认来源检查设置
   - 验证认证配置

2. **消息丢失**
   - 检查消息缓冲区大小
   - 确认连接状态
   - 验证消息格式

3. **房间操作失败**
   - 检查房间权限设置
   - 确认房间大小限制
   - 验证用户状态

### 调试技巧

1. **启用详细日志**：
   ```go
   config.LogConnections = true
   config.LogMessages = true
   ```

2. **监控连接状态**：
   ```bash
   # 查看连接统计
   curl http://localhost:8080/api/v1/websocket/stats
   
   # 查看特定连接
   curl http://localhost:8080/api/v1/websocket/connections/{connection_id}
   ```

3. **检查系统资源**：
   ```bash
   # 检查内存使用
   ps aux | grep api-gateway
   
   # 检查网络连接
   netstat -an | grep :8080
   ```

## 📈 性能优化

1. **连接池优化**：
   - 合理设置最大连接数
   - 优化心跳检测间隔
   - 及时清理死连接

2. **消息处理优化**：
   - 使用消息缓冲区
   - 批量处理消息
   - 异步消息分发

3. **内存管理**：
   - 定期清理过期数据
   - 优化数据结构
   - 控制消息大小

## 🔒 安全考虑

1. **认证授权**：强制认证和基于角色的访问控制
2. **来源验证**：严格的 CORS 和来源检查
3. **消息验证**：输入验证和消息格式检查
4. **速率限制**：连接和消息速率限制
5. **审计日志**：记录所有连接和消息操作

## 🚀 扩展功能

### 计划中的功能
- [ ] **集群支持**：多实例 WebSocket 集群和负载均衡
- [ ] **消息持久化**：离线消息存储和推送
- [ ] **插件系统**：自定义消息处理插件
- [ ] **协议扩展**：支持自定义 WebSocket 子协议

### 扩展策略

1. **水平扩展**：支持多实例部署和负载均衡
2. **消息队列**：集成 Redis/RabbitMQ 等消息队列
3. **数据持久化**：集成数据库存储离线消息
4. **监控集成**：集成 Prometheus/Grafana 监控

---

**注意**：WebSocket 网关是现代实时应用的重要基础设施，能够支持大规模的实时通信需求。在生产环境中使用时，请根据实际业务场景合理配置参数，确保系统的稳定性和性能。
