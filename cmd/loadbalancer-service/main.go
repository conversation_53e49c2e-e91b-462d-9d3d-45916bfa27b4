package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	"paas-platform/internal/loadbalancer"
	"paas-platform/pkg/logger"
)

func main() {
	// 初始化配置
	initConfig()

	// 初始化日志
	log := logger.NewLogger(logger.Config{
		Level:  viper.GetString("logging.level"),
		Format: viper.GetString("logging.format"),
		Output: viper.GetString("logging.output"),
	})

	log.Info("启动负载均衡服务",
		"version", "1.0.0",
		"port", viper.GetInt("server.port"))

	// 初始化服务注册中心
	registry := loadbalancer.NewServiceRegistry(log)
	registry.Start()

	// 初始化负载均衡器
	lb := loadbalancer.NewLoadBalancer(registry, log)

	// 初始化健康检查器
	healthChecker := loadbalancer.NewHealthChecker(registry, log)
	healthChecker.Start()

	// 初始化处理器
	handler := loadbalancer.NewHandler(registry, lb, healthChecker, log)

	// 设置路由
	router := setupRouter(handler, log)

	// 启动服务器
	srv := &http.Server{
		Addr:         fmt.Sprintf(":%d", viper.GetInt("server.port")),
		Handler:      router,
		ReadTimeout:  viper.GetDuration("server.read_timeout"),
		WriteTimeout: viper.GetDuration("server.write_timeout"),
	}

	// 启动服务器
	go func() {
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Error("服务器启动失败", "error", err)
		}
	}()

	log.Info("负载均衡服务启动成功", "port", viper.GetInt("server.port"))

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Info("正在关闭负载均衡服务...")

	// 优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 停止健康检查器
	healthChecker.Stop()

	// 停止服务注册中心
	registry.Stop()

	// 关闭HTTP服务器
	if err := srv.Shutdown(ctx); err != nil {
		log.Error("服务器关闭失败", "error", err)
	}

	log.Info("负载均衡服务已关闭")
}

// initConfig 初始化配置
func initConfig() {
	viper.SetConfigName("loadbalancer")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("./configs")
	viper.AddConfigPath(".")

	// 设置默认值
	viper.SetDefault("server.port", 8086)
	viper.SetDefault("server.host", "0.0.0.0")
	viper.SetDefault("server.read_timeout", "30s")
	viper.SetDefault("server.write_timeout", "30s")
	viper.SetDefault("logging.level", "info")
	viper.SetDefault("logging.format", "json")
	viper.SetDefault("logging.output", "stdout")

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		fmt.Printf("警告: 无法读取配置文件: %v\n", err)
		fmt.Println("使用默认配置")
	}

	// 读取环境变量
	viper.AutomaticEnv()
}

// setupRouter 设置路由
func setupRouter(handler *loadbalancer.Handler, logger logger.Logger) *gin.Engine {
	// 设置 Gin 模式
	if viper.GetString("logging.level") == "debug" {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()

	// 添加中间件
	router.Use(gin.Recovery())
	router.Use(corsMiddleware())
	router.Use(loggingMiddleware(logger))

	// 健康检查端点
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "healthy",
			"service":   "loadbalancer",
			"timestamp": time.Now().Format(time.RFC3339),
		})
	})

	// API 路由组
	api := router.Group("/api/v1")
	{
		// 负载均衡器路由
		lbGroup := api.Group("/loadbalancer")
		handler.RegisterRoutes(lbGroup)
	}

	return router
}

// corsMiddleware CORS 中间件
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// loggingMiddleware 日志中间件
func loggingMiddleware(logger logger.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery

		// 处理请求
		c.Next()

		// 记录日志
		latency := time.Since(start)
		clientIP := c.ClientIP()
		method := c.Request.Method
		statusCode := c.Writer.Status()

		if raw != "" {
			path = path + "?" + raw
		}

		logger.Info("HTTP请求",
			"method", method,
			"path", path,
			"status", statusCode,
			"latency", latency,
			"client_ip", clientIP,
			"user_agent", c.Request.UserAgent(),
		)
	}
}
