package main

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"strings"
	"syscall"
	"time"

	"paas-platform/internal/auth"
	"paas-platform/internal/database"
	"paas-platform/pkg/logger"
	"paas-platform/pkg/middleware"
	"paas-platform/pkg/circuitbreaker"
	"paas-platform/pkg/mock"
	"paas-platform/pkg/retry"
	"paas-platform/pkg/transformer"
	"paas-platform/pkg/validator"
	"paas-platform/pkg/version"
	"paas-platform/pkg/websocket"
	"paas-platform/pkg/graphql"

	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	"github.com/swaggo/files"
	"github.com/swaggo/gin-swagger"

	_ "paas-platform/docs" // 导入生成的 docs 包
)

// @title PaaS 平台API网关
// @version 1.0
// @description PaaS 平台的统一API网关，提供认证、路由转发和API聚合功能
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name MIT
// @license.url https://opensource.org/licenses/MIT

// @host localhost:8080
// @BasePath /api/v1

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.

func main() {
	// 初始化配置
	initConfig()
	
	// 初始化日志
	logger := logger.NewLogger()
	
	// 🚨 显示开发模式警告（如果启用）
	if viper.GetBool("security.jwt.dev_mode") || !viper.GetBool("security.jwt.enabled") {
		logger.Warn("⚠️  API网关开发模式已启用")
		logger.Warn("🔓 用户认证已禁用或使用开发模式")
		logger.Warn("🚨 请确保仅在开发环境使用此配置！")
	}
	
	// 初始化数据库（用于认证服务）
	db, err := database.NewConnection()
	if err != nil {
		log.Fatalf("数据库连接失败: %v", err)
	}
	
	// 自动迁移数据库表
	if err := database.AutoMigrate(db); err != nil {
		log.Fatalf("数据库迁移失败: %v", err)
	}
	
	// 初始化JWT服务
	jwtConfig := auth.JWTConfig{
		Secret:           viper.GetString("security.jwt.secret"),
		AccessTokenTTL:   viper.GetDuration("security.jwt.expires_in"),
		RefreshTokenTTL:  viper.GetDuration("security.jwt.refresh_expires_in"),
		Issuer:           "paas-platform",
		Audience:         "paas-api",
	}
	jwtService := auth.NewJWTService(jwtConfig, logger)
	
	// 初始化认证服务
	authService := auth.NewAuthService(db, jwtService, logger, auth.AuthConfig{
		AccessTokenExpiry:  viper.GetDuration("security.jwt.expires_in"),
		RefreshTokenExpiry: viper.GetDuration("security.jwt.refresh_expires_in"),
		MaxLoginAttempts:   5,
		LockoutDuration:    15 * time.Minute,
	})
	
	// 初始化认证处理器
	authHandler := auth.NewHandler(authService, logger)

	// 初始化验证系统
	apiValidator := validator.NewAPIValidator(logger)
	validationMiddleware := initValidationSystem(apiValidator, logger)

	// 初始化 Gin 路由
	router := setupRouter(authHandler, jwtService, validationMiddleware, logger)
	
	// 启动 HTTP 服务器
	srv := &http.Server{
		Addr:         fmt.Sprintf(":%d", viper.GetInt("server.port")),
		Handler:      router,
		ReadTimeout:  viper.GetDuration("server.read_timeout"),
		WriteTimeout: viper.GetDuration("server.write_timeout"),
		IdleTimeout:  viper.GetDuration("server.idle_timeout"),
	}
	
	// 优雅启动
	go func() {
		logger.Info("API网关启动", 
			"port", viper.GetInt("server.port"),
			"auth_enabled", viper.GetBool("security.jwt.enabled"),
			"dev_mode", viper.GetBool("security.jwt.dev_mode"))
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("服务启动失败: %v", err)
		}
	}()
	
	// 等待中断信号以优雅关闭服务器
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	
	logger.Info("正在关闭API网关...")
	
	// 优雅关闭，超时时间为 30 秒
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	if err := srv.Shutdown(ctx); err != nil {
		log.Fatalf("服务关闭失败: %v", err)
	}
	
	logger.Info("API网关已关闭")
}

// initConfig 初始化配置
func initConfig() {
	// 支持通过命令行参数指定配置文件
	configFile := "api-gateway"

	// 解析命令行参数
	for i, arg := range os.Args {
		if arg == "--config" && i+1 < len(os.Args) {
			configFile = os.Args[i+1]
			break
		}
		if strings.HasPrefix(arg, "--config=") {
			configFile = strings.TrimPrefix(arg, "--config=")
			break
		}
	}

	// 如果配置文件包含路径，分离目录和文件名
	if strings.Contains(configFile, "/") {
		dir := filepath.Dir(configFile)
		name := filepath.Base(configFile)
		// 移除.yaml扩展名
		if strings.HasSuffix(name, ".yaml") {
			name = strings.TrimSuffix(name, ".yaml")
		}
		if strings.HasSuffix(name, ".yml") {
			name = strings.TrimSuffix(name, ".yml")
		}

		viper.SetConfigName(name)
		viper.AddConfigPath(dir)
	} else {
		viper.SetConfigName(configFile)
		viper.AddConfigPath("./configs")
		viper.AddConfigPath(".")
	}

	viper.SetConfigType("yaml")
	
	// 设置默认值
	viper.SetDefault("server.port", 8080)
	viper.SetDefault("server.mode", "debug")
	viper.SetDefault("server.read_timeout", "30s")
	viper.SetDefault("server.write_timeout", "30s")
	viper.SetDefault("server.idle_timeout", "60s")
	viper.SetDefault("database.driver", "sqlite")
	viper.SetDefault("database.dsn", "./data/api-gateway.db")
	viper.SetDefault("log.level", "info")
	viper.SetDefault("log.format", "json")
	
	// 🔧 认证相关默认配置
	viper.SetDefault("security.jwt.enabled", true)
	viper.SetDefault("security.jwt.secret", "your-jwt-secret-key")
	viper.SetDefault("security.jwt.expires_in", "24h")
	viper.SetDefault("security.jwt.refresh_expires_in", "168h")
	viper.SetDefault("security.jwt.dev_mode", false)
	viper.SetDefault("security.jwt.dev_token", "dev-token")
	
	// 支持环境变量
	viper.AutomaticEnv()
	viper.SetEnvPrefix("API_GATEWAY")
	
	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			log.Println("配置文件未找到，使用默认配置")
		} else {
			log.Fatalf("读取配置文件失败: %v", err)
		}
	}
	
	log.Printf("使用配置文件: %s", viper.ConfigFileUsed())
}

// setupRouter 设置路由
func setupRouter(authHandler *auth.Handler, jwtService auth.JWTService, validationMiddleware *middleware.ValidationMiddleware, logger logger.Logger) *gin.Engine {
	// 设置 Gin 模式
	gin.SetMode(viper.GetString("server.mode"))
	
	router := gin.New()
	
	// 添加基础中间件
	router.Use(middleware.Logger(logger))
	router.Use(middleware.Recovery(logger))
	router.Use(middleware.CORS())

	// 添加验证中间件（在认证之前）
	router.Use(validationMiddleware.Handler())

	// 初始化转换中间件
	transformMiddleware := setupTransformMiddleware(logger)
	router.Use(transformMiddleware.Handler())

	// 初始化版本控制中间件
	versionMiddleware := setupVersionMiddleware(logger)
	router.Use(versionMiddleware.Handler())

	// 初始化 Mock 中间件
	mockMiddleware := setupMockMiddleware(logger)
	router.Use(mockMiddleware.Handler())

	// 初始化弹性中间件（断路器+重试）
	resilienceMiddleware := setupResilienceMiddleware(logger)
	router.Use(resilienceMiddleware.Handler())

	// 初始化 WebSocket 中间件
	wsMiddleware := setupWebSocketMiddleware(logger)
	router.Use(wsMiddleware.Handler())

	// 初始化 GraphQL 中间件
	gqlMiddleware := setupGraphQLMiddleware(logger)
	router.Use(gqlMiddleware.Handler())
	
	// 健康检查接口
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":      "healthy",
			"service":     "api-gateway",
			"version":     "1.0.0",
			"timestamp":   time.Now().Format(time.RFC3339),
			"auth_enabled": viper.GetBool("security.jwt.enabled"),
			"dev_mode":    viper.GetBool("security.jwt.dev_mode"),
		})
	})
	
	// 就绪检查接口
	router.GET("/ready", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"ready":     true,
			"timestamp": time.Now().Format(time.RFC3339),
		})
	})
	
	// Swagger 文档 (开发环境)
	if gin.Mode() == gin.DebugMode {
		router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
		router.GET("/docs", func(c *gin.Context) {
			c.Redirect(http.StatusMovedPermanently, "/swagger/index.html")
		})
		router.GET("/api/docs", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"title":       "PaaS 平台API网关",
				"version":     "1.0.0",
				"description": "PaaS 平台的统一API网关，提供认证、路由转发和API聚合功能",
				"swagger_url": "/swagger/index.html",
				"base_path":   "/api/v1",
				"host":        fmt.Sprintf("localhost:%d", viper.GetInt("server.port")),
			})
		})
	}
	
	// 🔀 API代理路由 - 将所有API请求代理到对应的微服务
	api := router.Group("/api")
	{
		// 🔐 认证相关路由 -> User Service (8083) (无需预先认证)
		authGroup := api.Group("/v1/auth")
		{
			authGroup.Any("/*path", func(c *gin.Context) {
				// 🔧 修复：代理认证请求到正确的User Service端口 (8083)
				targetPath := "/api/v1/auth" + c.Param("path")
				targetURL := "http://localhost:8083" + targetPath

				logger.Debug("代理认证请求到User Service",
					"method", c.Request.Method,
					"source_path", c.Request.URL.Path,
					"target_url", targetURL)

				proxyToURL(c, targetURL, logger)
			})
		}

		// 🔒 业务API路由组 (需要认证，在开发模式下会跳过)
		v1 := api.Group("/v1")
		// 创建 JWT 服务适配器以兼容 middleware.JWTService 接口
		jwtAdapter := auth.NewJWTServiceAdapter(jwtService, logger)
		v1.Use(middleware.NewAuthMiddleware(jwtAdapter, logger).Handler())
		{
			// 🔧 添加用户管理路由 -> User Service (8083) (需要认证)
			users := v1.Group("/users")
			{
				users.Any("/*path", func(c *gin.Context) {
					targetPath := "/api/v1/users" + c.Param("path")
					targetURL := "http://localhost:8083" + targetPath

					logger.Debug("代理用户管理请求到User Service",
						"method", c.Request.Method,
						"source_path", c.Request.URL.Path,
						"target_url", targetURL)

					proxyToURL(c, targetURL, logger)
				})
			}

			// 代理到其他服务的路由
			setupProxyRoutes(v1, logger)
		}
	}

	// 设置 WebSocket 路由
	setupWebSocketRoutes(router, wsMiddleware, logger)

	// 设置 GraphQL 路由
	setupGraphQLRoutes(router, gqlMiddleware, logger)

	return router
}

// setupTransformMiddleware 设置转换中间件
func setupTransformMiddleware(logger logger.Logger) *middleware.TransformMiddleware {
	// 创建请求转换器
	requestTransformer := transformer.NewRequestTransformer(logger)

	// 创建响应转换器
	responseTransformer := transformer.NewResponseTransformer(logger)

	// 创建规则管理器
	ruleManager := transformer.NewRuleManager(logger, "configs/transform-rules", false)

	// 加载转换规则
	if err := ruleManager.LoadRulesFromDirectory(); err != nil {
		logger.Error("加载转换规则失败", "error", err)
	} else {
		logger.Info("转换规则加载成功", "stats", ruleManager.GetStats())
	}

	// 获取转换配置
	var transformConfig middleware.TransformConfig
	if gin.Mode() == gin.DebugMode {
		transformConfig = middleware.DevelopmentTransformConfig()
	} else {
		transformConfig = middleware.ProductionTransformConfig()
	}

	// 创建转换中间件
	transformMiddleware := middleware.NewTransformMiddleware(
		requestTransformer,
		responseTransformer,
		logger,
		transformConfig,
	)

	// 加载规则到转换器
	rules := ruleManager.GetAllRules()
	if err := transformMiddleware.LoadTransformRules(rules); err != nil {
		logger.Error("加载转换规则到中间件失败", "error", err)
	}

	logger.Info("转换中间件初始化完成",
		"enabled", transformConfig.Enabled,
		"request_transform", transformConfig.RequestTransform,
		"response_transform", transformConfig.ResponseTransform,
		"rules_count", len(rules))

	return transformMiddleware
}

// setupVersionMiddleware 设置版本控制中间件
func setupVersionMiddleware(logger logger.Logger) *middleware.VersionMiddleware {
	// 创建版本管理器
	versionConfig := version.DefaultVersionConfig()
	versionManager := version.NewVersionManager(versionConfig, logger)

	// 创建版本路由器
	versionRouter := version.NewVersionRouter(versionManager, logger)

	// 创建配置加载器
	configLoader := version.NewConfigLoader("configs/versions", logger)

	// 加载版本配置
	if err := configLoader.LoadAndApplyConfig(versionManager, "api-versions.json"); err != nil {
		logger.Error("加载版本配置失败", "error", err)
	} else {
		logger.Info("版本配置加载成功")
	}

	// 获取版本中间件配置
	var middlewareConfig middleware.VersionMiddlewareConfig
	if gin.Mode() == gin.DebugMode {
		middlewareConfig = middleware.DevelopmentVersionMiddlewareConfig()
	} else {
		middlewareConfig = middleware.ProductionVersionMiddlewareConfig()
	}

	// 创建版本控制中间件
	versionMiddleware := middleware.NewVersionMiddleware(
		versionManager,
		versionRouter,
		logger,
		middlewareConfig,
	)

	logger.Info("版本控制中间件初始化完成",
		"enabled", middlewareConfig.Enabled,
		"default_version", middlewareConfig.DefaultVersion,
		"strict_versioning", middlewareConfig.StrictVersioning,
		"versions_count", len(versionManager.GetAllVersions()),
		"routes_count", len(versionManager.GetAllRoutes()))

	return versionMiddleware
}

// setupVersionMiddleware 设置版本控制中间件
func setupVersionMiddleware(logger logger.Logger) *middleware.VersionMiddleware {
	// 创建版本管理器
	versionConfig := version.DefaultVersionConfig()
	versionManager := version.NewVersionManager(versionConfig, logger)

	// 创建版本路由器
	versionRouter := version.NewVersionRouter(versionManager, logger)

	// 创建配置加载器
	configLoader := version.NewConfigLoader("configs/versions", logger)

	// 加载版本配置
	if err := configLoader.LoadAndApplyConfig(versionManager, "api-versions.json"); err != nil {
		logger.Error("加载版本配置失败", "error", err)
	} else {
		logger.Info("版本配置加载成功")
	}

	// 获取版本中间件配置
	var middlewareConfig middleware.VersionMiddlewareConfig
	if gin.Mode() == gin.DebugMode {
		middlewareConfig = middleware.DevelopmentVersionMiddlewareConfig()
	} else {
		middlewareConfig = middleware.ProductionVersionMiddlewareConfig()
	}

	// 创建版本控制中间件
	versionMiddleware := middleware.NewVersionMiddleware(
		versionManager,
		versionRouter,
		logger,
		middlewareConfig,
	)

	logger.Info("版本控制中间件初始化完成",
		"enabled", middlewareConfig.Enabled,
		"default_version", middlewareConfig.DefaultVersion,
		"strict_versioning", middlewareConfig.StrictVersioning,
		"versions_count", len(versionManager.GetAllVersions()),
		"routes_count", len(versionManager.GetAllRoutes()))

	return versionMiddleware
}

// setupMockMiddleware 设置 Mock 中间件
func setupMockMiddleware(logger logger.Logger) *middleware.MockMiddleware {
	// 获取 Mock 配置
	var mockConfig mock.MockConfig
	if gin.Mode() == gin.DebugMode {
		mockConfig = mock.DevelopmentMockConfig()
	} else {
		mockConfig = mock.ProductionMockConfig()
	}

	// 创建 Mock 管理器
	mockManager := mock.NewMockManager(mockConfig, logger)

	// 创建配置加载器
	configLoader := mock.NewConfigLoader("configs/mocks", logger)

	// 加载 Mock 规则
	if err := configLoader.LoadAndApplyRules(mockManager); err != nil {
		logger.Error("加载 Mock 规则失败", "error", err)
	} else {
		logger.Info("Mock 规则加载成功")
	}

	// 获取中间件配置
	var middlewareConfig middleware.MockMiddlewareConfig
	if gin.Mode() == gin.DebugMode {
		middlewareConfig = middleware.DevelopmentMockMiddlewareConfig()
	} else {
		middlewareConfig = middleware.ProductionMockMiddlewareConfig()
	}

	// 创建 Mock 中间件
	mockMiddleware := middleware.NewMockMiddleware(
		mockManager,
		logger,
		middlewareConfig,
	)

	logger.Info("Mock 中间件初始化完成",
		"enabled", middlewareConfig.Enabled,
		"force_mode", middlewareConfig.ForceMode,
		"pass_through", middlewareConfig.PassThrough,
		"rules_count", len(mockManager.GetAllMocks()))

	return mockMiddleware
}

// setupResilienceMiddleware 设置弹性中间件
func setupResilienceMiddleware(logger logger.Logger) *middleware.ResilienceMiddleware {
	// 获取断路器管理器配置
	var cbManagerConfig circuitbreaker.ManagerConfig
	if gin.Mode() == gin.DebugMode {
		cbManagerConfig = circuitbreaker.DevelopmentManagerConfig()
	} else {
		cbManagerConfig = circuitbreaker.ProductionManagerConfig()
	}

	// 创建断路器管理器
	cbManager := circuitbreaker.NewManager(cbManagerConfig, logger)

	// 获取重试管理器配置
	var retryManagerConfig retry.ManagerConfig
	if gin.Mode() == gin.DebugMode {
		retryManagerConfig = retry.DevelopmentManagerConfig()
	} else {
		retryManagerConfig = retry.ProductionManagerConfig()
	}

	// 创建重试管理器
	retryManager := retry.NewManager(retryManagerConfig, logger)

	// 获取弹性中间件配置
	resilienceConfig := middleware.DefaultResilienceConfig()
	if gin.Mode() == gin.ReleaseMode {
		// 生产环境配置调整
		resilienceConfig.DefaultCBConfig.Timeout = 120 * time.Second
		resilienceConfig.DefaultCBConfig.FailureThreshold = 10
		resilienceConfig.DefaultRetryConfig.MaxAttempts = 3
		resilienceConfig.LogRequests = false
	}

	// 创建弹性中间件
	resilienceMiddleware := middleware.NewResilienceMiddleware(
		cbManager,
		retryManager,
		logger,
		resilienceConfig,
	)

	logger.Info("弹性中间件初始化完成",
		"circuit_breaker_enabled", resilienceConfig.EnableCircuitBreaker,
		"retry_enabled", resilienceConfig.EnableRetry,
		"cb_timeout", resilienceConfig.DefaultCBConfig.Timeout,
		"retry_max_attempts", resilienceConfig.DefaultRetryConfig.MaxAttempts)

	return resilienceMiddleware
}

// setupWebSocketMiddleware 设置 WebSocket 中间件
func setupWebSocketMiddleware(logger logger.Logger) *middleware.WebSocketMiddleware {
	// 获取 WebSocket 配置
	var wsConfig websocket.Config
	if gin.Mode() == gin.DebugMode {
		wsConfig = websocket.DefaultConfig()
		wsConfig.MaxConnections = 1000
		wsConfig.MaxRooms = 100
		wsConfig.LogConnections = true
	} else {
		wsConfig = websocket.DefaultConfig()
		wsConfig.MaxConnections = 10000
		wsConfig.MaxRooms = 1000
		wsConfig.CheckOrigin = true
		wsConfig.AllowedOrigins = []string{} // 生产环境需要明确指定
	}

	// 创建 WebSocket 网关
	wsGateway := websocket.NewWebSocketGateway(wsConfig, logger)

	// 获取中间件配置
	var middlewareConfig middleware.WebSocketMiddlewareConfig
	if gin.Mode() == gin.DebugMode {
		middlewareConfig = middleware.DevelopmentWebSocketMiddlewareConfig()
	} else {
		middlewareConfig = middleware.ProductionWebSocketMiddlewareConfig()
	}

	// 创建 WebSocket 中间件
	wsMiddleware := middleware.NewWebSocketMiddleware(
		wsGateway,
		logger,
		middlewareConfig,
	)

	logger.Info("WebSocket 中间件初始化完成",
		"enabled", middlewareConfig.Enabled,
		"path", middlewareConfig.Path,
		"max_connections", wsConfig.MaxConnections,
		"max_rooms", wsConfig.MaxRooms,
		"require_auth", middlewareConfig.RequireAuth)

	return wsMiddleware
}

// setupGraphQLMiddleware 设置 GraphQL 中间件
func setupGraphQLMiddleware(logger logger.Logger) *middleware.GraphQLMiddleware {
	// 获取 GraphQL 配置
	var gqlConfig graphql.Config
	if gin.Mode() == gin.DebugMode {
		gqlConfig = graphql.DefaultConfig()
		gqlConfig.PlaygroundEnabled = true
		gqlConfig.IntrospectionEnabled = true
		gqlConfig.EnableLogging = true
		gqlConfig.CacheEnabled = true
		gqlConfig.CacheTTL = 1 * time.Minute
	} else {
		gqlConfig = graphql.DefaultConfig()
		gqlConfig.PlaygroundEnabled = false
		gqlConfig.IntrospectionEnabled = false
		gqlConfig.EnableLogging = false
		gqlConfig.AuthRequired = true
		gqlConfig.CacheEnabled = true
		gqlConfig.CacheTTL = 10 * time.Minute
	}

	// 创建 GraphQL 网关
	gqlGateway := graphql.NewGraphQLGateway(gqlConfig, logger)

	// 注册示例服务
	if gin.Mode() == gin.DebugMode {
		exampleService := &graphql.Service{
			ID:      "example-service",
			Name:    "示例 GraphQL 服务",
			URL:     "https://api.github.com/graphql",
			Schema:  "type Query { viewer: User } type User { login: String! }",
			Headers: map[string]string{
				"User-Agent": "PaaS-Platform-GraphQL-Gateway",
			},
			Timeout:    30 * time.Second,
			RetryCount: 3,
			Enabled:    true,
			Weight:     1,
			Version:    "1.0.0",
		}

		if err := gqlGateway.RegisterService(exampleService); err != nil {
			logger.Error("注册示例 GraphQL 服务失败", "error", err)
		}
	}

	// 获取中间件配置
	var middlewareConfig middleware.GraphQLMiddlewareConfig
	if gin.Mode() == gin.DebugMode {
		middlewareConfig = middleware.DevelopmentGraphQLMiddlewareConfig()
	} else {
		middlewareConfig = middleware.ProductionGraphQLMiddlewareConfig()
	}

	// 创建 GraphQL 中间件
	gqlMiddleware := middleware.NewGraphQLMiddleware(
		gqlGateway,
		logger,
		middlewareConfig,
	)

	logger.Info("GraphQL 中间件初始化完成",
		"enabled", middlewareConfig.Enabled,
		"path", middlewareConfig.Path,
		"playground_path", middlewareConfig.PlaygroundPath,
		"playground_enabled", gqlConfig.PlaygroundEnabled,
		"cache_enabled", gqlConfig.CacheEnabled,
		"require_auth", middlewareConfig.RequireAuth)

	return gqlMiddleware
}

// setupProxyRoutes 设置代理路由
func setupProxyRoutes(router *gin.RouterGroup, logger logger.Logger) {
	// 🔧 开发环境提示：在开发模式下，这些路由的认证会被自动跳过

	// 应用管理服务代理 - /api/v1/apps/* -> http://localhost:8081/api/v1/apps/*
	apps := router.Group("/apps")
	{
		apps.Any("/*path", func(c *gin.Context) {
			// 构建目标URL：保持完整路径
			targetPath := "/api/v1/apps" + c.Param("path")
			targetURL := "http://localhost:8081" + targetPath

			// 记录代理请求
			logger.Debug("代理应用管理请求",
				"method", c.Request.Method,
				"source_path", c.Request.URL.Path,
				"target_url", targetURL)

			// 执行代理
			proxyToURL(c, targetURL, logger)
		})
	}

	// 负载均衡器服务代理 - /api/v1/loadbalancer/* -> http://localhost:8086/api/v1/loadbalancer/*
	loadbalancer := router.Group("/loadbalancer")
	{
		loadbalancer.Any("/*path", func(c *gin.Context) {
			targetPath := "/api/v1/loadbalancer" + c.Param("path")
			targetURL := "http://localhost:8086" + targetPath

			logger.Debug("代理负载均衡器请求",
				"method", c.Request.Method,
				"source_path", c.Request.URL.Path,
				"target_url", targetURL)

			proxyToURL(c, targetURL, logger)
		})
	}

	// 脚本执行服务代理 - /api/v1/scripts/* -> http://localhost:8084/api/v1/scripts/*
	scripts := router.Group("/scripts")
	{
		scripts.Any("/*path", func(c *gin.Context) {
			targetPath := "/api/v1/scripts" + c.Param("path")
			targetURL := "http://localhost:8084" + targetPath

			logger.Debug("代理脚本服务请求",
				"method", c.Request.Method,
				"source_path", c.Request.URL.Path,
				"target_url", targetURL)

			proxyToURL(c, targetURL, logger)
		})
	}

	// CI/CD服务代理 - /api/v1/cicd/* -> http://localhost:8082/api/v1/*
	cicd := router.Group("/cicd")
	{
		cicd.Any("/*path", func(c *gin.Context) {
			// CI/CD服务的路径映射稍有不同
			targetPath := "/api/v1" + c.Param("path")
			targetURL := "http://localhost:8082" + targetPath

			logger.Debug("代理CI/CD服务请求",
				"method", c.Request.Method,
				"source_path", c.Request.URL.Path,
				"target_url", targetURL)

			proxyToURL(c, targetURL, logger)
		})
	}

	// 配置服务代理 - /api/v1/configs/* -> http://localhost:8084/api/v1/configs/*
	configs := router.Group("/configs")
	{
		configs.Any("/*path", func(c *gin.Context) {
			// 🔧 修复：配置服务端口应该是8084，不是8083
			targetPath := "/api/v1/configs" + c.Param("path")
			targetURL := "http://localhost:8084" + targetPath

			logger.Debug("代理配置服务请求",
				"method", c.Request.Method,
				"source_path", c.Request.URL.Path,
				"target_url", targetURL)

			proxyToURL(c, targetURL, logger)
		})
	}

	// FaaS 服务代理 - /api/v1/faas/* -> http://localhost:8087/api/v1/faas/*
	faas := router.Group("/faas")
	{
		faas.Any("/*path", func(c *gin.Context) {
			targetPath := "/api/v1/faas" + c.Param("path")
			targetURL := "http://localhost:8087" + targetPath

			logger.Debug("代理 FaaS 服务请求",
				"method", c.Request.Method,
				"source_path", c.Request.URL.Path,
				"target_url", targetURL,
				"function_id", c.Query("function_id"),
				"runtime", c.Query("runtime"))

			// 设置特殊的超时时间，因为函数执行可能需要更长时间
			c.Header("X-Proxy-Timeout", "300s")

			proxyToURL(c, targetURL, logger)
		})
	}
}

// initValidationSystem 初始化验证系统
func initValidationSystem(apiValidator *validator.APIValidator, logger logger.Logger) *middleware.ValidationMiddleware {
	// 创建规则加载器
	ruleLoader := validator.NewRuleLoader(logger)

	// 加载验证规则
	rulesPath := viper.GetString("validation.rules_file")
	if rulesPath == "" {
		rulesPath = ruleLoader.GetDefaultRulesPath()
	}

	config, err := ruleLoader.LoadFromFile(rulesPath)
	if err != nil {
		logger.Warn("加载验证规则失败，使用默认配置", "error", err)
		config = ruleLoader.CreateSampleConfig()
	}

	// 注册验证规则
	for _, rule := range config.Rules {
		if err := apiValidator.RegisterRule(rule); err != nil {
			logger.Error("注册验证规则失败", "error", err, "path", rule.Path, "method", rule.Method)
		}
	}

	// 注册 JSON Schema
	schemas := ruleLoader.ConvertToJSONSchemas(config.Schemas)
	for name, schema := range schemas {
		if err := apiValidator.RegisterSchema(name, schema); err != nil {
			logger.Error("注册JSON Schema失败", "error", err, "name", name)
		}
	}

	// 创建验证中间件配置
	validationConfig := middleware.ValidationConfig{
		Enabled:       config.Validation.Enabled,
		StrictMode:    config.Validation.StrictMode,
		LogValidation: config.Validation.LogValidation,
		CacheResults:  config.Validation.CacheResults,
		MaxCacheSize:  config.Validation.MaxCacheSize,
		CacheTTL:      config.Validation.CacheTTL,
		SkipPaths:     config.Validation.SkipPaths,
	}

	// 创建验证中间件
	validationMiddleware := middleware.NewValidationMiddleware(apiValidator, logger, validationConfig)

	logger.Info("验证系统初始化完成",
		"enabled", validationConfig.Enabled,
		"strict_mode", validationConfig.StrictMode,
		"rules_count", len(config.Rules),
		"schemas_count", len(config.Schemas))

	return validationMiddleware
}

// proxyToURL 简化的代理函数
func proxyToURL(c *gin.Context, targetURL string, logger logger.Logger) {
	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	// 读取请求体
	var bodyBytes []byte
	if c.Request.Body != nil {
		bodyBytes, _ = io.ReadAll(c.Request.Body)
	}

	// 创建代理请求
	req, err := http.NewRequest(c.Request.Method, targetURL, bytes.NewReader(bodyBytes))
	if err != nil {
		logger.Error("创建代理请求失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "代理请求创建失败",
		})
		return
	}

	// 复制请求头
	for key, values := range c.Request.Header {
		for _, value := range values {
			req.Header.Add(key, value)
		}
	}

	// 设置代理头部
	req.Header.Set("X-Forwarded-For", c.ClientIP())
	req.Header.Set("X-Real-IP", c.ClientIP())

	// 执行请求
	resp, err := client.Do(req)
	if err != nil {
		logger.Error("代理请求执行失败", "error", err, "target_url", targetURL)
		c.JSON(http.StatusBadGateway, gin.H{
			"error":   "服务不可用",
			"message": "无法连接到目标服务",
			"target":  targetURL,
		})
		return
	}
	defer resp.Body.Close()

	// 复制响应头
	for key, values := range resp.Header {
		for _, value := range values {
			c.Header(key, value)
		}
	}

	// 设置状态码并复制响应体
	c.Status(resp.StatusCode)
	io.Copy(c.Writer, resp.Body)
}

// setupWebSocketRoutes 设置 WebSocket API 路由
func setupWebSocketRoutes(router *gin.Engine, wsMiddleware *middleware.WebSocketMiddleware, logger logger.Logger) {
	// 创建 WebSocket API 处理器
	wsAPIHandler := websocket.NewAPIHandler(wsMiddleware.GetGateway(), logger)

	// 注册 WebSocket 管理 API 路由
	api := router.Group("/api/v1")
	wsAPIHandler.RegisterRoutes(api)

	// WebSocket 连接端点
	router.GET("/ws", func(c *gin.Context) {
		// WebSocket 升级由中间件处理
		c.Next()
	})

	// WebSocket 健康检查端点
	router.GET("/ws/health", func(c *gin.Context) {
		stats := wsMiddleware.GetStats()
		c.JSON(http.StatusOK, gin.H{
			"status": "healthy",
			"websocket": gin.H{
				"active_connections": stats.ActiveConnections,
				"active_rooms":       stats.ActiveRooms,
				"total_messages":     stats.TotalMessages,
				"last_updated":       stats.LastUpdated,
			},
		})
	})

	logger.Info("WebSocket API 路由设置完成",
		"websocket_endpoint", "/ws",
		"api_prefix", "/api/v1/websocket",
		"health_endpoint", "/ws/health")
}

// setupGraphQLRoutes 设置 GraphQL API 路由
func setupGraphQLRoutes(router *gin.Engine, gqlMiddleware *middleware.GraphQLMiddleware, logger logger.Logger) {
	// 创建 GraphQL API 处理器
	gqlAPIHandler := graphql.NewAPIHandler(gqlMiddleware.GetGateway(), logger)

	// 注册 GraphQL 管理 API 路由
	api := router.Group("/api/v1")
	gqlAPIHandler.RegisterRoutes(api)

	// GraphQL 查询端点
	router.POST("/graphql", func(c *gin.Context) {
		// GraphQL 查询由中间件处理
		c.Next()
	})

	router.GET("/graphql", func(c *gin.Context) {
		// 支持 GET 请求的 GraphQL 查询
		c.Next()
	})

	// GraphQL Playground 端点
	router.GET("/graphql/playground", func(c *gin.Context) {
		// Playground 由中间件处理
		c.Next()
	})

	// GraphQL Schema 端点
	router.GET("/graphql/schema", func(c *gin.Context) {
		schema := gqlMiddleware.GetSchema()
		c.Header("Content-Type", "text/plain")
		c.String(http.StatusOK, schema)
	})

	// GraphQL 健康检查端点
	router.GET("/graphql/health", func(c *gin.Context) {
		stats := gqlMiddleware.GetStats()
		services := gqlMiddleware.GetServices()

		enabledServices := 0
		for _, service := range services {
			if service.Enabled {
				enabledServices++
			}
		}

		c.JSON(http.StatusOK, gin.H{
			"status": "healthy",
			"graphql": gin.H{
				"enabled":          true,
				"total_services":   len(services),
				"enabled_services": enabledServices,
				"total_requests":   stats.TotalRequests,
				"success_requests": stats.SuccessRequests,
				"error_requests":   stats.ErrorRequests,
				"cache_hits":       stats.CacheHits,
				"cache_misses":     stats.CacheMisses,
				"average_latency":  stats.AverageLatency.String(),
				"last_updated":     stats.LastUpdated,
			},
		})
	})

	logger.Info("GraphQL API 路由设置完成",
		"graphql_endpoint", "/graphql",
		"playground_endpoint", "/graphql/playground",
		"schema_endpoint", "/graphql/schema",
		"api_prefix", "/api/v1/graphql",
		"health_endpoint", "/graphql/health")
}
