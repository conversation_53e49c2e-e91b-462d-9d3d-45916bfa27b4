package faas

import (
	"encoding/json"
	"time"

	"gorm.io/gorm"
)

// Function 函数定义模型
type Function struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	FunctionID  string    `json:"function_id" gorm:"uniqueIndex;not null;size:255" binding:"required"`
	Name        string    `json:"name" gorm:"not null;size:255" binding:"required"`
	Description string    `json:"description" gorm:"type:text"`
	Runtime     string    `json:"runtime" gorm:"not null;size:50" binding:"required"`
	Handler     string    `json:"handler" gorm:"size:255"`
	Code        string    `json:"code" gorm:"type:longtext;not null" binding:"required"`
	Version     string    `json:"version" gorm:"size:50;default:'1.0.0'"`
	Status      string    `json:"status" gorm:"size:20;default:'active'"` // active, inactive, deprecated
	
	// 配置信息
	Timeout     int    `json:"timeout" gorm:"default:30"`        // 超时时间(秒)
	MemoryLimit string `json:"memory_limit" gorm:"size:20"`      // 内存限制
	CPULimit    string `json:"cpu_limit" gorm:"size:20"`         // CPU限制
	
	// 环境变量 (JSON 格式存储)
	Environment string `json:"environment" gorm:"type:text"`
	
	// 标签 (JSON 格式存储)
	Tags string `json:"tags" gorm:"type:text"`
	
	// 元数据
	CreatedBy string    `json:"created_by" gorm:"size:255"`
	UpdatedBy string    `json:"updated_by" gorm:"size:255"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"deleted_at" gorm:"index"`
	
	// 关联关系
	Executions []FunctionExecution `json:"executions,omitempty" gorm:"foreignKey:FunctionID;references:FunctionID"`
}

// TableName 指定表名
func (Function) TableName() string {
	return "faas_functions"
}

// GetEnvironmentMap 获取环境变量映射
func (f *Function) GetEnvironmentMap() map[string]string {
	if f.Environment == "" {
		return make(map[string]string)
	}
	
	var env map[string]string
	if err := json.Unmarshal([]byte(f.Environment), &env); err != nil {
		return make(map[string]string)
	}
	
	return env
}

// SetEnvironmentMap 设置环境变量映射
func (f *Function) SetEnvironmentMap(env map[string]string) error {
	data, err := json.Marshal(env)
	if err != nil {
		return err
	}
	
	f.Environment = string(data)
	return nil
}

// GetTagsList 获取标签列表
func (f *Function) GetTagsList() []string {
	if f.Tags == "" {
		return []string{}
	}
	
	var tags []string
	if err := json.Unmarshal([]byte(f.Tags), &tags); err != nil {
		return []string{}
	}
	
	return tags
}

// SetTagsList 设置标签列表
func (f *Function) SetTagsList(tags []string) error {
	data, err := json.Marshal(tags)
	if err != nil {
		return err
	}
	
	f.Tags = string(data)
	return nil
}

// FunctionExecution 函数执行记录模型
type FunctionExecution struct {
	ID         uint      `json:"id" gorm:"primaryKey"`
	RequestID  string    `json:"request_id" gorm:"uniqueIndex;not null;size:255"`
	FunctionID string    `json:"function_id" gorm:"index;not null;size:255"`
	
	// 执行信息
	Status        string    `json:"status" gorm:"size:20;not null"`        // pending, running, success, failed, timeout
	Runtime       string    `json:"runtime" gorm:"size:50;not null"`
	ContainerID   string    `json:"container_id" gorm:"size:255"`
	
	// 执行参数 (JSON 格式存储)
	Parameters string `json:"parameters" gorm:"type:text"`
	
	// 执行结果 (JSON 格式存储)
	Result string `json:"result" gorm:"type:longtext"`
	Error  string `json:"error" gorm:"type:text"`
	
	// 执行日志 (JSON 格式存储)
	Logs string `json:"logs" gorm:"type:longtext"`
	
	// 性能指标
	ExecutionTime int64 `json:"execution_time"` // 执行时间(毫秒)
	MemoryUsage   int64 `json:"memory_usage"`   // 内存使用量(字节)
	CPUUsage      float64 `json:"cpu_usage"`    // CPU 使用率
	
	// 时间戳
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	
	// 关联关系
	Function *Function `json:"function,omitempty" gorm:"foreignKey:FunctionID;references:FunctionID"`
}

// TableName 指定表名
func (FunctionExecution) TableName() string {
	return "faas_function_executions"
}

// GetParametersMap 获取参数映射
func (fe *FunctionExecution) GetParametersMap() map[string]interface{} {
	if fe.Parameters == "" {
		return make(map[string]interface{})
	}
	
	var params map[string]interface{}
	if err := json.Unmarshal([]byte(fe.Parameters), &params); err != nil {
		return make(map[string]interface{})
	}
	
	return params
}

// SetParametersMap 设置参数映射
func (fe *FunctionExecution) SetParametersMap(params map[string]interface{}) error {
	data, err := json.Marshal(params)
	if err != nil {
		return err
	}
	
	fe.Parameters = string(data)
	return nil
}

// GetResultData 获取结果数据
func (fe *FunctionExecution) GetResultData() interface{} {
	if fe.Result == "" {
		return nil
	}
	
	var result interface{}
	if err := json.Unmarshal([]byte(fe.Result), &result); err != nil {
		return fe.Result // 如果不是 JSON，返回原始字符串
	}
	
	return result
}

// SetResultData 设置结果数据
func (fe *FunctionExecution) SetResultData(result interface{}) error {
	data, err := json.Marshal(result)
	if err != nil {
		return err
	}
	
	fe.Result = string(data)
	return nil
}

// GetLogsList 获取日志列表
func (fe *FunctionExecution) GetLogsList() []string {
	if fe.Logs == "" {
		return []string{}
	}
	
	var logs []string
	if err := json.Unmarshal([]byte(fe.Logs), &logs); err != nil {
		return []string{}
	}
	
	return logs
}

// SetLogsList 设置日志列表
func (fe *FunctionExecution) SetLogsList(logs []string) error {
	data, err := json.Marshal(logs)
	if err != nil {
		return err
	}
	
	fe.Logs = string(data)
	return nil
}

// FunctionVersion 函数版本模型
type FunctionVersion struct {
	ID         uint      `json:"id" gorm:"primaryKey"`
	FunctionID string    `json:"function_id" gorm:"index;not null;size:255"`
	Version    string    `json:"version" gorm:"not null;size:50"`
	Code       string    `json:"code" gorm:"type:longtext;not null"`
	Handler    string    `json:"handler" gorm:"size:255"`
	
	// 配置信息
	Timeout     int    `json:"timeout"`
	MemoryLimit string `json:"memory_limit" gorm:"size:20"`
	CPULimit    string `json:"cpu_limit" gorm:"size:20"`
	Environment string `json:"environment" gorm:"type:text"`
	
	// 版本状态
	Status    string `json:"status" gorm:"size:20;default:'active'"` // active, deprecated
	IsDefault bool   `json:"is_default" gorm:"default:false"`
	
	// 变更信息
	ChangeLog string `json:"change_log" gorm:"type:text"`
	CreatedBy string `json:"created_by" gorm:"size:255"`
	CreatedAt time.Time `json:"created_at"`
	
	// 关联关系
	Function *Function `json:"function,omitempty" gorm:"foreignKey:FunctionID;references:FunctionID"`
}

// TableName 指定表名
func (FunctionVersion) TableName() string {
	return "faas_function_versions"
}

// FunctionMetrics 函数指标模型
type FunctionMetrics struct {
	ID         uint      `json:"id" gorm:"primaryKey"`
	FunctionID string    `json:"function_id" gorm:"index;not null;size:255"`
	Date       time.Time `json:"date" gorm:"index;not null"`
	
	// 执行统计
	TotalExecutions      int64 `json:"total_executions"`
	SuccessfulExecutions int64 `json:"successful_executions"`
	FailedExecutions     int64 `json:"failed_executions"`
	TimeoutExecutions    int64 `json:"timeout_executions"`
	
	// 性能统计
	AvgExecutionTime int64   `json:"avg_execution_time"` // 平均执行时间(毫秒)
	MaxExecutionTime int64   `json:"max_execution_time"` // 最大执行时间(毫秒)
	MinExecutionTime int64   `json:"min_execution_time"` // 最小执行时间(毫秒)
	AvgMemoryUsage   int64   `json:"avg_memory_usage"`   // 平均内存使用量(字节)
	MaxMemoryUsage   int64   `json:"max_memory_usage"`   // 最大内存使用量(字节)
	AvgCPUUsage      float64 `json:"avg_cpu_usage"`      // 平均CPU使用率
	
	// 错误统计
	ErrorRate float64 `json:"error_rate"` // 错误率(百分比)
	
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	
	// 关联关系
	Function *Function `json:"function,omitempty" gorm:"foreignKey:FunctionID;references:FunctionID"`
}

// TableName 指定表名
func (FunctionMetrics) TableName() string {
	return "faas_function_metrics"
}

// FunctionTrigger 函数触发器模型
type FunctionTrigger struct {
	ID         uint      `json:"id" gorm:"primaryKey"`
	TriggerID  string    `json:"trigger_id" gorm:"uniqueIndex;not null;size:255"`
	FunctionID string    `json:"function_id" gorm:"index;not null;size:255"`
	
	// 触发器信息
	Name        string `json:"name" gorm:"not null;size:255"`
	Type        string `json:"type" gorm:"not null;size:50"`        // http, cron, event
	Status      string `json:"status" gorm:"size:20;default:'active'"` // active, inactive
	
	// 触发器配置 (JSON 格式存储)
	Config string `json:"config" gorm:"type:text"`
	
	// 统计信息
	TriggerCount int64     `json:"trigger_count" gorm:"default:0"`
	LastTriggered time.Time `json:"last_triggered"`
	
	CreatedBy string    `json:"created_by" gorm:"size:255"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"deleted_at" gorm:"index"`
	
	// 关联关系
	Function *Function `json:"function,omitempty" gorm:"foreignKey:FunctionID;references:FunctionID"`
}

// TableName 指定表名
func (FunctionTrigger) TableName() string {
	return "faas_function_triggers"
}

// GetConfigMap 获取配置映射
func (ft *FunctionTrigger) GetConfigMap() map[string]interface{} {
	if ft.Config == "" {
		return make(map[string]interface{})
	}
	
	var config map[string]interface{}
	if err := json.Unmarshal([]byte(ft.Config), &config); err != nil {
		return make(map[string]interface{})
	}
	
	return config
}

// SetConfigMap 设置配置映射
func (ft *FunctionTrigger) SetConfigMap(config map[string]interface{}) error {
	data, err := json.Marshal(config)
	if err != nil {
		return err
	}
	
	ft.Config = string(data)
	return nil
}
