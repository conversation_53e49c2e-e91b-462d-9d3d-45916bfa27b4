package faas

import (
	"os"
	"testing"

	"github.com/spf13/viper"
	"github.com/stretchr/testify/assert"
)

// TestFaaSServiceEnvConfig 测试 FaaS 服务环境变量配置
func TestFaaSServiceEnvConfig(t *testing.T) {
	// 设置测试环境变量
	testEnvVars := map[string]string{
		"FAAS_SERVICE_PORT":                        "8087",
		"FAAS_SERVICE_DB_DSN":                      "./data/faas-service.db",
		"FAAS_SERVICE_LOG_LEVEL":                   "debug",
		"FAAS_SERVICE_LOG_FILE":                    "./logs/faas-service.log",
		"FAAS_SERVICE_REDIS_DB":                    "4",
		"FAAS_SERVICE_PPROF_PORT":                  "6065",
		"FAAS_SERVICE_MAX_CONCURRENT_EXECUTIONS":   "50",
		"FAAS_SERVICE_DEFAULT_TIMEOUT":             "30s",
		"FAAS_SERVICE_MAX_EXECUTION_TIME":          "5m",
		"FAAS_SERVICE_CONTAINER_POOL_SIZE":         "5",
		"FAAS_SERVICE_PREWARM_CONTAINERS":          "2",
		"FAAS_SERVICE_CLEANUP_INTERVAL":            "5m",
		"FAAS_SERVICE_CPU_LIMIT":                   "0.5",
		"FAAS_SERVICE_MEMORY_LIMIT":                "512m",
		"FAAS_SERVICE_DISK_LIMIT":                  "1g",
		"FAAS_SERVICE_WORKSPACE_BASE_PATH":         "./data/faas/workspaces",
		"FAAS_SERVICE_ARTIFACTS_BASE_PATH":         "./data/faas/artifacts",
		"FAAS_SERVICE_WORKSPACE_MAX_SIZE":          "5Gi",
		"FAAS_SERVICE_ARTIFACTS_MAX_SIZE":          "20Gi",
		"FAAS_SERVICE_WORKSPACE_RETENTION_DAYS":    "3",
		"FAAS_SERVICE_ARTIFACTS_RETENTION_DAYS":    "7",
		"FAAS_SERVICE_NOTIFICATION_ENABLED":        "false",
		"FAAS_SERVICE_WEBHOOK_URL":                 "",
		"FAAS_SERVICE_NOTIFICATION_TIMEOUT":        "30s",
	}

	// 设置环境变量
	for key, value := range testEnvVars {
		os.Setenv(key, value)
	}

	// 清理函数
	defer func() {
		for key := range testEnvVars {
			os.Unsetenv(key)
		}
	}()

	// 初始化 viper 配置
	v := viper.New()
	v.AutomaticEnv()
	v.SetEnvPrefix("FAAS_SERVICE")

	// 测试基础服务配置
	t.Run("基础服务配置", func(t *testing.T) {
		assert.Equal(t, "8087", v.GetString("PORT"), "端口配置应该正确")
		assert.Equal(t, "./data/faas-service.db", v.GetString("DB_DSN"), "数据库 DSN 配置应该正确")
		assert.Equal(t, "debug", v.GetString("LOG_LEVEL"), "日志级别配置应该正确")
		assert.Equal(t, "./logs/faas-service.log", v.GetString("LOG_FILE"), "日志文件配置应该正确")
		assert.Equal(t, "4", v.GetString("REDIS_DB"), "Redis 数据库编号配置应该正确")
		assert.Equal(t, "6065", v.GetString("PPROF_PORT"), "性能分析端口配置应该正确")
	})

	// 测试执行器配置
	t.Run("执行器配置", func(t *testing.T) {
		assert.Equal(t, "50", v.GetString("MAX_CONCURRENT_EXECUTIONS"), "最大并发执行数配置应该正确")
		assert.Equal(t, "30s", v.GetString("DEFAULT_TIMEOUT"), "默认超时配置应该正确")
		assert.Equal(t, "5m", v.GetString("MAX_EXECUTION_TIME"), "最大执行时间配置应该正确")
		assert.Equal(t, "5", v.GetString("CONTAINER_POOL_SIZE"), "容器池大小配置应该正确")
		assert.Equal(t, "2", v.GetString("PREWARM_CONTAINERS"), "预热容器数量配置应该正确")
		assert.Equal(t, "5m", v.GetString("CLEANUP_INTERVAL"), "清理间隔配置应该正确")
	})

	// 测试资源限制配置
	t.Run("资源限制配置", func(t *testing.T) {
		assert.Equal(t, "0.5", v.GetString("CPU_LIMIT"), "CPU 限制配置应该正确")
		assert.Equal(t, "512m", v.GetString("MEMORY_LIMIT"), "内存限制配置应该正确")
		assert.Equal(t, "1g", v.GetString("DISK_LIMIT"), "磁盘限制配置应该正确")
	})

	// 测试存储配置
	t.Run("存储配置", func(t *testing.T) {
		assert.Equal(t, "./data/faas/workspaces", v.GetString("WORKSPACE_BASE_PATH"), "工作空间路径配置应该正确")
		assert.Equal(t, "./data/faas/artifacts", v.GetString("ARTIFACTS_BASE_PATH"), "产物存储路径配置应该正确")
		assert.Equal(t, "5Gi", v.GetString("WORKSPACE_MAX_SIZE"), "工作空间最大大小配置应该正确")
		assert.Equal(t, "20Gi", v.GetString("ARTIFACTS_MAX_SIZE"), "产物存储最大大小配置应该正确")
		assert.Equal(t, "3", v.GetString("WORKSPACE_RETENTION_DAYS"), "工作空间保留天数配置应该正确")
		assert.Equal(t, "7", v.GetString("ARTIFACTS_RETENTION_DAYS"), "产物保留天数配置应该正确")
	})

	// 测试通知配置
	t.Run("通知配置", func(t *testing.T) {
		assert.Equal(t, "false", v.GetString("NOTIFICATION_ENABLED"), "通知开关配置应该正确")
		assert.Equal(t, "", v.GetString("WEBHOOK_URL"), "Webhook URL 配置应该正确")
		assert.Equal(t, "30s", v.GetString("NOTIFICATION_TIMEOUT"), "通知超时配置应该正确")
	})
}

// TestFaaSServiceConfigDefaults 测试 FaaS 服务配置默认值
func TestFaaSServiceConfigDefaults(t *testing.T) {
	// 创建新的 viper 实例，不设置任何环境变量
	v := viper.New()

	// 设置默认值（模拟 cmd/faas-service/main.go 中的 setDefaultConfig 函数）
	v.SetDefault("server.port", 8087)
	v.SetDefault("server.host", "0.0.0.0")
	v.SetDefault("server.mode", "release")
	v.SetDefault("database.driver", "sqlite")
	v.SetDefault("database.dsn", "./data/faas-service.db")
	v.SetDefault("database.max_open_conns", 10)
	v.SetDefault("database.max_idle_conns", 5)
	v.SetDefault("executor.max_concurrent_executions", 100)
	v.SetDefault("executor.default_timeout", "30s")
	v.SetDefault("executor.max_execution_time", "5m")
	v.SetDefault("executor.container_pool_size", 10)
	v.SetDefault("executor.prewarm_containers", 3)
	v.SetDefault("resources.cpu_limit", "1.0")
	v.SetDefault("resources.memory_limit", "1Gi")
	v.SetDefault("resources.disk_limit", "2Gi")

	// 测试默认配置值
	t.Run("默认配置值", func(t *testing.T) {
		assert.Equal(t, 8087, v.GetInt("server.port"), "默认端口应该是 8087")
		assert.Equal(t, "0.0.0.0", v.GetString("server.host"), "默认主机应该是 0.0.0.0")
		assert.Equal(t, "release", v.GetString("server.mode"), "默认模式应该是 release")
		assert.Equal(t, "sqlite", v.GetString("database.driver"), "默认数据库驱动应该是 sqlite")
		assert.Equal(t, "./data/faas-service.db", v.GetString("database.dsn"), "默认数据库 DSN 应该正确")
		assert.Equal(t, 10, v.GetInt("database.max_open_conns"), "默认最大连接数应该是 10")
		assert.Equal(t, 5, v.GetInt("database.max_idle_conns"), "默认最大空闲连接数应该是 5")
		assert.Equal(t, 100, v.GetInt("executor.max_concurrent_executions"), "默认最大并发执行数应该是 100")
		assert.Equal(t, "30s", v.GetString("executor.default_timeout"), "默认超时应该是 30s")
		assert.Equal(t, "5m", v.GetString("executor.max_execution_time"), "默认最大执行时间应该是 5m")
		assert.Equal(t, 10, v.GetInt("executor.container_pool_size"), "默认容器池大小应该是 10")
		assert.Equal(t, 3, v.GetInt("executor.prewarm_containers"), "默认预热容器数量应该是 3")
		assert.Equal(t, "1.0", v.GetString("resources.cpu_limit"), "默认 CPU 限制应该是 1.0")
		assert.Equal(t, "1Gi", v.GetString("resources.memory_limit"), "默认内存限制应该是 1Gi")
		assert.Equal(t, "2Gi", v.GetString("resources.disk_limit"), "默认磁盘限制应该是 2Gi")
	})
}

// TestFaaSServiceConfigValidation 测试 FaaS 服务配置验证
func TestFaaSServiceConfigValidation(t *testing.T) {
	testCases := []struct {
		name        string
		envVars     map[string]string
		expectValid bool
		description string
	}{
		{
			name: "有效的开发环境配置",
			envVars: map[string]string{
				"FAAS_SERVICE_PORT":                      "8087",
				"FAAS_SERVICE_DB_DSN":                    "./data/faas-service.db",
				"FAAS_SERVICE_MAX_CONCURRENT_EXECUTIONS": "50",
				"FAAS_SERVICE_CPU_LIMIT":                 "0.5",
				"FAAS_SERVICE_MEMORY_LIMIT":              "512m",
			},
			expectValid: true,
			description: "开发环境的标准配置应该有效",
		},
		{
			name: "有效的生产环境配置",
			envVars: map[string]string{
				"FAAS_SERVICE_PORT":                      "8087",
				"FAAS_SERVICE_DB_DSN":                    "host=localhost user=faas_user password=password dbname=paas_faas port=5432",
				"FAAS_SERVICE_MAX_CONCURRENT_EXECUTIONS": "100",
				"FAAS_SERVICE_CPU_LIMIT":                 "2.0",
				"FAAS_SERVICE_MEMORY_LIMIT":              "2Gi",
			},
			expectValid: true,
			description: "生产环境的标准配置应该有效",
		},
		{
			name: "端口配置缺失",
			envVars: map[string]string{
				"FAAS_SERVICE_DB_DSN": "./data/faas-service.db",
			},
			expectValid: false,
			description: "缺少端口配置应该无效",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 清理环境变量
			for key := range tc.envVars {
				os.Unsetenv(key)
			}

			// 设置测试环境变量
			for key, value := range tc.envVars {
				os.Setenv(key, value)
			}

			// 清理函数
			defer func() {
				for key := range tc.envVars {
					os.Unsetenv(key)
				}
			}()

			// 创建 viper 实例并加载配置
			v := viper.New()
			v.AutomaticEnv()
			v.SetEnvPrefix("FAAS_SERVICE")

			// 验证配置
			hasPort := v.GetString("PORT") != ""
			hasDBDSN := v.GetString("DB_DSN") != ""

			isValid := hasPort && hasDBDSN

			if tc.expectValid {
				assert.True(t, isValid, tc.description)
			} else {
				assert.False(t, isValid, tc.description)
			}
		})
	}
}
