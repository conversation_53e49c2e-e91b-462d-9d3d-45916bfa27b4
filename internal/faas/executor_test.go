package faas

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"paas-platform/internal/container"
	"paas-platform/pkg/logger"
)

// MockContainerManager 模拟容器管理器
type MockContainerManager struct {
	mock.Mock
}

func (m *MockContainerManager) CreateContainer(ctx context.Context, config *container.ContainerConfig) (*container.Container, error) {
	args := m.Called(ctx, config)
	return args.Get(0).(*container.Container), args.Error(1)
}

func (m *MockContainerManager) StartContainer(ctx context.Context, containerID string) error {
	args := m.Called(ctx, containerID)
	return args.Error(0)
}

func (m *MockContainerManager) StopContainer(ctx context.Context, containerID string, timeout time.Duration) error {
	args := m.Called(ctx, containerID, timeout)
	return args.Error(0)
}

func (m *MockContainerManager) RemoveContainer(ctx context.Context, containerID string, force bool) error {
	args := m.Called(ctx, containerID, force)
	return args.Error(0)
}

func (m *MockContainerManager) GetContainerLogs(ctx context.Context, containerID string) (interface{}, error) {
	args := m.Called(ctx, containerID)
	return args.Get(0), args.Error(1)
}

func (m *MockContainerManager) GetContainerStatus(ctx context.Context, containerID string) (*container.ContainerStatus, error) {
	args := m.Called(ctx, containerID)
	return args.Get(0).(*container.ContainerStatus), args.Error(1)
}

func (m *MockContainerManager) ExecCommand(ctx context.Context, containerID string, cmd []string) (string, error) {
	args := m.Called(ctx, containerID, cmd)
	return args.String(0), args.Error(1)
}

func (m *MockContainerManager) GetContainerStats(ctx context.Context, containerID string) (*container.ContainerStats, error) {
	args := m.Called(ctx, containerID)
	return args.Get(0).(*container.ContainerStats), args.Error(1)
}

func (m *MockContainerManager) ListContainers(ctx context.Context, filters map[string]string) ([]*container.Container, error) {
	args := m.Called(ctx, filters)
	return args.Get(0).([]*container.Container), args.Error(1)
}

func (m *MockContainerManager) PullImage(ctx context.Context, image string) error {
	args := m.Called(ctx, image)
	return args.Error(0)
}

func (m *MockContainerManager) ImageExists(ctx context.Context, image string) (bool, error) {
	args := m.Called(ctx, image)
	return args.Bool(0), args.Error(1)
}

func (m *MockContainerManager) GetContainerStats(ctx context.Context, containerID string) (*container.ContainerStats, error) {
	args := m.Called(ctx, containerID)
	return args.Get(0).(*container.ContainerStats), args.Error(1)
}

// TestNewFunctionExecutor 测试创建函数执行器
func TestNewFunctionExecutor(t *testing.T) {
	mockContainerManager := new(MockContainerManager)
	mockLogger := logger.NewConsoleLogger("test")
	
	config := &ExecutorConfig{
		MaxConcurrentExecutions: 10,
		DefaultTimeout:          30 * time.Second,
		MaxExecutionTime:        5 * time.Minute,
		ContainerPoolSize:       5,
		PrewarmContainers:       2,
		CleanupInterval:         5 * time.Minute,
		ResourceLimits: &ResourceLimits{
			CPULimit:    "0.5",
			MemoryLimit: "512m",
			DiskLimit:   "1g",
		},
	}

	executor := NewFunctionExecutor(mockContainerManager, config, mockLogger)

	assert.NotNil(t, executor)
	assert.Equal(t, mockContainerManager, executor.containerManager)
	assert.Equal(t, config, executor.config)
	assert.NotNil(t, executor.containerPool)
	assert.NotNil(t, executor.metrics)
}

// TestValidateRequest 测试请求验证
func TestValidateRequest(t *testing.T) {
	mockContainerManager := new(MockContainerManager)
	mockLogger := logger.NewConsoleLogger("test")
	executor := NewFunctionExecutor(mockContainerManager, nil, mockLogger)

	tests := []struct {
		name    string
		request *FunctionRequest
		wantErr bool
		errMsg  string
	}{
		{
			name: "有效请求",
			request: &FunctionRequest{
				FunctionID:   "test-func-1",
				FunctionName: "testFunction",
				Runtime:      "nodejs",
				Code:         "console.log('Hello World');",
				Handler:      "index.handler",
			},
			wantErr: false,
		},
		{
			name: "缺少函数ID",
			request: &FunctionRequest{
				FunctionName: "testFunction",
				Runtime:      "nodejs",
				Code:         "console.log('Hello World');",
			},
			wantErr: true,
			errMsg:  "函数ID不能为空",
		},
		{
			name: "缺少函数名称",
			request: &FunctionRequest{
				FunctionID: "test-func-1",
				Runtime:    "nodejs",
				Code:       "console.log('Hello World');",
			},
			wantErr: true,
			errMsg:  "函数名称不能为空",
		},
		{
			name: "不支持的运行时",
			request: &FunctionRequest{
				FunctionID:   "test-func-1",
				FunctionName: "testFunction",
				Runtime:      "unsupported",
				Code:         "console.log('Hello World');",
			},
			wantErr: true,
			errMsg:  "不支持的运行时: unsupported",
		},
		{
			name: "缺少代码",
			request: &FunctionRequest{
				FunctionID:   "test-func-1",
				FunctionName: "testFunction",
				Runtime:      "nodejs",
			},
			wantErr: true,
			errMsg:  "函数代码不能为空",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := executor.validateRequest(tt.request)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestCheckConcurrencyLimit 测试并发限制检查
func TestCheckConcurrencyLimit(t *testing.T) {
	mockContainerManager := new(MockContainerManager)
	mockLogger := logger.NewConsoleLogger("test")
	
	config := &ExecutorConfig{
		MaxConcurrentExecutions: 2,
	}
	executor := NewFunctionExecutor(mockContainerManager, config, mockLogger)

	// 初始状态应该允许执行
	assert.True(t, executor.checkConcurrencyLimit())

	// 增加活跃执行数
	executor.incrementActiveExecutions()
	assert.True(t, executor.checkConcurrencyLimit())

	executor.incrementActiveExecutions()
	assert.False(t, executor.checkConcurrencyLimit()) // 达到限制

	// 减少活跃执行数
	executor.decrementActiveExecutions()
	assert.True(t, executor.checkConcurrencyLimit())
}

// TestGetRuntimeImage 测试获取运行时镜像
func TestGetRuntimeImage(t *testing.T) {
	mockContainerManager := new(MockContainerManager)
	mockLogger := logger.NewConsoleLogger("test")
	executor := NewFunctionExecutor(mockContainerManager, nil, mockLogger)

	tests := []struct {
		runtime string
		want    string
	}{
		{"nodejs", "node:18-alpine"},
		{"python", "python:3.9-alpine"},
		{"go", "golang:1.21-alpine"},
		{"java", "openjdk:11-jre-slim"},
		{"unknown", "alpine:latest"},
	}

	for _, tt := range tests {
		t.Run(tt.runtime, func(t *testing.T) {
			got := executor.getRuntimeImage(tt.runtime)
			assert.Equal(t, tt.want, got)
		})
	}
}

// TestGetFileExtension 测试获取文件扩展名
func TestGetFileExtension(t *testing.T) {
	mockContainerManager := new(MockContainerManager)
	mockLogger := logger.NewConsoleLogger("test")
	executor := NewFunctionExecutor(mockContainerManager, nil, mockLogger)

	tests := []struct {
		runtime string
		want    string
	}{
		{"nodejs", "js"},
		{"python", "py"},
		{"go", "go"},
		{"java", "java"},
		{"unknown", "txt"},
	}

	for _, tt := range tests {
		t.Run(tt.runtime, func(t *testing.T) {
			got := executor.getFileExtension(tt.runtime)
			assert.Equal(t, tt.want, got)
		})
	}
}

// TestBuildContainerConfig 测试构建容器配置
func TestBuildContainerConfig(t *testing.T) {
	mockContainerManager := new(MockContainerManager)
	mockLogger := logger.NewConsoleLogger("test")
	executor := NewFunctionExecutor(mockContainerManager, nil, mockLogger)

	request := &FunctionRequest{
		FunctionID:   "test-func-1",
		FunctionName: "testFunction",
		Runtime:      "nodejs",
		Code:         "console.log('Hello World');",
		Handler:      "index.handler",
		Environment: map[string]string{
			"NODE_ENV": "production",
			"DEBUG":    "true",
		},
		MemoryLimit: "256m",
	}

	config := executor.buildContainerConfig(request)

	assert.Equal(t, "node:18-alpine", config.Image)
	assert.Contains(t, config.Name, "faas-test-func-1")
	assert.Equal(t, "/app", config.WorkingDir)
	assert.Equal(t, "256m", config.Resources.MemoryLimit)
	assert.Equal(t, "0.5", config.Resources.CPULimit)
	assert.False(t, config.AutoRemove)

	// 检查环境变量
	envMap := make(map[string]string)
	for _, env := range config.Env {
		// 解析 KEY=VALUE 格式
		if len(env) > 0 {
			parts := []string{env[:len(env)/2], env[len(env)/2:]}
			if len(parts) == 2 {
				envMap[parts[0]] = parts[1]
			}
		}
	}

	// 检查标签
	assert.Equal(t, "test-func-1", config.Labels["paas.function.id"])
	assert.Equal(t, "testFunction", config.Labels["paas.function.name"])
	assert.Equal(t, "nodejs", config.Labels["paas.runtime"])
	assert.Equal(t, "faas", config.Labels["paas.type"])
}

// TestUpdateMetrics 测试指标更新
func TestUpdateMetrics(t *testing.T) {
	mockContainerManager := new(MockContainerManager)
	mockLogger := logger.NewConsoleLogger("test")
	executor := NewFunctionExecutor(mockContainerManager, nil, mockLogger)

	// 初始指标
	metrics := executor.GetMetrics()
	assert.Equal(t, int64(0), metrics.TotalExecutions)
	assert.Equal(t, int64(0), metrics.SuccessfulExecutions)
	assert.Equal(t, int64(0), metrics.FailedExecutions)

	// 模拟成功执行
	successResponse := &FunctionResponse{
		Status: "success",
	}
	executor.updateMetrics(successResponse, nil, 100*time.Millisecond)

	metrics = executor.GetMetrics()
	assert.Equal(t, int64(1), metrics.TotalExecutions)
	assert.Equal(t, int64(1), metrics.SuccessfulExecutions)
	assert.Equal(t, int64(0), metrics.FailedExecutions)
	assert.Equal(t, 100*time.Millisecond, metrics.AverageExecutionTime)

	// 模拟失败执行
	errorResponse := &FunctionResponse{
		Status: "error",
	}
	executor.updateMetrics(errorResponse, nil, 200*time.Millisecond)

	metrics = executor.GetMetrics()
	assert.Equal(t, int64(2), metrics.TotalExecutions)
	assert.Equal(t, int64(1), metrics.SuccessfulExecutions)
	assert.Equal(t, int64(1), metrics.FailedExecutions)
	assert.Equal(t, 150*time.Millisecond, metrics.AverageExecutionTime)
}

// TestExecutionContext 测试执行上下文
func TestExecutionContext(t *testing.T) {
	request := &FunctionRequest{
		FunctionID:   "test-func-1",
		FunctionName: "testFunction",
		Runtime:      "nodejs",
		Code:         "console.log('Hello World');",
	}

	execCtx := &ExecutionContext{
		Request:   request,
		StartTime: time.Now(),
		Timeout:   30 * time.Second,
		Logs:      make([]string, 0),
	}

	// 测试添加日志
	execCtx.addLog("测试日志消息")
	assert.Len(t, execCtx.Logs, 1)
	assert.Contains(t, execCtx.Logs[0], "测试日志消息")

	// 测试并发添加日志
	go execCtx.addLog("并发日志1")
	go execCtx.addLog("并发日志2")
	
	time.Sleep(10 * time.Millisecond) // 等待goroutine完成
	assert.Len(t, execCtx.Logs, 3)
}
