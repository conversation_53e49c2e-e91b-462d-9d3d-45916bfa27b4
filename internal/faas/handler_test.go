package faas

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"paas-platform/pkg/logger"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockFunctionExecutor 模拟函数执行器
type MockFunctionExecutor struct {
	mock.Mock
}

func (m *MockFunctionExecutor) ExecuteFunction(ctx context.Context, request *FunctionRequest) (*FunctionResponse, error) {
	args := m.Called(ctx, request)
	return args.Get(0).(*FunctionResponse), args.Error(1)
}

func (m *MockFunctionExecutor) GetMetrics() *ExecutorMetrics {
	args := m.Called()
	return args.Get(0).(*ExecutorMetrics)
}

func (m *MockFunctionExecutor) GetContainerPoolStats() map[string]interface{} {
	args := m.Called()
	return args.Get(0).(map[string]interface{})
}

// setupTestRouter 设置测试路由
func setupTestRouter(handler *Handler) *gin.Engine {
	gin.SetMode(gin.TestMode)
	router := gin.New()
	
	v1 := router.Group("/api/v1")
	faasGroup := v1.Group("/faas")
	handler.RegisterRoutes(faasGroup)
	
	return router
}

// TestExecuteFunction 测试同步执行函数
func TestExecuteFunction(t *testing.T) {
	mockExecutor := &MockFunctionExecutor{}
	logger := logger.NewLogger()
	handler := NewHandler(mockExecutor, logger)
	router := setupTestRouter(handler)

	tests := []struct {
		name           string
		requestBody    interface{}
		mockResponse   *FunctionResponse
		mockError      error
		expectedStatus int
	}{
		{
			name: "成功执行函数",
			requestBody: FunctionRequest{
				FunctionID:   "test-func",
				FunctionName: "Test Function",
				Runtime:      "nodejs",
				Code:         "function main() { return 'hello'; }",
				Handler:      "main",
			},
			mockResponse: &FunctionResponse{
				RequestID:     "req-123",
				FunctionID:    "test-func",
				Status:        "success",
				Result:        "hello",
				ExecutionTime: 100 * time.Millisecond,
			},
			mockError:      nil,
			expectedStatus: http.StatusOK,
		},
		{
			name: "执行失败",
			requestBody: FunctionRequest{
				FunctionID:   "test-func",
				FunctionName: "Test Function",
				Runtime:      "nodejs",
				Code:         "function main() { throw new Error('test error'); }",
				Handler:      "main",
			},
			mockResponse:   nil,
			mockError:      assert.AnError,
			expectedStatus: http.StatusInternalServerError,
		},
		{
			name: "无效请求",
			requestBody: map[string]interface{}{
				"invalid": "request",
			},
			mockResponse:   nil,
			mockError:      nil,
			expectedStatus: http.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置 mock 期望
			if tt.mockResponse != nil || tt.mockError != nil {
				mockExecutor.On("ExecuteFunction", mock.Anything, mock.AnythingOfType("*faas.FunctionRequest")).
					Return(tt.mockResponse, tt.mockError).Once()
			}

			// 准备请求
			body, _ := json.Marshal(tt.requestBody)
			req, _ := http.NewRequest("POST", "/api/v1/faas/functions/execute", bytes.NewBuffer(body))
			req.Header.Set("Content-Type", "application/json")

			// 执行请求
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				var response FunctionResponse
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.mockResponse.Status, response.Status)
				assert.Equal(t, tt.mockResponse.Result, response.Result)
			}

			// 验证 mock 调用
			mockExecutor.AssertExpectations(t)
		})
	}
}

// TestAsyncExecuteFunction 测试异步执行函数
func TestAsyncExecuteFunction(t *testing.T) {
	mockExecutor := &MockFunctionExecutor{}
	logger := logger.NewLogger()
	handler := NewHandler(mockExecutor, logger)
	router := setupTestRouter(handler)

	// 设置 mock 期望
	mockExecutor.On("ExecuteFunction", mock.Anything, mock.AnythingOfType("*faas.FunctionRequest")).
		Return(&FunctionResponse{
			Status: "success",
			Result: "hello",
		}, nil).Once()

	// 准备请求
	requestBody := FunctionRequest{
		FunctionID:   "test-func",
		FunctionName: "Test Function",
		Runtime:      "nodejs",
		Code:         "function main() { return 'hello'; }",
		Handler:      "main",
	}
	body, _ := json.Marshal(requestBody)
	req, _ := http.NewRequest("POST", "/api/v1/faas/functions/async-execute", bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")

	// 执行请求
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// 验证响应
	assert.Equal(t, http.StatusAccepted, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "函数执行请求已接受", response["message"])
	assert.Equal(t, "accepted", response["status"])
	assert.NotEmpty(t, response["request_id"])

	// 等待异步执行完成
	time.Sleep(100 * time.Millisecond)

	// 验证 mock 调用
	mockExecutor.AssertExpectations(t)
}

// TestGetExecutorMetrics 测试获取执行器指标
func TestGetExecutorMetrics(t *testing.T) {
	mockExecutor := &MockFunctionExecutor{}
	logger := logger.NewLogger()
	handler := NewHandler(mockExecutor, logger)
	router := setupTestRouter(handler)

	// 设置 mock 期望
	expectedMetrics := &ExecutorMetrics{
		TotalExecutions:      100,
		SuccessfulExecutions: 95,
		FailedExecutions:     5,
		AverageExecutionTime: 150 * time.Millisecond,
		ActiveExecutions:     3,
	}
	mockExecutor.On("GetMetrics").Return(expectedMetrics).Once()

	// 执行请求
	req, _ := http.NewRequest("GET", "/api/v1/faas/functions/metrics", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// 验证响应
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.NotNil(t, response["metrics"])
	assert.NotNil(t, response["timestamp"])

	// 验证 mock 调用
	mockExecutor.AssertExpectations(t)
}

// TestGetPoolStats 测试获取容器池统计
func TestGetPoolStats(t *testing.T) {
	mockExecutor := &MockFunctionExecutor{}
	logger := logger.NewLogger()
	handler := NewHandler(mockExecutor, logger)
	router := setupTestRouter(handler)

	// 创建一个包含 GetPoolStats 方法的 mock
	type MockExecutorWithPool struct {
		MockFunctionExecutor
		containerPool *MockContainerPool
	}

	mockPool := &MockContainerPool{}
	mockExecWithPool := &MockExecutorWithPool{
		MockFunctionExecutor: *mockExecutor,
		containerPool:        mockPool,
	}

	// 重新创建 handler
	handler = &Handler{
		executor: mockExecWithPool,
		logger:   logger,
	}
	router = setupTestRouter(handler)

	// 设置 mock 期望
	expectedStats := map[string]interface{}{
		"total_containers": 5,
		"max_size":        10,
		"runtime_distribution": map[string]int{
			"nodejs": 2,
			"python": 3,
		},
	}
	mockPool.On("GetPoolStats").Return(expectedStats).Once()

	// 执行请求
	req, _ := http.NewRequest("GET", "/api/v1/faas/functions/pool/stats", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// 验证响应
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.NotNil(t, response["pool_stats"])
	assert.NotNil(t, response["timestamp"])

	// 验证 mock 调用
	mockPool.AssertExpectations(t)
}

// TestHealthCheck 测试健康检查
func TestHealthCheck(t *testing.T) {
	mockExecutor := &MockFunctionExecutor{}
	logger := logger.NewLogger()
	handler := NewHandler(mockExecutor, logger)
	router := setupTestRouter(handler)

	// 设置 mock 期望
	expectedMetrics := &ExecutorMetrics{
		TotalExecutions:      100,
		SuccessfulExecutions: 95,
		FailedExecutions:     5,
		AverageExecutionTime: 150 * time.Millisecond,
		ActiveExecutions:     3,
	}
	mockExecutor.On("GetMetrics").Return(expectedMetrics).Once()

	// 创建一个包含 GetPoolStats 方法的 mock
	type MockExecutorWithPool struct {
		MockFunctionExecutor
		containerPool *MockContainerPool
	}

	mockPool := &MockContainerPool{}
	mockExecWithPool := &MockExecutorWithPool{
		MockFunctionExecutor: *mockExecutor,
		containerPool:        mockPool,
	}

	// 重新创建 handler
	handler = &Handler{
		executor: mockExecWithPool,
		logger:   logger,
	}
	router = setupTestRouter(handler)

	expectedStats := map[string]interface{}{
		"total_containers": 5,
		"max_size":        10,
	}
	mockPool.On("GetPoolStats").Return(expectedStats).Once()

	// 执行请求
	req, _ := http.NewRequest("GET", "/api/v1/faas/functions/health", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// 验证响应
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "healthy", response["status"])
	assert.Equal(t, "faas-executor", response["service"])
	assert.NotNil(t, response["metrics"])
	assert.NotNil(t, response["container_pool"])

	// 验证 mock 调用
	mockExecutor.AssertExpectations(t)
	mockPool.AssertExpectations(t)
}

// MockContainerPool 模拟容器池
type MockContainerPool struct {
	mock.Mock
}

func (m *MockContainerPool) GetPoolStats() map[string]interface{} {
	args := m.Called()
	return args.Get(0).(map[string]interface{})
}
