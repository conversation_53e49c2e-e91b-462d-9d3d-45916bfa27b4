package auth

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// AdminHandler 管理员认证处理器
// 专门处理系统管理员的本地认证，与普通用户的 IDP 认证分离
type AdminHandler struct {
	authService Service
	jwtService  JWTService
	logger      Logger
}

// NewAdminHandler 创建管理员认证处理器
func NewAdminHandler(authService Service, jwtService JWTService, logger Logger) *AdminHandler {
	return &AdminHandler{
		authService: authService,
		jwtService:  jwtService,
		logger:      logger,
	}
}

// RegisterRoutes 注册管理员认证路由
func (h *AdminHandler) RegisterRoutes(router *gin.RouterGroup) {
	// 管理员专用认证路由
	admin := router.Group("/admin")
	{
		// 管理员登录（本地认证）
		admin.POST("/login", h.AdminLogin)
		
		// 管理员登出
		admin.POST("/logout", h.AdminLogout)
		
		// 管理员密码修改
		admin.POST("/change-password", h.AdminChangePassword)
		
		// 管理员账号管理
		admin.POST("/create-admin", h.CreateAdminUser)
		admin.GET("/admins", h.ListAdminUsers)
		admin.PUT("/admins/:id", h.UpdateAdminUser)
		admin.DELETE("/admins/:id", h.DeleteAdminUser)
	}
}

// AdminLoginRequest 管理员登录请求
type AdminLoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
	TenantID string `json:"tenant_id" binding:"required"`
}

// AdminLoginResponse 管理员登录响应
type AdminLoginResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int64  `json:"expires_in"`
	TokenType    string `json:"token_type"`
	User         *User  `json:"user"`
}

// AdminLogin 管理员登录
// @Summary 管理员登录
// @Description 系统管理员使用用户名密码进行本地认证登录
// @Tags 管理员认证
// @Accept json
// @Produce json
// @Param request body AdminLoginRequest true "管理员登录请求"
// @Success 200 {object} AdminLoginResponse "登录成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 401 {object} ErrorResponse "认证失败"
// @Failure 403 {object} ErrorResponse "权限不足"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/admin/login [post]
func (h *AdminHandler) AdminLogin(c *gin.Context) {
	var req AdminLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定管理员登录请求失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:      "INVALID_REQUEST",
			Message:   "请求参数格式错误",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	// 构造登录请求
	loginReq := &LoginRequest{
		Username: req.Username,
		Password: req.Password,
		TenantID: req.TenantID,
	}

	// 调用认证服务
	response, err := h.authService.Login(c.Request.Context(), loginReq)
	if err != nil {
		h.logger.Error("管理员登录失败", "error", err, "username", req.Username)
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Code:      "LOGIN_FAILED",
			Message:   "登录失败",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	// 验证用户是否为管理员
	if response.User.UserType != UserTypeAdmin {
		h.logger.Warn("非管理员用户尝试管理员登录", "user_id", response.User.ID, "user_type", response.User.UserType)
		c.JSON(http.StatusForbidden, ErrorResponse{
			Code:      "ACCESS_DENIED",
			Message:   "访问被拒绝，仅限系统管理员",
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	// 构造管理员登录响应
	adminResponse := &AdminLoginResponse{
		AccessToken:  response.AccessToken,
		RefreshToken: response.RefreshToken,
		ExpiresIn:    response.ExpiresIn,
		TokenType:    response.TokenType,
		User:         response.User,
	}

	h.logger.Info("管理员登录成功", "admin_id", response.User.ID, "username", response.User.Username)
	c.JSON(http.StatusOK, adminResponse)
}

// AdminLogout 管理员登出
// @Summary 管理员登出
// @Description 管理员登出，清除会话
// @Tags 管理员认证
// @Accept json
// @Produce json
// @Success 200 {object} map[string]string "登出成功"
// @Failure 401 {object} ErrorResponse "未授权"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/admin/logout [post]
func (h *AdminHandler) AdminLogout(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Code:      "UNAUTHORIZED",
			Message:   "未授权访问",
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	sessionID, _ := c.Get("session_id")

	// 调用认证服务登出
	err := h.authService.Logout(c.Request.Context(), userID.(string), sessionID.(string))
	if err != nil {
		h.logger.Error("管理员登出失败", "error", err, "user_id", userID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:      "LOGOUT_FAILED",
			Message:   "登出失败",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	h.logger.Info("管理员登出成功", "admin_id", userID)
	c.JSON(http.StatusOK, gin.H{
		"message": "登出成功",
	})
}

// CreateAdminUserRequest 创建管理员用户请求
type CreateAdminUserRequest struct {
	Username  string `json:"username" binding:"required"`
	Email     string `json:"email" binding:"required,email"`
	Password  string `json:"password" binding:"required,min=8"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
	TenantID  string `json:"tenant_id" binding:"required"`
}

// CreateAdminUser 创建管理员用户
// @Summary 创建管理员用户
// @Description 创建新的系统管理员账号
// @Tags 管理员管理
// @Accept json
// @Produce json
// @Param request body CreateAdminUserRequest true "创建管理员请求"
// @Success 201 {object} User "创建成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 403 {object} ErrorResponse "权限不足"
// @Failure 409 {object} ErrorResponse "用户已存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/admin/create-admin [post]
func (h *AdminHandler) CreateAdminUser(c *gin.Context) {
	var req CreateAdminUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定创建管理员请求失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:      "INVALID_REQUEST",
			Message:   "请求参数格式错误",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	// 验证当前用户是否为管理员
	userType, exists := c.Get("user_type")
	if !exists || userType != UserTypeAdmin {
		c.JSON(http.StatusForbidden, ErrorResponse{
			Code:      "ACCESS_DENIED",
			Message:   "仅限系统管理员操作",
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	// 构造注册请求
	registerReq := &RegisterRequest{
		Username:  req.Username,
		Email:     req.Email,
		Password:  req.Password,
		FirstName: req.FirstName,
		LastName:  req.LastName,
		TenantID:  req.TenantID,
	}

	// 调用认证服务注册
	user, err := h.authService.Register(c.Request.Context(), registerReq)
	if err != nil {
		h.logger.Error("创建管理员用户失败", "error", err, "username", req.Username)
		
		statusCode := http.StatusInternalServerError
		errorCode := "CREATE_FAILED"
		
		if err.Error() == "用户名已存在" || err.Error() == "邮箱已存在" {
			statusCode = http.StatusConflict
			errorCode = "USER_EXISTS"
		}
		
		c.JSON(statusCode, ErrorResponse{
			Code:      errorCode,
			Message:   "创建管理员用户失败",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	// 更新用户类型为管理员
	user.UserType = UserTypeAdmin
	// 这里需要调用用户服务更新用户类型
	// TODO: 实现用户类型更新逻辑

	h.logger.Info("管理员用户创建成功", "admin_id", user.ID, "username", user.Username)
	c.JSON(http.StatusCreated, user)
}

// AdminChangePassword 管理员修改密码
func (h *AdminHandler) AdminChangePassword(c *gin.Context) {
	// TODO: 实现管理员密码修改逻辑
	c.JSON(http.StatusNotImplemented, gin.H{
		"message": "功能开发中",
	})
}

// ListAdminUsers 获取管理员用户列表
func (h *AdminHandler) ListAdminUsers(c *gin.Context) {
	// TODO: 实现管理员用户列表查询
	c.JSON(http.StatusNotImplemented, gin.H{
		"message": "功能开发中",
	})
}

// UpdateAdminUser 更新管理员用户
func (h *AdminHandler) UpdateAdminUser(c *gin.Context) {
	// TODO: 实现管理员用户更新
	c.JSON(http.StatusNotImplemented, gin.H{
		"message": "功能开发中",
	})
}

// DeleteAdminUser 删除管理员用户
func (h *AdminHandler) DeleteAdminUser(c *gin.Context) {
	// TODO: 实现管理员用户删除
	c.JSON(http.StatusNotImplemented, gin.H{
		"message": "功能开发中",
	})
}
