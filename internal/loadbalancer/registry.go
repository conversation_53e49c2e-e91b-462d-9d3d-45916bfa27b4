package loadbalancer

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"paas-platform/pkg/logger"
)

// ServiceRegistry 服务注册中心
type ServiceRegistry struct {
	services      map[string]*ServiceGroup
	mutex         sync.RWMutex
	logger        logger.Logger
	healthChecker *HealthChecker
	ctx           context.Context
	cancel        context.CancelFunc
}

// ServiceGroup 服务组
type ServiceGroup struct {
	Name      string              `json:"name"`
	Instances []*ServiceInstance  `json:"instances"`
	Config    *LoadBalancerConfig `json:"config"`
	mutex     sync.RWMutex
}

// ServiceInstance 服务实例
type ServiceInstance struct {
	ID       string            `json:"id"`
	Name     string            `json:"name"`
	Host     string            `json:"host"`
	Port     int               `json:"port"`
	Protocol string            `json:"protocol"`
	Path     string            `json:"path"`
	Weight   int               `json:"weight"`
	Status   string            `json:"status"` // healthy, unhealthy, unknown
	Metadata map[string]string `json:"metadata"`
	
	// 健康检查相关
	HealthCheckURL      string        `json:"health_check_url"`
	HealthCheckInterval time.Duration `json:"health_check_interval"`
	HealthCheckTimeout  time.Duration `json:"health_check_timeout"`
	
	// 统计信息
	RequestCount    int64     `json:"request_count"`
	ErrorCount      int64     `json:"error_count"`
	LastRequestTime time.Time `json:"last_request_time"`
	RegisterTime    time.Time `json:"register_time"`
	LastHealthCheck time.Time `json:"last_health_check"`
}

// LoadBalancerConfig 负载均衡配置
type LoadBalancerConfig struct {
	Algorithm           string        `json:"algorithm"`            // round_robin, weighted_round_robin, least_connections, ip_hash
	HealthCheckEnabled  bool          `json:"health_check_enabled"`
	HealthCheckInterval time.Duration `json:"health_check_interval"`
	HealthCheckTimeout  time.Duration `json:"health_check_timeout"`
	HealthCheckPath     string        `json:"health_check_path"`
	MaxRetries          int           `json:"max_retries"`
	RetryTimeout        time.Duration `json:"retry_timeout"`
	SessionSticky       bool          `json:"session_sticky"`
	CircuitBreaker      bool          `json:"circuit_breaker"`
}

// RegistrationRequest 服务注册请求
type RegistrationRequest struct {
	ServiceName string            `json:"service_name" binding:"required"`
	InstanceID  string            `json:"instance_id" binding:"required"`
	Host        string            `json:"host" binding:"required"`
	Port        int               `json:"port" binding:"required,min=1,max=65535"`
	Protocol    string            `json:"protocol"`
	Path        string            `json:"path"`
	Weight      int               `json:"weight"`
	Metadata    map[string]string `json:"metadata"`
	
	// 健康检查配置
	HealthCheckURL      string `json:"health_check_url"`
	HealthCheckInterval string `json:"health_check_interval"`
	HealthCheckTimeout  string `json:"health_check_timeout"`
}

// NewServiceRegistry 创建服务注册中心
func NewServiceRegistry(logger logger.Logger) *ServiceRegistry {
	ctx, cancel := context.WithCancel(context.Background())
	
	registry := &ServiceRegistry{
		services: make(map[string]*ServiceGroup),
		logger:   logger,
		ctx:      ctx,
		cancel:   cancel,
	}
	
	// 初始化健康检查器
	registry.healthChecker = NewHealthChecker(registry, logger)
	
	return registry
}

// Start 启动服务注册中心
func (sr *ServiceRegistry) Start() {
	sr.logger.Info("启动服务注册中心")
	
	// 启动健康检查
	go sr.healthChecker.Start()
	
	// 启动清理任务
	go sr.startCleanupTask()
}

// Stop 停止服务注册中心
func (sr *ServiceRegistry) Stop() {
	sr.logger.Info("停止服务注册中心")
	sr.cancel()
	sr.healthChecker.Stop()
}

// RegisterService 注册服务
func (sr *ServiceRegistry) RegisterService(req *RegistrationRequest) error {
	sr.mutex.Lock()
	defer sr.mutex.Unlock()

	// 验证请求
	if err := sr.validateRegistrationRequest(req); err != nil {
		return fmt.Errorf("注册请求验证失败: %w", err)
	}

	// 获取或创建服务组
	serviceGroup, exists := sr.services[req.ServiceName]
	if !exists {
		serviceGroup = &ServiceGroup{
			Name:      req.ServiceName,
			Instances: make([]*ServiceInstance, 0),
			Config:    sr.getDefaultConfig(),
		}
		sr.services[req.ServiceName] = serviceGroup
	}

	// 解析健康检查间隔
	healthCheckInterval, _ := time.ParseDuration(req.HealthCheckInterval)
	if healthCheckInterval == 0 {
		healthCheckInterval = 30 * time.Second
	}

	healthCheckTimeout, _ := time.ParseDuration(req.HealthCheckTimeout)
	if healthCheckTimeout == 0 {
		healthCheckTimeout = 5 * time.Second
	}

	// 创建服务实例
	instance := &ServiceInstance{
		ID:                  req.InstanceID,
		Name:                req.ServiceName,
		Host:                req.Host,
		Port:                req.Port,
		Protocol:            req.Protocol,
		Path:                req.Path,
		Weight:              req.Weight,
		Status:              "unknown",
		Metadata:            req.Metadata,
		HealthCheckURL:      req.HealthCheckURL,
		HealthCheckInterval: healthCheckInterval,
		HealthCheckTimeout:  healthCheckTimeout,
		RegisterTime:        time.Now(),
	}

	// 设置默认值
	if instance.Protocol == "" {
		instance.Protocol = "http"
	}
	if instance.Weight <= 0 {
		instance.Weight = 1
	}
	if instance.Metadata == nil {
		instance.Metadata = make(map[string]string)
	}

	// 构建健康检查 URL
	if instance.HealthCheckURL == "" {
		instance.HealthCheckURL = fmt.Sprintf("%s://%s:%d/health", 
			instance.Protocol, instance.Host, instance.Port)
	}

	// 检查是否已存在相同实例
	serviceGroup.mutex.Lock()
	for i, existingInstance := range serviceGroup.Instances {
		if existingInstance.ID == instance.ID {
			// 更新现有实例
			serviceGroup.Instances[i] = instance
			serviceGroup.mutex.Unlock()
			sr.logger.Info("更新服务实例", "service", req.ServiceName, "instance", req.InstanceID)
			return nil
		}
	}

	// 添加新实例
	serviceGroup.Instances = append(serviceGroup.Instances, instance)
	serviceGroup.mutex.Unlock()

	sr.logger.Info("注册服务实例", "service", req.ServiceName, "instance", req.InstanceID, 
		"endpoint", fmt.Sprintf("%s:%d", req.Host, req.Port))

	return nil
}

// DeregisterService 注销服务
func (sr *ServiceRegistry) DeregisterService(serviceName, instanceID string) error {
	sr.mutex.Lock()
	defer sr.mutex.Unlock()

	serviceGroup, exists := sr.services[serviceName]
	if !exists {
		return fmt.Errorf("服务不存在: %s", serviceName)
	}

	serviceGroup.mutex.Lock()
	defer serviceGroup.mutex.Unlock()

	// 查找并移除实例
	for i, instance := range serviceGroup.Instances {
		if instance.ID == instanceID {
			serviceGroup.Instances = append(serviceGroup.Instances[:i], serviceGroup.Instances[i+1:]...)
			sr.logger.Info("注销服务实例", "service", serviceName, "instance", instanceID)
			
			// 如果服务组没有实例了，删除服务组
			if len(serviceGroup.Instances) == 0 {
				delete(sr.services, serviceName)
				sr.logger.Info("删除空服务组", "service", serviceName)
			}
			
			return nil
		}
	}

	return fmt.Errorf("服务实例不存在: %s/%s", serviceName, instanceID)
}

// GetService 获取服务
func (sr *ServiceRegistry) GetService(serviceName string) (*ServiceGroup, error) {
	sr.mutex.RLock()
	defer sr.mutex.RUnlock()

	serviceGroup, exists := sr.services[serviceName]
	if !exists {
		return nil, fmt.Errorf("服务不存在: %s", serviceName)
	}

	// 返回副本以避免并发修改
	serviceGroup.mutex.RLock()
	defer serviceGroup.mutex.RUnlock()

	copyGroup := &ServiceGroup{
		Name:      serviceGroup.Name,
		Instances: make([]*ServiceInstance, len(serviceGroup.Instances)),
		Config:    serviceGroup.Config,
	}

	for i, instance := range serviceGroup.Instances {
		instanceCopy := *instance
		copyGroup.Instances[i] = &instanceCopy
	}

	return copyGroup, nil
}

// GetHealthyInstances 获取健康的服务实例
func (sr *ServiceRegistry) GetHealthyInstances(serviceName string) ([]*ServiceInstance, error) {
	serviceGroup, err := sr.GetService(serviceName)
	if err != nil {
		return nil, err
	}

	var healthyInstances []*ServiceInstance
	for _, instance := range serviceGroup.Instances {
		if instance.Status == "healthy" {
			healthyInstances = append(healthyInstances, instance)
		}
	}

	return healthyInstances, nil
}

// ListServices 列出所有服务
func (sr *ServiceRegistry) ListServices() map[string]*ServiceGroup {
	sr.mutex.RLock()
	defer sr.mutex.RUnlock()

	result := make(map[string]*ServiceGroup)
	for name, serviceGroup := range sr.services {
		serviceGroup.mutex.RLock()
		copyGroup := &ServiceGroup{
			Name:      serviceGroup.Name,
			Instances: make([]*ServiceInstance, len(serviceGroup.Instances)),
			Config:    serviceGroup.Config,
		}

		for i, instance := range serviceGroup.Instances {
			instanceCopy := *instance
			copyGroup.Instances[i] = &instanceCopy
		}
		serviceGroup.mutex.RUnlock()

		result[name] = copyGroup
	}

	return result
}

// UpdateInstanceStatus 更新实例状态
func (sr *ServiceRegistry) UpdateInstanceStatus(serviceName, instanceID, status string) error {
	sr.mutex.RLock()
	serviceGroup, exists := sr.services[serviceName]
	sr.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("服务不存在: %s", serviceName)
	}

	serviceGroup.mutex.Lock()
	defer serviceGroup.mutex.Unlock()

	for _, instance := range serviceGroup.Instances {
		if instance.ID == instanceID {
			oldStatus := instance.Status
			instance.Status = status
			instance.LastHealthCheck = time.Now()
			
			if oldStatus != status {
				sr.logger.Info("实例状态变更", "service", serviceName, "instance", instanceID, 
					"old_status", oldStatus, "new_status", status)
			}
			
			return nil
		}
	}

	return fmt.Errorf("服务实例不存在: %s/%s", serviceName, instanceID)
}

// UpdateInstanceStats 更新实例统计信息
func (sr *ServiceRegistry) UpdateInstanceStats(serviceName, instanceID string, requestCount, errorCount int64) error {
	sr.mutex.RLock()
	serviceGroup, exists := sr.services[serviceName]
	sr.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("服务不存在: %s", serviceName)
	}

	serviceGroup.mutex.Lock()
	defer serviceGroup.mutex.Unlock()

	for _, instance := range serviceGroup.Instances {
		if instance.ID == instanceID {
			instance.RequestCount += requestCount
			instance.ErrorCount += errorCount
			instance.LastRequestTime = time.Now()
			return nil
		}
	}

	return fmt.Errorf("服务实例不存在: %s/%s", serviceName, instanceID)
}

// validateRegistrationRequest 验证注册请求
func (sr *ServiceRegistry) validateRegistrationRequest(req *RegistrationRequest) error {
	if req.ServiceName == "" {
		return fmt.Errorf("服务名称不能为空")
	}
	if req.InstanceID == "" {
		return fmt.Errorf("实例ID不能为空")
	}
	if req.Host == "" {
		return fmt.Errorf("主机地址不能为空")
	}
	if req.Port <= 0 || req.Port > 65535 {
		return fmt.Errorf("端口号必须在1-65535之间")
	}
	return nil
}

// getDefaultConfig 获取默认配置
func (sr *ServiceRegistry) getDefaultConfig() *LoadBalancerConfig {
	return &LoadBalancerConfig{
		Algorithm:           "round_robin",
		HealthCheckEnabled:  true,
		HealthCheckInterval: 30 * time.Second,
		HealthCheckTimeout:  5 * time.Second,
		HealthCheckPath:     "/health",
		MaxRetries:          3,
		RetryTimeout:        1 * time.Second,
		SessionSticky:       false,
		CircuitBreaker:      false,
	}
}

// startCleanupTask 启动清理任务
func (sr *ServiceRegistry) startCleanupTask() {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-sr.ctx.Done():
			return
		case <-ticker.C:
			sr.cleanupStaleInstances()
		}
	}
}

// cleanupStaleInstances 清理过期实例
func (sr *ServiceRegistry) cleanupStaleInstances() {
	sr.mutex.Lock()
	defer sr.mutex.Unlock()

	staleThreshold := 10 * time.Minute
	now := time.Now()

	for serviceName, serviceGroup := range sr.services {
		serviceGroup.mutex.Lock()
		
		var activeInstances []*ServiceInstance
		for _, instance := range serviceGroup.Instances {
			// 如果实例长时间没有健康检查更新，认为是过期的
			if instance.LastHealthCheck.IsZero() || now.Sub(instance.LastHealthCheck) < staleThreshold {
				activeInstances = append(activeInstances, instance)
			} else {
				sr.logger.Warn("清理过期实例", "service", serviceName, "instance", instance.ID)
			}
		}
		
		serviceGroup.Instances = activeInstances
		
		// 如果服务组没有实例了，删除服务组
		if len(serviceGroup.Instances) == 0 {
			delete(sr.services, serviceName)
			sr.logger.Info("删除空服务组", "service", serviceName)
		}
		
		serviceGroup.mutex.Unlock()
	}
}

// GetServiceStats 获取服务统计信息
func (sr *ServiceRegistry) GetServiceStats() map[string]interface{} {
	sr.mutex.RLock()
	defer sr.mutex.RUnlock()

	stats := map[string]interface{}{
		"total_services":  len(sr.services),
		"total_instances": 0,
		"healthy_instances": 0,
		"unhealthy_instances": 0,
		"services": make(map[string]interface{}),
	}

	totalInstances := 0
	healthyInstances := 0
	unhealthyInstances := 0

	for serviceName, serviceGroup := range sr.services {
		serviceGroup.mutex.RLock()
		
		serviceStats := map[string]interface{}{
			"instance_count": len(serviceGroup.Instances),
			"healthy_count":  0,
			"unhealthy_count": 0,
			"instances": make([]map[string]interface{}, 0),
		}

		serviceHealthy := 0
		serviceUnhealthy := 0

		for _, instance := range serviceGroup.Instances {
			instanceStats := map[string]interface{}{
				"id":               instance.ID,
				"endpoint":         fmt.Sprintf("%s:%d", instance.Host, instance.Port),
				"status":           instance.Status,
				"weight":           instance.Weight,
				"request_count":    instance.RequestCount,
				"error_count":      instance.ErrorCount,
				"last_request":     instance.LastRequestTime,
				"last_health_check": instance.LastHealthCheck,
				"register_time":    instance.RegisterTime,
			}
			serviceStats["instances"] = append(serviceStats["instances"].([]map[string]interface{}), instanceStats)

			if instance.Status == "healthy" {
				serviceHealthy++
				healthyInstances++
			} else {
				serviceUnhealthy++
				unhealthyInstances++
			}
			totalInstances++
		}

		serviceStats["healthy_count"] = serviceHealthy
		serviceStats["unhealthy_count"] = serviceUnhealthy
		stats["services"].(map[string]interface{})[serviceName] = serviceStats
		
		serviceGroup.mutex.RUnlock()
	}

	stats["total_instances"] = totalInstances
	stats["healthy_instances"] = healthyInstances
	stats["unhealthy_instances"] = unhealthyInstances

	return stats
}

// UpdateInstanceHealth 更新实例健康状态
func (sr *ServiceRegistry) UpdateInstanceHealth(serviceName, instanceID string, healthy bool) error {
	sr.mutex.RLock()
	serviceGroup, exists := sr.services[serviceName]
	sr.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("服务不存在: %s", serviceName)
	}

	serviceGroup.mutex.Lock()
	defer serviceGroup.mutex.Unlock()

	// 查找实例并更新健康状态
	for _, instance := range serviceGroup.Instances {
		if instance.ID == instanceID {
			if healthy {
				instance.Status = "healthy"
				instance.LastHealthCheck = time.Now()
				instance.FailureCount = 0
			} else {
				instance.Status = "unhealthy"
				instance.FailureCount++
			}

			sr.logger.Debug("更新实例健康状态",
				"service", serviceName,
				"instance", instanceID,
				"healthy", healthy,
				"status", instance.Status)

			return nil
		}
	}

	return fmt.Errorf("实例不存在: %s", instanceID)
}
