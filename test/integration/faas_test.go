package integration

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"

	"paas-platform/internal/faas"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

// FaaSIntegrationTestSuite FaaS 集成测试套件
type FaaSIntegrationTestSuite struct {
	suite.Suite
	baseURL string
	client  *http.Client
}

// SetupSuite 设置测试套件
func (suite *FaaSIntegrationTestSuite) SetupSuite() {
	suite.baseURL = "http://localhost:8087/api/v1/faas"
	suite.client = &http.Client{
		Timeout: 30 * time.Second,
	}
	
	// 等待服务启动
	suite.waitForService()
}

// waitForService 等待服务启动
func (suite *FaaSIntegrationTestSuite) waitForService() {
	maxRetries := 30
	for i := 0; i < maxRetries; i++ {
		resp, err := suite.client.Get(suite.baseURL + "/functions/health")
		if err == nil && resp.StatusCode == http.StatusOK {
			resp.Body.Close()
			return
		}
		if resp != nil {
			resp.Body.Close()
		}
		time.Sleep(1 * time.Second)
	}
	suite.T().Fatal("FaaS 服务启动超时")
}

// TestHealthCheck 测试健康检查
func (suite *FaaSIntegrationTestSuite) TestHealthCheck() {
	resp, err := suite.client.Get(suite.baseURL + "/functions/health")
	suite.NoError(err)
	suite.Equal(http.StatusOK, resp.StatusCode)
	
	var result map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&result)
	suite.NoError(err)
	suite.Equal("healthy", result["status"])
	suite.Equal("faas-executor", result["service"])
	
	resp.Body.Close()
}

// TestExecuteNodeJSFunction 测试执行 Node.js 函数
func (suite *FaaSIntegrationTestSuite) TestExecuteNodeJSFunction() {
	request := faas.FunctionRequest{
		FunctionID:   "test-nodejs-hello",
		FunctionName: "Node.js Hello World",
		Runtime:      "nodejs",
		Handler:      "main",
		Code: `
function main(params) {
    const name = params.name || "World";
    return {
        message: "Hello, " + name + "!",
        timestamp: new Date().toISOString(),
        runtime: "nodejs"
    };
}`,
		Parameters: map[string]interface{}{
			"name": "FaaS Test",
		},
		Timeout: 30 * time.Second,
	}
	
	body, err := json.Marshal(request)
	suite.NoError(err)
	
	resp, err := suite.client.Post(
		suite.baseURL+"/functions/execute",
		"application/json",
		bytes.NewBuffer(body),
	)
	suite.NoError(err)
	defer resp.Body.Close()
	
	suite.Equal(http.StatusOK, resp.StatusCode)
	
	var response faas.FunctionResponse
	err = json.NewDecoder(resp.Body).Decode(&response)
	suite.NoError(err)
	
	suite.Equal("success", response.Status)
	suite.NotNil(response.Result)
	suite.Greater(response.ExecutionTime, time.Duration(0))
	
	// 验证结果内容
	result, ok := response.Result.(map[string]interface{})
	suite.True(ok)
	suite.Contains(result["message"], "Hello, FaaS Test!")
	suite.Equal("nodejs", result["runtime"])
}

// TestExecutePythonFunction 测试执行 Python 函数
func (suite *FaaSIntegrationTestSuite) TestExecutePythonFunction() {
	request := faas.FunctionRequest{
		FunctionID:   "test-python-math",
		FunctionName: "Python Math Function",
		Runtime:      "python",
		Handler:      "main",
		Code: `
def main(params):
    import math
    
    x = params.get("x", 0)
    y = params.get("y", 0)
    
    return {
        "sum": x + y,
        "product": x * y,
        "sqrt_x": math.sqrt(abs(x)) if x >= 0 else None,
        "runtime": "python"
    }`,
		Parameters: map[string]interface{}{
			"x": 16,
			"y": 9,
		},
		Timeout: 30 * time.Second,
	}
	
	body, err := json.Marshal(request)
	suite.NoError(err)
	
	resp, err := suite.client.Post(
		suite.baseURL+"/functions/execute",
		"application/json",
		bytes.NewBuffer(body),
	)
	suite.NoError(err)
	defer resp.Body.Close()
	
	suite.Equal(http.StatusOK, resp.StatusCode)
	
	var response faas.FunctionResponse
	err = json.NewDecoder(resp.Body).Decode(&response)
	suite.NoError(err)
	
	suite.Equal("success", response.Status)
	suite.NotNil(response.Result)
	
	// 验证结果内容
	result, ok := response.Result.(map[string]interface{})
	suite.True(ok)
	suite.Equal(float64(25), result["sum"])
	suite.Equal(float64(144), result["product"])
	suite.Equal(float64(4), result["sqrt_x"])
	suite.Equal("python", result["runtime"])
}

// TestExecuteFunctionWithError 测试执行有错误的函数
func (suite *FaaSIntegrationTestSuite) TestExecuteFunctionWithError() {
	request := faas.FunctionRequest{
		FunctionID:   "test-error-function",
		FunctionName: "Error Function",
		Runtime:      "nodejs",
		Handler:      "main",
		Code: `
function main(params) {
    throw new Error("This is a test error");
}`,
		Parameters: map[string]interface{}{},
		Timeout:    30 * time.Second,
	}
	
	body, err := json.Marshal(request)
	suite.NoError(err)
	
	resp, err := suite.client.Post(
		suite.baseURL+"/functions/execute",
		"application/json",
		bytes.NewBuffer(body),
	)
	suite.NoError(err)
	defer resp.Body.Close()
	
	// 错误的函数执行应该返回 500 状态码
	suite.Equal(http.StatusInternalServerError, resp.StatusCode)
	
	var response map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&response)
	suite.NoError(err)
	
	suite.Equal("error", response["status"])
	suite.Contains(response["message"], "函数执行失败")
}

// TestAsyncExecution 测试异步执行
func (suite *FaaSIntegrationTestSuite) TestAsyncExecution() {
	request := faas.FunctionRequest{
		FunctionID:   "test-async-function",
		FunctionName: "Async Function",
		Runtime:      "nodejs",
		Handler:      "main",
		Code: `
function main(params) {
    // 模拟长时间运行的任务
    const delay = params.delay || 1000;
    const start = Date.now();
    while (Date.now() - start < delay) {
        // 忙等待
    }
    
    return {
        message: "Async execution completed",
        delay: delay,
        timestamp: new Date().toISOString()
    };
}`,
		Parameters: map[string]interface{}{
			"delay": 2000, // 2秒延迟
		},
		Timeout: 30 * time.Second,
	}
	
	body, err := json.Marshal(request)
	suite.NoError(err)
	
	// 发送异步执行请求
	resp, err := suite.client.Post(
		suite.baseURL+"/functions/async-execute",
		"application/json",
		bytes.NewBuffer(body),
	)
	suite.NoError(err)
	defer resp.Body.Close()
	
	// 异步执行应该立即返回 202 状态码
	suite.Equal(http.StatusAccepted, resp.StatusCode)
	
	var response map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&response)
	suite.NoError(err)
	
	suite.Equal("accepted", response["status"])
	suite.Contains(response["message"], "函数执行请求已接受")
	suite.NotEmpty(response["request_id"])
}

// TestGetMetrics 测试获取指标
func (suite *FaaSIntegrationTestSuite) TestGetMetrics() {
	resp, err := suite.client.Get(suite.baseURL + "/functions/metrics")
	suite.NoError(err)
	defer resp.Body.Close()
	
	suite.Equal(http.StatusOK, resp.StatusCode)
	
	var response map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&response)
	suite.NoError(err)
	
	suite.NotNil(response["metrics"])
	suite.NotNil(response["timestamp"])
	
	// 验证指标结构
	metrics, ok := response["metrics"].(map[string]interface{})
	suite.True(ok)
	suite.Contains(metrics, "total_executions")
	suite.Contains(metrics, "successful_executions")
	suite.Contains(metrics, "failed_executions")
	suite.Contains(metrics, "active_executions")
}

// TestGetPoolStats 测试获取容器池统计
func (suite *FaaSIntegrationTestSuite) TestGetPoolStats() {
	resp, err := suite.client.Get(suite.baseURL + "/functions/pool/stats")
	suite.NoError(err)
	defer resp.Body.Close()
	
	suite.Equal(http.StatusOK, resp.StatusCode)
	
	var response map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&response)
	suite.NoError(err)
	
	suite.NotNil(response["pool_stats"])
	suite.NotNil(response["timestamp"])
	
	// 验证容器池统计结构
	poolStats, ok := response["pool_stats"].(map[string]interface{})
	suite.True(ok)
	suite.Contains(poolStats, "total_containers")
	suite.Contains(poolStats, "max_size")
}

// TestInvalidRequest 测试无效请求
func (suite *FaaSIntegrationTestSuite) TestInvalidRequest() {
	// 测试缺少必需字段的请求
	invalidRequest := map[string]interface{}{
		"function_name": "Invalid Function",
		// 缺少 function_id, runtime, code
	}
	
	body, err := json.Marshal(invalidRequest)
	suite.NoError(err)
	
	resp, err := suite.client.Post(
		suite.baseURL+"/functions/execute",
		"application/json",
		bytes.NewBuffer(body),
	)
	suite.NoError(err)
	defer resp.Body.Close()
	
	suite.Equal(http.StatusBadRequest, resp.StatusCode)
	
	var response map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&response)
	suite.NoError(err)
	
	suite.Equal("error", response["status"])
	suite.Contains(response["message"], "请求验证失败")
}

// TestUnsupportedRuntime 测试不支持的运行时
func (suite *FaaSIntegrationTestSuite) TestUnsupportedRuntime() {
	request := faas.FunctionRequest{
		FunctionID:   "test-unsupported-runtime",
		FunctionName: "Unsupported Runtime Function",
		Runtime:      "unsupported",
		Handler:      "main",
		Code:         "some code",
		Parameters:   map[string]interface{}{},
		Timeout:      30 * time.Second,
	}
	
	body, err := json.Marshal(request)
	suite.NoError(err)
	
	resp, err := suite.client.Post(
		suite.baseURL+"/functions/execute",
		"application/json",
		bytes.NewBuffer(body),
	)
	suite.NoError(err)
	defer resp.Body.Close()
	
	suite.Equal(http.StatusBadRequest, resp.StatusCode)
	
	var response map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&response)
	suite.NoError(err)
	
	suite.Equal("error", response["status"])
	suite.Contains(response["message"], "不支持的运行时")
}

// TestFaaSIntegration 运行 FaaS 集成测试
func TestFaaSIntegration(t *testing.T) {
	suite.Run(t, new(FaaSIntegrationTestSuite))
}
