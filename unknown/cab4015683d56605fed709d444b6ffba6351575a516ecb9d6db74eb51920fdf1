package middleware

import (
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"paas-platform/pkg/graphql"
	"paas-platform/pkg/logger"
)

// GraphQLMiddleware GraphQL 中间件
type GraphQLMiddleware struct {
	gateway *graphql.GraphQLGateway
	logger  logger.Logger
	config  GraphQLMiddlewareConfig
}

// GraphQLMiddlewareConfig GraphQL 中间件配置
type GraphQLMiddlewareConfig struct {
	Enabled           bool     `json:"enabled"`            // 是否启用
	Path              string   `json:"path"`               // GraphQL 路径
	PlaygroundPath    string   `json:"playground_path"`    // Playground 路径
	SkipPaths         []string `json:"skip_paths"`         // 跳过的路径
	OnlyPaths         []string `json:"only_paths"`         // 仅处理的路径
	RequireAuth       bool     `json:"require_auth"`       // 是否需要认证
	AuthHeader        string   `json:"auth_header"`        // 认证头名称
	UserIDHeader      string   `json:"user_id_header"`     // 用户ID头名称
	LogRequests       bool     `json:"log_requests"`       // 记录请求日志
	LogResponses      bool     `json:"log_responses"`      // 记录响应日志
	AddHeaders        bool     `json:"add_headers"`        // 添加响应头
	CorsEnabled       bool     `json:"cors_enabled"`       // 启用CORS
	CorsOrigins       []string `json:"cors_origins"`       // CORS允许的来源
	RateLimitEnabled  bool     `json:"rate_limit_enabled"` // 启用速率限制
	RateLimit         int      `json:"rate_limit"`         // 速率限制（请求/分钟）
	MaxQueryDepth     int      `json:"max_query_depth"`    // 最大查询深度
	MaxQueryComplexity int     `json:"max_query_complexity"` // 最大查询复杂度
}

// NewGraphQLMiddleware 创建 GraphQL 中间件
func NewGraphQLMiddleware(
	gateway *graphql.GraphQLGateway,
	logger logger.Logger,
	config GraphQLMiddlewareConfig,
) *GraphQLMiddleware {
	return &GraphQLMiddleware{
		gateway: gateway,
		logger:  logger,
		config:  config,
	}
}

// Handler 返回中间件处理函数
func (gm *GraphQLMiddleware) Handler() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查是否启用
		if !gm.config.Enabled {
			c.Next()
			return
		}

		// 检查路径匹配
		if !gm.shouldHandle(c.Request.URL.Path) {
			c.Next()
			return
		}

		// 处理 CORS
		if gm.config.CorsEnabled {
			gm.handleCORS(c)
			if c.Request.Method == "OPTIONS" {
				c.AbortWithStatus(http.StatusOK)
				return
			}
		}

		// 认证检查
		if gm.config.RequireAuth {
			if !gm.checkAuth(c) {
				c.JSON(http.StatusUnauthorized, gin.H{
					"errors": []gin.H{{
						"message": "认证失败",
					}},
				})
				c.Abort()
				return
			}
		}

		// 速率限制检查
		if gm.config.RateLimitEnabled {
			if !gm.checkRateLimit(c) {
				c.JSON(http.StatusTooManyRequests, gin.H{
					"errors": []gin.H{{
						"message": "请求过于频繁",
					}},
				})
				c.Abort()
				return
			}
		}

		// 记录请求日志
		if gm.config.LogRequests {
			gm.logger.Info("GraphQL 请求",
				"method", c.Request.Method,
				"path", c.Request.URL.Path,
				"remote_addr", c.ClientIP(),
				"user_agent", c.Request.UserAgent(),
				"user_id", c.GetHeader(gm.config.UserIDHeader))
		}

		// 添加响应头
		if gm.config.AddHeaders {
			c.Header("X-GraphQL-Gateway", "enabled")
			c.Header("X-GraphQL-Version", "1.0")
			c.Header("X-GraphQL-Timestamp", time.Now().Format(time.RFC3339))
		}

		// 处理 GraphQL 请求
		if c.Request.URL.Path == gm.config.PlaygroundPath {
			gm.gateway.HandlePlayground(c)
		} else {
			gm.gateway.HandleRequest(c)
		}

		c.Abort()
	}
}

// shouldHandle 检查是否应该处理
func (gm *GraphQLMiddleware) shouldHandle(path string) bool {
	// 检查跳过路径
	for _, skipPath := range gm.config.SkipPaths {
		if gm.matchPath(skipPath, path) {
			return false
		}
	}

	// 检查仅处理路径
	if len(gm.config.OnlyPaths) > 0 {
		for _, onlyPath := range gm.config.OnlyPaths {
			if gm.matchPath(onlyPath, path) {
				return true
			}
		}
		return false
	}

	// 检查 GraphQL 路径
	return path == gm.config.Path || path == gm.config.PlaygroundPath
}

// matchPath 路径匹配
func (gm *GraphQLMiddleware) matchPath(pattern, path string) bool {
	if pattern == "*" {
		return true
	}
	if strings.HasSuffix(pattern, "*") {
		prefix := pattern[:len(pattern)-1]
		return strings.HasPrefix(path, prefix)
	}
	return pattern == path
}

// handleCORS 处理 CORS
func (gm *GraphQLMiddleware) handleCORS(c *gin.Context) {
	origin := c.GetHeader("Origin")

	// 检查允许的来源
	allowed := false
	for _, allowedOrigin := range gm.config.CorsOrigins {
		if allowedOrigin == "*" || allowedOrigin == origin {
			allowed = true
			break
		}
	}

	if allowed {
		c.Header("Access-Control-Allow-Origin", origin)
		c.Header("Access-Control-Allow-Credentials", "true")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, X-User-ID")
		c.Header("Access-Control-Expose-Headers", "X-GraphQL-Gateway, X-GraphQL-Version, X-GraphQL-Timestamp")
	}
}

// checkAuth 检查认证
func (gm *GraphQLMiddleware) checkAuth(c *gin.Context) bool {
	if gm.config.AuthHeader == "" {
		return true
	}

	authValue := c.GetHeader(gm.config.AuthHeader)
	if authValue == "" {
		return false
	}

	// 这里可以添加具体的认证逻辑
	// 例如验证 JWT token、API key 等
	// 简化实现，只检查是否存在认证头
	return true
}

// checkRateLimit 检查速率限制
func (gm *GraphQLMiddleware) checkRateLimit(c *gin.Context) bool {
	// 简化实现：总是返回 true
	// 实际项目中可以集成 Redis 或内存存储来实现速率限制
	return true
}

// GetGateway 获取 GraphQL 网关
func (gm *GraphQLMiddleware) GetGateway() *graphql.GraphQLGateway {
	return gm.gateway
}

// GetStats 获取统计信息
func (gm *GraphQLMiddleware) GetStats() *graphql.Stats {
	return gm.gateway.GetStats()
}

// RegisterService 注册服务
func (gm *GraphQLMiddleware) RegisterService(service *graphql.Service) error {
	return gm.gateway.RegisterService(service)
}

// UnregisterService 注销服务
func (gm *GraphQLMiddleware) UnregisterService(serviceID string) error {
	return gm.gateway.UnregisterService(serviceID)
}

// GetServices 获取所有服务
func (gm *GraphQLMiddleware) GetServices() []*graphql.Service {
	return gm.gateway.GetServices()
}

// GetSchema 获取聚合 Schema
func (gm *GraphQLMiddleware) GetSchema() string {
	return gm.gateway.GetSchema()
}

// DefaultGraphQLMiddlewareConfig 默认配置
func DefaultGraphQLMiddlewareConfig() GraphQLMiddlewareConfig {
	return GraphQLMiddlewareConfig{
		Enabled:            true,
		Path:               "/graphql",
		PlaygroundPath:     "/graphql/playground",
		RequireAuth:        false,
		AuthHeader:         "Authorization",
		UserIDHeader:       "X-User-ID",
		LogRequests:        true,
		LogResponses:       false,
		AddHeaders:         true,
		CorsEnabled:        true,
		CorsOrigins:        []string{"*"},
		RateLimitEnabled:   false,
		RateLimit:          100,
		MaxQueryDepth:      15,
		MaxQueryComplexity: 1000,
		SkipPaths: []string{
			"/health",
			"/ready",
			"/metrics",
		},
	}
}

// DevelopmentGraphQLMiddlewareConfig 开发环境配置
func DevelopmentGraphQLMiddlewareConfig() GraphQLMiddlewareConfig {
	config := DefaultGraphQLMiddlewareConfig()
	config.RequireAuth = false
	config.LogRequests = true
	config.LogResponses = true
	config.CorsOrigins = []string{"*"}
	config.RateLimitEnabled = false
	return config
}

// ProductionGraphQLMiddlewareConfig 生产环境配置
func ProductionGraphQLMiddlewareConfig() GraphQLMiddlewareConfig {
	config := DefaultGraphQLMiddlewareConfig()
	config.RequireAuth = true
	config.LogRequests = true
	config.LogResponses = false
	config.CorsOrigins = []string{} // 生产环境需要明确指定允许的来源
	config.RateLimitEnabled = true
	config.RateLimit = 60 // 每分钟60个请求
	return config
}

// GraphQLHandler GraphQL 处理器接口
type GraphQLHandler interface {
	HandleQuery(c *gin.Context, query string, variables map[string]interface{}) (*graphql.Response, error)
	HandleMutation(c *gin.Context, mutation string, variables map[string]interface{}) (*graphql.Response, error)
	HandleSubscription(c *gin.Context, subscription string, variables map[string]interface{}) (*graphql.Response, error)
}

// DefaultGraphQLHandler 默认 GraphQL 处理器
type DefaultGraphQLHandler struct {
	logger logger.Logger
}

// NewDefaultGraphQLHandler 创建默认处理器
func NewDefaultGraphQLHandler(logger logger.Logger) *DefaultGraphQLHandler {
	return &DefaultGraphQLHandler{
		logger: logger,
	}
}

// HandleQuery 处理查询
func (h *DefaultGraphQLHandler) HandleQuery(c *gin.Context, query string, variables map[string]interface{}) (*graphql.Response, error) {
	h.logger.Debug("处理 GraphQL 查询",
		"query", query,
		"variables", variables)

	return &graphql.Response{
		Data: map[string]interface{}{
			"hello": "world",
		},
	}, nil
}

// HandleMutation 处理变更
func (h *DefaultGraphQLHandler) HandleMutation(c *gin.Context, mutation string, variables map[string]interface{}) (*graphql.Response, error) {
	h.logger.Debug("处理 GraphQL 变更",
		"mutation", mutation,
		"variables", variables)

	return &graphql.Response{
		Data: map[string]interface{}{
			"success": true,
		},
	}, nil
}

// HandleSubscription 处理订阅
func (h *DefaultGraphQLHandler) HandleSubscription(c *gin.Context, subscription string, variables map[string]interface{}) (*graphql.Response, error) {
	h.logger.Debug("处理 GraphQL 订阅",
		"subscription", subscription,
		"variables", variables)

	return &graphql.Response{
		Data: map[string]interface{}{
			"subscribed": true,
		},
	}, nil
}
