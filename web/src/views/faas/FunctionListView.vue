<template>
  <div class="function-list-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">函数管理</h2>
        <p class="page-description">管理您的无服务器函数，支持多种运行时环境</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showCreateDialog">
          <el-icon><Plus /></el-icon>
          创建函数
        </el-button>
      </div>
    </div>
    
    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <div class="filter-left">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索函数名称..."
          clearable
          class="search-input"
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <el-select
          v-model="filterRuntime"
          placeholder="运行时"
          clearable
          class="filter-select"
          @change="handleFilter"
        >
          <el-option label="全部运行时" value="" />
          <el-option label="Node.js" value="nodejs" />
          <el-option label="Python" value="python" />
          <el-option label="Go" value="go" />
          <el-option label="Java" value="java" />
        </el-select>
        
        <el-select
          v-model="filterStatus"
          placeholder="状态"
          clearable
          class="filter-select"
          @change="handleFilter"
        >
          <el-option label="全部状态" value="" />
          <el-option label="活跃" value="active" />
          <el-option label="非活跃" value="inactive" />
          <el-option label="已弃用" value="deprecated" />
        </el-select>
      </div>
      
      <div class="filter-right">
        <el-button @click="refreshList">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>
    
    <!-- 函数列表 -->
    <div class="function-list">
      <el-table
        v-loading="loading"
        :data="functionList"
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column label="函数信息" min-width="200">
          <template #default="{ row }">
            <div class="function-info">
              <div class="function-icon">
                <el-icon><Lightning /></el-icon>
              </div>
              <div class="function-details">
                <div class="function-name">{{ row.name }}</div>
                <div class="function-id">{{ row.function_id }}</div>
                <div class="function-description">{{ row.description || '暂无描述' }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="运行时" width="120">
          <template #default="{ row }">
            <el-tag :type="getRuntimeTagType(row.runtime)">
              {{ getRuntimeLabel(row.runtime) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="版本" width="100">
          <template #default="{ row }">
            <span class="version-text">{{ row.version }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="资源限制" width="150">
          <template #default="{ row }">
            <div class="resource-info">
              <div>内存: {{ row.memory_limit || '128MB' }}</div>
              <div>CPU: {{ row.cpu_limit || '0.1' }}</div>
              <div>超时: {{ row.timeout || 30 }}s</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="标签" width="150">
          <template #default="{ row }">
            <el-tag
              v-for="tag in row.tags"
              :key="tag"
              size="small"
              style="margin-right: 4px; margin-bottom: 4px;"
            >
              {{ tag }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="创建时间" width="160">
          <template #default="{ row }">
            <span class="time-text">{{ formatTime(row.created_at) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                type="primary"
                size="small"
                @click="executeFunction(row)"
              >
                执行
              </el-button>
              
              <el-dropdown @command="(command) => handleAction(command, row)">
                <el-button size="small">
                  更多
                  <el-icon><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="view">查看详情</el-dropdown-item>
                    <el-dropdown-item command="edit">编辑</el-dropdown-item>
                    <el-dropdown-item command="versions">版本管理</el-dropdown-item>
                    <el-dropdown-item command="triggers">触发器</el-dropdown-item>
                    <el-dropdown-item command="logs">执行日志</el-dropdown-item>
                    <el-dropdown-item divided command="clone">克隆</el-dropdown-item>
                    <el-dropdown-item 
                      command="delete" 
                      class="danger-item"
                    >
                      删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 创建函数对话框 -->
    <el-dialog
      v-model="createDialogVisible"
      title="创建函数"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="createFormRef"
        :model="createForm"
        :rules="createRules"
        label-width="100px"
      >
        <el-form-item label="函数ID" prop="function_id">
          <el-input
            v-model="createForm.function_id"
            placeholder="请输入函数ID"
          />
        </el-form-item>
        
        <el-form-item label="函数名称" prop="name">
          <el-input
            v-model="createForm.name"
            placeholder="请输入函数名称"
          />
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="createForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入函数描述"
          />
        </el-form-item>
        
        <el-form-item label="运行时" prop="runtime">
          <el-select v-model="createForm.runtime" placeholder="选择运行时">
            <el-option label="Node.js" value="nodejs" />
            <el-option label="Python" value="python" />
            <el-option label="Go" value="go" />
            <el-option label="Java" value="java" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="入口函数" prop="handler">
          <el-input
            v-model="createForm.handler"
            placeholder="例如: index.handler"
          />
        </el-form-item>
        
        <el-form-item label="函数代码" prop="code">
          <el-input
            v-model="createForm.code"
            type="textarea"
            :rows="10"
            placeholder="请输入函数代码"
          />
        </el-form-item>
        
        <el-form-item label="超时时间">
          <el-input-number
            v-model="createForm.timeout"
            :min="1"
            :max="900"
            placeholder="秒"
          />
        </el-form-item>
        
        <el-form-item label="内存限制">
          <el-input
            v-model="createForm.memory_limit"
            placeholder="例如: 128MB"
          />
        </el-form-item>
        
        <el-form-item label="CPU限制">
          <el-input
            v-model="createForm.cpu_limit"
            placeholder="例如: 0.1"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="createDialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          :loading="creating"
          @click="handleCreate"
        >
          创建
        </el-button>
      </template>
    </el-dialog>

    <!-- 执行函数对话框 -->
    <el-dialog
      v-model="executeDialogVisible"
      title="执行函数"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form label-width="100px">
        <el-form-item label="函数名称">
          <span>{{ currentFunction?.name }}</span>
        </el-form-item>
        
        <el-form-item label="执行参数">
          <el-input
            v-model="executeParams"
            type="textarea"
            :rows="6"
            placeholder="请输入JSON格式的参数"
          />
        </el-form-item>
        
        <el-form-item label="执行模式">
          <el-radio-group v-model="executeMode">
            <el-radio label="sync">同步执行</el-radio>
            <el-radio label="async">异步执行</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="executeDialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          :loading="executing"
          @click="handleExecute"
        >
          执行
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useFaasStore } from '@/stores/faas'
import type { FaaSFunction, CreateFunctionRequest } from '@/api/faas'
import type { FormInstance, FormRules } from 'element-plus'
import dayjs from 'dayjs'

// 响应式数据
const router = useRouter()
const faasStore = useFaasStore()
const createDialogVisible = ref(false)
const executeDialogVisible = ref(false)
const creating = ref(false)
const searchKeyword = ref('')
const filterRuntime = ref('')
const filterStatus = ref('')
const selectedFunctions = ref<FaaSFunction[]>([])
const createFormRef = ref<FormInstance>()
const currentFunction = ref<FaaSFunction | null>(null)
const executeParams = ref('{}')
const executeMode = ref('sync')

// 从store获取数据
const functionList = computed(() => faasStore.functions)
const loading = computed(() => faasStore.functionsLoading)
const executing = computed(() => faasStore.executing)
const pagination = computed(() => faasStore.functionsPagination)
const error = computed(() => faasStore.error)

// 创建表单
const createForm = reactive<CreateFunctionRequest>({
  function_id: '',
  name: '',
  description: '',
  runtime: 'nodejs',
  handler: '',
  code: '',
  timeout: 30,
  memory_limit: '128MB',
  cpu_limit: '0.1',
  environment: {},
  tags: []
})

// 表单验证规则
const createRules: FormRules = {
  function_id: [
    { required: true, message: '请输入函数ID', trigger: 'blur' },
    { min: 3, max: 50, message: '函数ID长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入函数名称', trigger: 'blur' },
    { min: 2, max: 100, message: '函数名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  runtime: [
    { required: true, message: '请选择运行时', trigger: 'change' }
  ],
  handler: [
    { required: true, message: '请输入入口函数', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入函数代码', trigger: 'blur' }
  ]
}

// 工具函数
const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 计算属性
const queryParams = computed(() => ({
  page: pagination.value.page,
  pageSize: pagination.value.pageSize,
  search: searchKeyword.value,
  runtime: filterRuntime.value,
  status: filterStatus.value
}))

// 方法定义
const loadFunctionList = async () => {
  try {
    await faasStore.fetchFunctions(queryParams.value)
  } catch (error) {
    console.error('加载函数列表失败:', error)
    ElMessage.error('加载函数列表失败')
  }
}

const handleSearch = debounce(() => {
  faasStore.functionsPagination.page = 1
  loadFunctionList()
}, 300)

const handleFilter = () => {
  faasStore.functionsPagination.page = 1
  loadFunctionList()
}

const refreshList = () => {
  loadFunctionList()
}

const handleSelectionChange = (selection: FaaSFunction[]) => {
  selectedFunctions.value = selection
}

const handleSizeChange = (size: number) => {
  faasStore.functionsPagination.pageSize = size
  faasStore.functionsPagination.page = 1
  loadFunctionList()
}

const handleCurrentChange = (page: number) => {
  faasStore.functionsPagination.page = page
  loadFunctionList()
}

const showCreateDialog = () => {
  createDialogVisible.value = true
}

const handleCreate = async () => {
  if (!createFormRef.value) return
  
  try {
    await createFormRef.value.validate()
    creating.value = true
    
    await faasStore.createFunction(createForm)
    ElMessage.success('函数创建成功')
    createDialogVisible.value = false
    
    // 重置表单
    Object.assign(createForm, {
      function_id: '',
      name: '',
      description: '',
      runtime: 'nodejs',
      handler: '',
      code: '',
      timeout: 30,
      memory_limit: '128MB',
      cpu_limit: '0.1',
      environment: {},
      tags: []
    })
  } catch (error) {
    console.error('创建函数失败:', error)
    ElMessage.error('创建函数失败')
  } finally {
    creating.value = false
  }
}

const executeFunction = (func: FaaSFunction) => {
  currentFunction.value = func
  executeParams.value = '{}'
  executeMode.value = 'sync'
  executeDialogVisible.value = true
}

const handleExecute = async () => {
  if (!currentFunction.value) return
  
  try {
    let params = {}
    if (executeParams.value.trim()) {
      params = JSON.parse(executeParams.value)
    }
    
    const executeRequest = {
      function_id: currentFunction.value.function_id,
      parameters: params,
      async: executeMode.value === 'async'
    }
    
    if (executeMode.value === 'sync') {
      const result = await faasStore.executeFunction(executeRequest)
      ElMessage.success('函数执行成功')
      console.log('执行结果:', result)
    } else {
      const result = await faasStore.asyncExecuteFunction(executeRequest)
      ElMessage.success(`异步执行已提交，请求ID: ${result.request_id}`)
    }
    
    executeDialogVisible.value = false
  } catch (error) {
    console.error('执行函数失败:', error)
    ElMessage.error('执行函数失败')
  }
}

const handleAction = async (command: string, func: FaaSFunction) => {
  switch (command) {
    case 'view':
      router.push(`/faas/functions/${func.function_id}`)
      break
    case 'edit':
      ElMessage.info('编辑功能开发中...')
      break
    case 'versions':
      router.push(`/faas/functions/${func.function_id}/versions`)
      break
    case 'triggers':
      router.push(`/faas/functions/${func.function_id}/triggers`)
      break
    case 'logs':
      router.push(`/faas/functions/${func.function_id}/executions`)
      break
    case 'clone':
      ElMessage.info('克隆功能开发中...')
      break
    case 'delete':
      await deleteFunction(func)
      break
  }
}

const deleteFunction = async (func: FaaSFunction) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除函数 "${func.name}" 吗？此操作不可恢复！`,
      '确认删除',
      { 
        type: 'error',
        confirmButtonText: '确定删除',
        confirmButtonClass: 'el-button--danger'
      }
    )
    
    await faasStore.deleteFunction(func.function_id)
    ElMessage.success(`函数 "${func.name}" 已删除`)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除函数失败:', error)
      ElMessage.error('删除函数失败')
    }
  }
}

// 工具函数
const getRuntimeTagType = (runtime: string) => {
  const typeMap: Record<string, string> = {
    nodejs: 'success',
    python: 'warning',
    go: 'info',
    java: 'danger'
  }
  return typeMap[runtime] || ''
}

const getRuntimeLabel = (runtime: string) => {
  const labelMap: Record<string, string> = {
    nodejs: 'Node.js',
    python: 'Python',
    go: 'Go',
    java: 'Java'
  }
  return labelMap[runtime] || runtime
}

const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    active: 'success',
    inactive: 'info',
    deprecated: 'danger'
  }
  return typeMap[status] || ''
}

const getStatusLabel = (status: string) => {
  const labelMap: Record<string, string> = {
    active: '活跃',
    inactive: '非活跃',
    deprecated: '已弃用'
  }
  return labelMap[status] || status
}

const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

// 监听错误状态
watch(error, (newError) => {
  if (newError) {
    ElMessage.error(newError)
    faasStore.clearError()
  }
})

// 组件挂载时加载数据
onMounted(() => {
  loadFunctionList()
})

// 组件卸载时清理状态
onUnmounted(() => {
  faasStore.clearError()
})
</script>

<style lang="scss" scoped>
// 导入混合宏以使用样式函数
@use '@/styles/mixins' as mixins;

.function-list-container {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    
    .header-left {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin: 0 0 8px 0;
      }
      
      .page-description {
        font-size: 14px;
        color: var(--el-text-color-secondary);
        margin: 0;
      }
    }
  }
  
  .filter-section {
    @include mixins.card-style;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px 20px;
    
    .filter-left {
      display: flex;
      gap: 12px;
      flex: 1;
      
      .search-input {
        width: 300px;
      }
      
      .filter-select {
        width: 120px;
      }
    }
    
    .filter-right {
      display: flex;
      gap: 8px;
    }
  }
  
  .function-list {
    @include mixins.card-style;
    padding: 0;
    
    .function-info {
      display: flex;
      align-items: center;
      
      .function-icon {
        width: 40px;
        height: 40px;
        background: var(--el-color-primary-light-8);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        
        .el-icon {
          font-size: 20px;
          color: var(--el-color-primary);
        }
      }
      
      .function-details {
        .function-name {
          font-size: 14px;
          font-weight: 500;
          color: var(--el-text-color-primary);
          margin-bottom: 4px;
        }
        
        .function-id {
          font-size: 12px;
          color: var(--el-text-color-secondary);
          margin-bottom: 4px;
        }
        
        .function-description {
          font-size: 12px;
          color: var(--el-text-color-secondary);
          @include mixins.text-ellipsis;
          max-width: 200px;
        }
      }
    }
    
    .resource-info {
      font-size: 12px;
      color: var(--el-text-color-secondary);
      
      div {
        margin-bottom: 2px;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
    
    .version-text,
    .time-text {
      font-size: 12px;
      color: var(--el-text-color-secondary);
    }
    
    .action-buttons {
      display: flex;
      gap: 8px;
    }
    
    .pagination-container {
      padding: 20px;
      display: flex;
      justify-content: center;
    }
  }
}

:deep(.danger-item) {
  color: var(--el-color-danger);
}
</style>
