<template>
  <div class="admin-login-container">
    <div class="admin-login-card">
      <!-- 管理员登录头部 -->
      <div class="admin-login-header">
        <div class="admin-icon">
          <el-icon :size="48"><Setting /></el-icon>
        </div>
        <h1 class="admin-title">系统管理员登录</h1>
        <p class="admin-subtitle">仅限系统管理员使用</p>
        
        <!-- 返回普通登录链接 -->
        <el-button 
          type="text" 
          class="back-to-login"
          @click="backToLogin"
        >
          <el-icon><ArrowLeft /></el-icon>
          返回用户登录
        </el-button>
      </div>

      <!-- 管理员登录表单 -->
      <el-form
        ref="adminFormRef"
        :model="adminForm"
        :rules="adminRules"
        class="admin-form"
        size="large"
        @keyup.enter="handleAdminLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="adminForm.username"
            placeholder="管理员用户名"
            :prefix-icon="User"
            clearable
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="adminForm.password"
            type="password"
            placeholder="管理员密码"
            :prefix-icon="Lock"
            show-password
            clearable
          />
        </el-form-item>

        <el-form-item prop="tenantId">
          <el-input
            v-model="adminForm.tenantId"
            placeholder="租户ID"
            :prefix-icon="Building"
            clearable
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            class="admin-login-button"
            :loading="adminLoading"
            @click="handleAdminLogin"
          >
            {{ adminLoading ? '登录中...' : '管理员登录' }}
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 安全提示 -->
      <div class="security-notice">
        <el-alert
          title="安全提示"
          type="warning"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>此页面仅供系统管理员使用。</p>
            <p>请确保您有合法的管理员权限。</p>
            <p>所有登录行为将被记录和审计。</p>
          </template>
        </el-alert>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { authApi } from '@/api/auth'
import type { FormInstance, FormRules } from 'element-plus'
import { User, Lock, Building, Setting, ArrowLeft } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const router = useRouter()
const userStore = useUserStore()

// 表单引用
const adminFormRef = ref<FormInstance>()

// 状态管理
const adminLoading = ref(false)

// 管理员登录表单
const adminForm = reactive({
  username: '',
  password: '',
  tenantId: 'default'
})

// 表单验证规则
const adminRules: FormRules = {
  username: [
    { required: true, message: '请输入管理员用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入管理员密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于 6 个字符', trigger: 'blur' }
  ],
  tenantId: [
    { required: true, message: '请输入租户ID', trigger: 'blur' }
  ]
}

// 处理管理员登录
const handleAdminLogin = async () => {
  if (!adminFormRef.value) return

  try {
    // 验证表单
    await adminFormRef.value.validate()
    
    adminLoading.value = true

    // 调用管理员登录 API
    const response = await authApi.adminLogin({
      username: adminForm.username,
      password: adminForm.password,
      tenant_id: adminForm.tenantId
    })

    // 保存管理员令牌
    localStorage.setItem('access_token', response.data.access_token)
    if (response.data.refresh_token) {
      localStorage.setItem('refresh_token', response.data.refresh_token)
    }

    // 更新用户状态
    await userStore.setUser(response.data.user)

    ElMessage.success('管理员登录成功')

    // 跳转到管理后台
    router.push('/admin/dashboard')

  } catch (error: any) {
    console.error('管理员登录失败:', error)
    
    let errorMessage = '登录失败，请检查用户名和密码'
    
    if (error.response?.data?.message) {
      errorMessage = error.response.data.message
    } else if (error.message) {
      errorMessage = error.message
    }
    
    ElMessage.error(errorMessage)
  } finally {
    adminLoading.value = false
  }
}

// 返回普通登录
const backToLogin = () => {
  router.push('/login')
}
</script>

<style lang="scss" scoped>
.admin-login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.admin-login-card {
  background: var(--el-bg-color);
  border-radius: 16px;
  padding: 48px;
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 480px;
  position: relative;
}

.admin-login-header {
  text-align: center;
  margin-bottom: 40px;
  
  .admin-icon {
    margin-bottom: 16px;
    color: var(--el-color-warning);
  }
  
  .admin-title {
    margin: 0 0 8px;
    font-size: 28px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
  
  .admin-subtitle {
    margin: 0 0 24px;
    font-size: 14px;
    color: var(--el-text-color-regular);
  }
  
  .back-to-login {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    
    &:hover {
      color: var(--el-color-primary);
    }
    
    .el-icon {
      margin-right: 4px;
    }
  }
}

.admin-form {
  .el-form-item {
    margin-bottom: 24px;
  }
  
  .el-input {
    height: 48px;
    
    :deep(.el-input__inner) {
      height: 48px;
      line-height: 48px;
      font-size: 16px;
    }
  }
  
  .admin-login-button {
    width: 100%;
    height: 48px;
    font-size: 16px;
    font-weight: 500;
    border-radius: 8px;
  }
}

.security-notice {
  margin-top: 32px;
  
  :deep(.el-alert) {
    border-radius: 8px;
    
    .el-alert__content {
      p {
        margin: 4px 0;
        font-size: 13px;
        line-height: 1.4;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .admin-login-card {
    padding: 32px 24px;
    margin: 20px;
  }
  
  .admin-login-header {
    .admin-title {
      font-size: 24px;
    }
  }
}
</style>
