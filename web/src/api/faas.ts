import request from '@/utils/request'
import type { ApiResponse, PaginatedResponse } from '@/types'

// 函数定义
export interface FaaSFunction {
  id: number
  function_id: string
  name: string
  description: string
  runtime: string
  handler: string
  code: string
  version: string
  status: 'active' | 'inactive' | 'deprecated'
  timeout: number
  memory_limit: string
  cpu_limit: string
  environment: Record<string, string>
  tags: string[]
  created_by: string
  updated_by: string
  created_at: string
  updated_at: string
}

// 函数执行记录
export interface FunctionExecution {
  id: number
  request_id: string
  function_id: string
  status: 'pending' | 'running' | 'success' | 'failed' | 'timeout'
  runtime: string
  container_id: string
  parameters: Record<string, any>
  result: any
  error: string
  logs: string[]
  execution_time: number
  memory_usage: number
  cpu_usage: number
  start_time: string
  end_time: string
  created_at: string
  updated_at: string
}

// 函数版本
export interface FunctionVersion {
  id: number
  function_id: string
  version: string
  code: string
  handler: string
  environment: Record<string, string>
  created_by: string
  created_at: string
  is_active: boolean
}

// 函数触发器
export interface FunctionTrigger {
  id: number
  function_id: string
  name: string
  type: 'http' | 'timer' | 'event' | 'webhook'
  config: Record<string, any>
  enabled: boolean
  created_at: string
  updated_at: string
}

// 函数指标
export interface FunctionMetrics {
  function_id: string
  total_executions: number
  success_executions: number
  failed_executions: number
  avg_execution_time: number
  avg_memory_usage: number
  avg_cpu_usage: number
  last_execution: string
  created_at: string
  updated_at: string
}

// 函数执行请求
export interface FunctionExecuteRequest {
  function_id: string
  parameters?: Record<string, any>
  timeout?: number
  async?: boolean
  callback_url?: string
}

// 函数执行响应
export interface FunctionExecuteResponse {
  request_id: string
  function_id: string
  status: string
  result?: any
  error?: string
  execution_time?: number
  memory_usage?: number
  cpu_usage?: number
  logs?: string[]
  start_time: string
  end_time?: string
}

// 创建函数请求
export interface CreateFunctionRequest {
  function_id: string
  name: string
  description?: string
  runtime: 'nodejs' | 'python' | 'go' | 'java'
  handler: string
  code: string
  timeout?: number
  memory_limit?: string
  cpu_limit?: string
  environment?: Record<string, string>
  tags?: string[]
}

// 更新函数请求
export interface UpdateFunctionRequest {
  name?: string
  description?: string
  handler?: string
  code?: string
  timeout?: number
  memory_limit?: string
  cpu_limit?: string
  environment?: Record<string, string>
  tags?: string[]
  status?: 'active' | 'inactive' | 'deprecated'
}

// 函数查询参数
export interface FunctionQueryParams {
  page?: number
  pageSize?: number
  search?: string
  runtime?: string
  status?: 'active' | 'inactive' | 'deprecated'
  tags?: string[]
  created_by?: string
}

// 执行记录查询参数
export interface ExecutionQueryParams {
  page?: number
  pageSize?: number
  function_id?: string
  status?: 'pending' | 'running' | 'success' | 'failed' | 'timeout'
  start_time?: string
  end_time?: string
}

// 创建触发器请求
export interface CreateTriggerRequest {
  function_id: string
  name: string
  type: 'http' | 'timer' | 'event' | 'webhook'
  config: Record<string, any>
  enabled?: boolean
}

// 容器池统计
export interface PoolStats {
  total_containers: number
  active_containers: number
  idle_containers: number
  by_runtime: Record<string, number>
  pool_utilization: number
}

// 执行器指标
export interface ExecutorMetrics {
  active_executions: number
  total_executions: number
  success_rate: number
  avg_execution_time: number
  memory_usage: number
  cpu_usage: number
  container_pool: PoolStats
}

/**
 * FaaS 相关API
 */
export const faasApi = {
  // ==================== 函数管理 ====================
  
  /**
   * 获取函数列表
   */
  getFunctions(params?: FunctionQueryParams): Promise<ApiResponse<PaginatedResponse<FaaSFunction>>> {
    return request.get('/v1/faas/functions', { params })
  },

  /**
   * 获取函数详情
   */
  getFunction(functionId: string): Promise<ApiResponse<FaaSFunction>> {
    return request.get(`/v1/faas/functions/${functionId}`)
  },

  /**
   * 创建函数
   */
  createFunction(data: CreateFunctionRequest): Promise<ApiResponse<FaaSFunction>> {
    return request.post('/v1/faas/functions', data)
  },

  /**
   * 更新函数
   */
  updateFunction(functionId: string, data: UpdateFunctionRequest): Promise<ApiResponse<FaaSFunction>> {
    return request.put(`/v1/faas/functions/${functionId}`, data)
  },

  /**
   * 删除函数
   */
  deleteFunction(functionId: string): Promise<ApiResponse<void>> {
    return request.delete(`/v1/faas/functions/${functionId}`)
  },

  // ==================== 函数执行 ====================
  
  /**
   * 同步执行函数
   */
  executeFunction(data: FunctionExecuteRequest): Promise<ApiResponse<FunctionExecuteResponse>> {
    return request.post('/v1/faas/functions/execute', data)
  },

  /**
   * 异步执行函数
   */
  asyncExecuteFunction(data: FunctionExecuteRequest): Promise<ApiResponse<{ request_id: string }>> {
    return request.post('/v1/faas/functions/async-execute', data)
  },

  /**
   * 获取执行结果
   */
  getExecutionResult(requestId: string): Promise<ApiResponse<FunctionExecuteResponse>> {
    return request.get(`/v1/faas/executions/${requestId}`)
  },

  /**
   * 获取执行记录列表
   */
  getExecutions(params?: ExecutionQueryParams): Promise<ApiResponse<PaginatedResponse<FunctionExecution>>> {
    return request.get('/v1/faas/executions', { params })
  },

  /**
   * 获取函数执行记录
   */
  getFunctionExecutions(functionId: string, params?: ExecutionQueryParams): Promise<ApiResponse<PaginatedResponse<FunctionExecution>>> {
    return request.get(`/v1/faas/functions/${functionId}/executions`, { params })
  },

  /**
   * 取消执行
   */
  cancelExecution(requestId: string): Promise<ApiResponse<void>> {
    return request.post(`/v1/faas/executions/${requestId}/cancel`)
  },

  // ==================== 版本管理 ====================
  
  /**
   * 获取函数版本列表
   */
  getFunctionVersions(functionId: string): Promise<ApiResponse<FunctionVersion[]>> {
    return request.get(`/v1/faas/functions/${functionId}/versions`)
  },

  /**
   * 创建函数版本
   */
  createFunctionVersion(functionId: string, data: {
    version: string
    code: string
    handler?: string
    environment?: Record<string, string>
  }): Promise<ApiResponse<FunctionVersion>> {
    return request.post(`/v1/faas/functions/${functionId}/versions`, data)
  },

  /**
   * 激活函数版本
   */
  activateFunctionVersion(functionId: string, version: string): Promise<ApiResponse<void>> {
    return request.post(`/v1/faas/functions/${functionId}/versions/${version}/activate`)
  },

  // ==================== 触发器管理 ====================
  
  /**
   * 获取函数触发器列表
   */
  getFunctionTriggers(functionId: string): Promise<ApiResponse<FunctionTrigger[]>> {
    return request.get(`/v1/faas/functions/${functionId}/triggers`)
  },

  /**
   * 创建触发器
   */
  createTrigger(data: CreateTriggerRequest): Promise<ApiResponse<FunctionTrigger>> {
    return request.post('/v1/faas/triggers', data)
  },

  /**
   * 更新触发器
   */
  updateTrigger(triggerId: number, data: Partial<CreateTriggerRequest>): Promise<ApiResponse<FunctionTrigger>> {
    return request.put(`/v1/faas/triggers/${triggerId}`, data)
  },

  /**
   * 删除触发器
   */
  deleteTrigger(triggerId: number): Promise<ApiResponse<void>> {
    return request.delete(`/v1/faas/triggers/${triggerId}`)
  },

  /**
   * 启用/禁用触发器
   */
  toggleTrigger(triggerId: number, enabled: boolean): Promise<ApiResponse<FunctionTrigger>> {
    return request.patch(`/v1/faas/triggers/${triggerId}`, { enabled })
  },

  // ==================== 监控和指标 ====================
  
  /**
   * 获取执行器指标
   */
  getExecutorMetrics(): Promise<ApiResponse<ExecutorMetrics>> {
    return request.get('/v1/faas/functions/metrics')
  },

  /**
   * 获取容器池统计
   */
  getPoolStats(): Promise<ApiResponse<PoolStats>> {
    return request.get('/v1/faas/functions/pool/stats')
  },

  /**
   * 获取函数指标
   */
  getFunctionMetrics(functionId: string): Promise<ApiResponse<FunctionMetrics>> {
    return request.get(`/v1/faas/functions/${functionId}/metrics`)
  },

  /**
   * 健康检查
   */
  healthCheck(): Promise<ApiResponse<{
    status: 'healthy' | 'unhealthy'
    executor: 'running' | 'stopped'
    container_pool: 'available' | 'exhausted'
    timestamp: string
  }>> {
    return request.get('/v1/faas/functions/health')
  }
}
