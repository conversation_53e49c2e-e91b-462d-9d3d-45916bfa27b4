import request from '@/utils/request'
import type { ApiResponse, PaginatedResponse, Config } from '@/types'

// 配置查询参数
export interface ConfigQueryParams {
  page?: number
  pageSize?: number
  search?: string
  scope?: 'global' | 'tenant' | 'application'
  scopeId?: string
  environment?: string
  type?: 'string' | 'number' | 'boolean' | 'json' | 'secret'
  tags?: string[]
}

// 创建配置请求
export interface CreateConfigRequest {
  key: string
  value: any
  type: 'string' | 'number' | 'boolean' | 'json' | 'secret'
  scope: 'global' | 'tenant' | 'application'
  scopeId?: string
  environment: string
  encrypted?: boolean
  description?: string
  tags?: string[]
  metadata?: Record<string, string>
  schema?: any
}

// 更新配置请求
export interface UpdateConfigRequest {
  value?: any
  description?: string
  tags?: string[]
  metadata?: Record<string, string>
  schema?: any
}

// 配置版本信息
export interface ConfigVersion {
  id: string
  configId: string
  version: number
  value: any
  changeReason: string
  createdAt: string
  createdBy: string
}

// 批量配置操作
export interface BatchConfigRequest {
  keys: string[]
  scope: 'global' | 'tenant' | 'application'
  scopeId?: string
  environment: string
}

// 批量更新配置
export interface BatchUpdateConfigRequest {
  updates: Array<{
    configId: string
    value: any
    description?: string
  }>
}

// 回滚配置请求
export interface RollbackConfigRequest {
  version: number
  reason: string
}

// 导出配置请求
export interface ExportConfigsRequest {
  scope?: 'global' | 'tenant' | 'application'
  scopeId?: string
  environment?: string
  format?: 'json' | 'yaml' | 'env'
  includeSecrets?: boolean
}

// 导入配置请求
export interface ImportConfigsRequest {
  data: any
  format?: 'json' | 'yaml' | 'env'
  scope: 'global' | 'tenant' | 'application'
  scopeId?: string
  environment: string
  overwrite?: boolean
}

// 密钥查询参数
export interface SecretQueryParams {
  page?: number
  pageSize?: number
  search?: string
  scope?: 'global' | 'tenant' | 'application'
  scopeId?: string
  environment?: string
  tags?: string[]
}

// 创建密钥请求
export interface CreateSecretRequest {
  key: string
  value: string
  scope: 'global' | 'tenant' | 'application'
  scopeId?: string
  environment: string
  description?: string
  tags?: string[]
  metadata?: Record<string, string>
  expiresAt?: string
  rotationPolicy?: {
    enabled: boolean
    interval: number
    unit: 'days' | 'weeks' | 'months'
  }
}

// 更新密钥请求
export interface UpdateSecretRequest {
  value?: string
  description?: string
  tags?: string[]
  metadata?: Record<string, string>
  expiresAt?: string
  rotationPolicy?: {
    enabled: boolean
    interval: number
    unit: 'days' | 'weeks' | 'months'
  }
}

// 密钥信息
export interface Secret {
  id: string
  key: string
  scope: 'global' | 'tenant' | 'application'
  scopeId: string
  environment: string
  description: string
  tags: string[]
  metadata: Record<string, string>
  expiresAt?: string
  rotationPolicy?: {
    enabled: boolean
    interval: number
    unit: 'days' | 'weeks' | 'months'
  }
  lastRotated?: string
  version: number
  status: 'active' | 'inactive' | 'expired'
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

/**
 * 配置管理相关API
 */
export const configApi = {
  // ==================== 配置管理 ====================
  
  /**
   * 获取配置列表
   */
  getConfigs(params?: ConfigQueryParams): Promise<ApiResponse<PaginatedResponse<Config>>> {
    return request.get('/v1/configs', { params })
  },

  /**
   * 获取配置详情
   */
  getConfig(id: string): Promise<ApiResponse<Config>> {
    return request.get(`/v1/configs/${id}`)
  },

  /**
   * 创建配置
   */
  createConfig(data: CreateConfigRequest): Promise<ApiResponse<Config>> {
    return request.post('/v1/configs', data)
  },

  /**
   * 更新配置
   */
  updateConfig(id: string, data: UpdateConfigRequest): Promise<ApiResponse<Config>> {
    return request.put(`/v1/configs/${id}`, data)
  },

  /**
   * 删除配置
   */
  deleteConfig(id: string): Promise<ApiResponse<void>> {
    return request.delete(`/v1/configs/${id}`)
  },

  /**
   * 搜索配置
   */
  searchConfigs(params: {
    query: string
    scope?: string
    scopeId?: string
    environment?: string
  }): Promise<ApiResponse<Config[]>> {
    return request.get('/v1/configs/search', { params })
  },

  /**
   * 获取有效配置
   */
  getEffectiveConfig(params: {
    key: string
    scope: string
    scopeId?: string
    environment: string
  }): Promise<ApiResponse<{ value: any }>> {
    return request.get('/v1/configs/effective', { params })
  },

  /**
   * 批量获取配置
   */
  batchGetConfigs(data: BatchConfigRequest): Promise<ApiResponse<Config[]>> {
    return request.post('/v1/configs/batch', data)
  },

  /**
   * 批量更新配置
   */
  batchUpdateConfigs(data: BatchUpdateConfigRequest): Promise<ApiResponse<Config[]>> {
    return request.put('/v1/configs/batch', data)
  },

  /**
   * 获取配置版本历史
   */
  getConfigVersions(id: string): Promise<ApiResponse<ConfigVersion[]>> {
    return request.get(`/v1/configs/${id}/versions`)
  },

  /**
   * 回滚配置
   */
  rollbackConfig(id: string, data: RollbackConfigRequest): Promise<ApiResponse<Config>> {
    return request.post(`/v1/configs/${id}/rollback`, data)
  },

  /**
   * 导出配置
   */
  exportConfigs(data: ExportConfigsRequest): Promise<ApiResponse<{ data: any; format: string }>> {
    return request.post('/v1/configs/export', data)
  },

  /**
   * 导入配置
   */
  importConfigs(data: ImportConfigsRequest): Promise<ApiResponse<{
    imported: number
    updated: number
    skipped: number
    errors: Array<{ key: string; error: string }>
  }>> {
    return request.post('/v1/configs/import', data)
  },

  /**
   * 验证配置
   */
  validateConfig(data: {
    key: string
    value: any
    type: string
    schema?: any
  }): Promise<ApiResponse<{ valid: boolean; errors?: string[] }>> {
    return request.post('/v1/configs/validate', data)
  },

  // ==================== 密钥管理 ====================
  
  /**
   * 获取密钥列表
   */
  getSecrets(params?: SecretQueryParams): Promise<ApiResponse<PaginatedResponse<Secret>>> {
    return request.get('/v1/secrets', { params })
  },

  /**
   * 获取密钥详情
   */
  getSecret(id: string): Promise<ApiResponse<Secret>> {
    return request.get(`/v1/secrets/${id}`)
  },

  /**
   * 创建密钥
   */
  createSecret(data: CreateSecretRequest): Promise<ApiResponse<Secret>> {
    return request.post('/v1/secrets', data)
  },

  /**
   * 更新密钥
   */
  updateSecret(id: string, data: UpdateSecretRequest): Promise<ApiResponse<Secret>> {
    return request.put(`/v1/secrets/${id}`, data)
  },

  /**
   * 删除密钥
   */
  deleteSecret(id: string): Promise<ApiResponse<void>> {
    return request.delete(`/v1/secrets/${id}`)
  },

  /**
   * 轮换密钥
   */
  rotateSecret(id: string): Promise<ApiResponse<Secret>> {
    return request.post(`/v1/secrets/${id}/rotate`)
  }
}
