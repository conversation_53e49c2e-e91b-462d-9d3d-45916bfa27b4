import request from '@/utils/request'
import type { ApiResponse, PaginatedResponse, User, Role, Permission, Tenant } from '@/types'

// 用户查询参数
export interface UserQueryParams {
  page?: number
  pageSize?: number
  search?: string
  status?: 'active' | 'inactive' | 'suspended'
  role?: string
  tenantId?: string
  startDate?: string
  endDate?: string
}

// 创建用户请求
export interface CreateUserRequest {
  username: string
  email: string
  password: string
  firstName?: string
  lastName?: string
  phone?: string
  tenantId: string
  roleIds?: string[]
}

// 更新用户请求
export interface UpdateUserRequest {
  firstName?: string
  lastName?: string
  email?: string
  phone?: string
  avatar?: string
  status?: 'active' | 'inactive' | 'suspended'
}

// 修改密码请求
export interface ChangePasswordRequest {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

// 重置密码请求
export interface ResetPasswordRequest {
  userId: string
  newPassword: string
}

// 分配角色请求
export interface AssignRoleRequest {
  roleIds: string[]
}

// 用户会话信息
export interface UserSession {
  id: string
  userId: string
  deviceInfo: string
  ipAddress: string
  location?: string
  createdAt: string
  lastActiveAt: string
  expiresAt: string
}

// 角色查询参数
export interface RoleQueryParams {
  page?: number
  pageSize?: number
  search?: string
  tenantId?: string
}

// 创建角色请求
export interface CreateRoleRequest {
  name: string
  displayName: string
  description: string
  permissionIds: string[]
  tenantId: string
}

// 更新角色请求
export interface UpdateRoleRequest {
  displayName?: string
  description?: string
  permissionIds?: string[]
}

// 租户查询参数
export interface TenantQueryParams {
  page?: number
  pageSize?: number
  search?: string
  status?: 'active' | 'inactive' | 'suspended'
}

// 创建租户请求
export interface CreateTenantRequest {
  name: string
  displayName: string
  description: string
  plan: string
  settings?: Record<string, any>
}

// 更新租户请求
export interface UpdateTenantRequest {
  displayName?: string
  description?: string
  plan?: string
  status?: 'active' | 'inactive' | 'suspended'
  settings?: Record<string, any>
}

// 租户统计信息
export interface TenantStats {
  userCount: number
  appCount: number
  resourceUsage: {
    cpu: number
    memory: number
    storage: number
  }
  monthlyStats: {
    requests: number
    builds: number
    deployments: number
  }
}

/**
 * 用户管理相关API
 */
export const usersApi = {
  // ==================== 用户管理 ====================
  
  /**
   * 获取用户列表
   */
  getUsers(params?: UserQueryParams): Promise<ApiResponse<PaginatedResponse<User>>> {
    return request.get('/v1/users', { params })
  },

  /**
   * 获取用户详情
   */
  getUser(id: string): Promise<ApiResponse<User>> {
    return request.get(`/v1/users/${id}`)
  },

  /**
   * 创建用户
   */
  createUser(data: CreateUserRequest): Promise<ApiResponse<User>> {
    return request.post('/v1/users', data)
  },

  /**
   * 更新用户
   */
  updateUser(id: string, data: UpdateUserRequest): Promise<ApiResponse<User>> {
    return request.put(`/v1/users/${id}`, data)
  },

  /**
   * 删除用户
   */
  deleteUser(id: string): Promise<ApiResponse<void>> {
    return request.delete(`/v1/users/${id}`)
  },

  /**
   * 修改用户密码
   */
  changeUserPassword(id: string, data: ChangePasswordRequest): Promise<ApiResponse<void>> {
    return request.post(`/v1/users/${id}/change-password`, data)
  },

  /**
   * 重置用户密码
   */
  resetUserPassword(data: ResetPasswordRequest): Promise<ApiResponse<void>> {
    return request.post('/v1/users/reset-password', data)
  },

  /**
   * 获取用户角色
   */
  getUserRoles(id: string): Promise<ApiResponse<Role[]>> {
    return request.get(`/v1/users/${id}/roles`)
  },

  /**
   * 分配用户角色
   */
  assignUserRoles(id: string, data: AssignRoleRequest): Promise<ApiResponse<void>> {
    return request.post(`/v1/users/${id}/roles`, data)
  },

  /**
   * 取消用户角色
   */
  unassignUserRole(userId: string, roleId: string): Promise<ApiResponse<void>> {
    return request.delete(`/v1/users/${userId}/roles/${roleId}`)
  },

  /**
   * 获取用户权限
   */
  getUserPermissions(id: string): Promise<ApiResponse<Permission[]>> {
    return request.get(`/v1/users/${id}/permissions`)
  },

  /**
   * 获取用户会话
   */
  getUserSessions(id: string): Promise<ApiResponse<UserSession[]>> {
    return request.get(`/v1/users/${id}/sessions`)
  },

  /**
   * 删除用户会话
   */
  deleteUserSessions(id: string): Promise<ApiResponse<void>> {
    return request.delete(`/v1/users/${id}/sessions`)
  },

  // ==================== 角色管理 ====================
  
  /**
   * 获取角色列表
   */
  getRoles(params?: RoleQueryParams): Promise<ApiResponse<PaginatedResponse<Role>>> {
    return request.get('/v1/roles', { params })
  },

  /**
   * 获取角色详情
   */
  getRole(id: string): Promise<ApiResponse<Role>> {
    return request.get(`/v1/roles/${id}`)
  },

  /**
   * 创建角色
   */
  createRole(data: CreateRoleRequest): Promise<ApiResponse<Role>> {
    return request.post('/v1/roles', data)
  },

  /**
   * 更新角色
   */
  updateRole(id: string, data: UpdateRoleRequest): Promise<ApiResponse<Role>> {
    return request.put(`/v1/roles/${id}`, data)
  },

  /**
   * 删除角色
   */
  deleteRole(id: string): Promise<ApiResponse<void>> {
    return request.delete(`/v1/roles/${id}`)
  },

  /**
   * 获取角色用户
   */
  getRoleUsers(id: string): Promise<ApiResponse<User[]>> {
    return request.get(`/v1/roles/${id}/users`)
  },

  // ==================== 权限管理 ====================
  
  /**
   * 获取权限列表
   */
  getPermissions(): Promise<ApiResponse<Permission[]>> {
    return request.get('/v1/permissions')
  },

  /**
   * 检查权限
   */
  checkPermission(data: { resource: string; action: string }): Promise<ApiResponse<{ allowed: boolean }>> {
    return request.post('/v1/permissions/check', data)
  },

  // ==================== 租户管理 ====================
  
  /**
   * 获取租户列表
   */
  getTenants(params?: TenantQueryParams): Promise<ApiResponse<PaginatedResponse<Tenant>>> {
    return request.get('/v1/tenants', { params })
  },

  /**
   * 获取租户详情
   */
  getTenant(id: string): Promise<ApiResponse<Tenant>> {
    return request.get(`/v1/tenants/${id}`)
  },

  /**
   * 创建租户
   */
  createTenant(data: CreateTenantRequest): Promise<ApiResponse<Tenant>> {
    return request.post('/v1/tenants', data)
  },

  /**
   * 更新租户
   */
  updateTenant(id: string, data: UpdateTenantRequest): Promise<ApiResponse<Tenant>> {
    return request.put(`/v1/tenants/${id}`, data)
  },

  /**
   * 获取租户用户
   */
  getTenantUsers(id: string): Promise<ApiResponse<User[]>> {
    return request.get(`/v1/tenants/${id}/users`)
  },

  /**
   * 获取租户统计
   */
  getTenantStats(id: string): Promise<ApiResponse<TenantStats>> {
    return request.get(`/v1/tenants/${id}/stats`)
  },

  // ==================== 会话管理 ====================

  /**
   * 获取会话列表
   */
  getSessions(): Promise<ApiResponse<UserSession[]>> {
    return request.get('/v1/sessions')
  },

  /**
   * 获取会话详情
   */
  getSession(id: string): Promise<ApiResponse<UserSession>> {
    return request.get(`/v1/sessions/${id}`)
  },

  /**
   * 删除会话
   */
  deleteSession(id: string): Promise<ApiResponse<void>> {
    return request.delete(`/v1/sessions/${id}`)
  },

  // ==================== 审计日志 ====================

  /**
   * 获取审计日志
   */
  getAuditLogs(params?: {
    page?: number
    pageSize?: number
    userId?: string
    action?: string
    resource?: string
    startDate?: string
    endDate?: string
  }): Promise<ApiResponse<PaginatedResponse<{
    id: string
    userId: string
    username: string
    action: string
    resource: string
    resourceId: string
    details: Record<string, any>
    ipAddress: string
    userAgent: string
    createdAt: string
  }>>> {
    return request.get('/v1/audit/logs', { params })
  },

  /**
   * 获取审计统计
   */
  getAuditStats(): Promise<ApiResponse<{
    totalActions: number
    todayActions: number
    topUsers: Array<{ userId: string; username: string; count: number }>
    topActions: Array<{ action: string; count: number }>
  }>> {
    return request.get('/v1/audit/stats')
  },

  /**
   * 获取安全报告
   */
  getSecurityReport(): Promise<ApiResponse<{
    failedLogins: number
    suspiciousActivities: number
    activeUsers: number
    recommendations: string[]
  }>> {
    return request.get('/v1/audit/security-report')
  }
}
