import { request } from '@/utils/request'
import type { ApiResponse, User } from '@/types'

// 登录请求参数
export interface LoginRequest {
  username: string
  password: string
  captcha?: string
  remember?: boolean
}

// 登录响应
export interface LoginResponse {
  access_token: string
  refresh_token: string
  token_type: string
  expires_in: number
  user: User
}

// 注册请求参数
export interface RegisterRequest {
  username: string
  email: string
  password: string
  confirmPassword: string
  displayName: string
  captcha?: string
}

// 修改密码请求参数
export interface ChangePasswordRequest {
  oldPassword: string
  newPassword: string
  confirmPassword: string
}

// 重置密码请求参数
export interface ResetPasswordRequest {
  email: string
  captcha?: string
}

/**
 * 认证相关API
 */
export const authApi = {
  /**
   * 用户登录
   */
  login(data: LoginRequest): Promise<ApiResponse<LoginResponse>> {
    return request.post('/v1/auth/login', data)
  },

  /**
   * 用户登出
   */
  logout(): Promise<ApiResponse<void>> {
    return request.post('/v1/auth/logout')
  },

  /**
   * 刷新token
   */
  refreshToken(): Promise<ApiResponse<{ access_token: string }>> {
    return request.post('/v1/auth/refresh')
  },

  /**
   * 用户注册
   */
  register(data: RegisterRequest): Promise<ApiResponse<User>> {
    return request.post('/v1/auth/register', data)
  },

  /**
   * 获取当前用户信息
   */
  getCurrentUser(): Promise<ApiResponse<User>> {
    return request.get('/v1/auth/me')
  },

  /**
   * 修改密码
   */
  changePassword(data: ChangePasswordRequest): Promise<ApiResponse<void>> {
    return request.post('/v1/auth/change-password', data)
  },

  /**
   * 重置密码
   */
  resetPassword(data: ResetPasswordRequest): Promise<ApiResponse<void>> {
    return request.post('/v1/auth/reset-password', data)
  },

  /**
   * 确认重置密码
   */
  confirmResetPassword(data: { token: string; password: string }): Promise<ApiResponse<void>> {
    return request.post('/v1/auth/confirm-reset', data)
  },

  /**
   * 获取用户权限列表
   */
  getUserPermissions(userId: string): Promise<ApiResponse<any[]>> {
    return request.get(`/v1/users/${userId}/permissions`)
  },

  /**
   * 获取验证码
   */
  getCaptcha(): Promise<ApiResponse<{ image: string; key: string }>> {
    return request.get('/v1/auth/captcha')
  },

  /**
   * 管理员登录
   */
  adminLogin(data: AdminLoginRequest): Promise<ApiResponse<AdminLoginResponse>> {
    return request.post('/v1/admin/login', data)
  },

  /**
   * 管理员登出
   */
  adminLogout(): Promise<ApiResponse<void>> {
    return request.post('/v1/admin/logout')
  }
}

/**
 * IDP 认证相关API
 */
export const idpApi = {
  /**
   * 获取可用的 IDP 提供商列表
   */
  getProviders(): Promise<ApiResponse<IDPProvider[]>> {
    return request.get('/v1/idp/providers')
  },

  /**
   * 发起 IDP 登录
   */
  initiateLogin(data: IDPLoginRequest): Promise<ApiResponse<IDPAuthInitResponse>> {
    return request.post(`/v1/idp/login/${data.provider_name}`, data)
  },

  /**
   * 处理 IDP 回调
   */
  handleCallback(data: IDPCallbackRequest): Promise<ApiResponse<IDPAuthResult>> {
    return request.post(`/v1/idp/callback/${data.provider_name}`, data)
  },

  /**
   * 关联 IDP 账号
   */
  linkAccount(data: LinkIDPAccountRequest): Promise<ApiResponse<IDPAccount>> {
    return request.post('/v1/idp/link', data)
  },

  /**
   * 取消关联 IDP 账号
   */
  unlinkAccount(accountId: string): Promise<ApiResponse<void>> {
    return request.delete(`/v1/idp/unlink/${accountId}`)
  },

  /**
   * 获取用户的 IDP 账号列表
   */
  getUserAccounts(): Promise<ApiResponse<IDPAccountResponse[]>> {
    return request.get('/v1/idp/accounts')
  },

  /**
   * 同步用户信息从 IDP
   */
  syncUser(accountId: string): Promise<ApiResponse<IDPSyncResult>> {
    return request.post(`/v1/idp/sync/${accountId}`)
  }
}

// IDP 相关类型定义
export interface IDPProvider {
  id: string
  name: string
  type: 'oidc' | 'oauth2' | 'saml' | 'ldap'
  enabled: boolean
  priority: number
  auto_create_user: boolean
  auto_link_user: boolean
}

export interface IDPLoginRequest {
  provider_name: string
  tenant_id: string
  redirect_url?: string
}

export interface IDPCallbackRequest {
  provider_name: string
  code: string
  state: string
  tenant_id: string
}

export interface IDPAuthInitResponse {
  auth_url: string
  state: string
  nonce: string
  expires_at: number
}

export interface IDPAuthResult {
  success: boolean
  user?: User
  idp_account?: IDPAccount
  token_pair?: {
    access_token: string
    refresh_token: string
    expires_in: number
  }
  is_new_user: boolean
  is_linked: boolean
  error_code?: string
  error_msg?: string
}

export interface IDPAccount {
  id: string
  user_id: string
  idp_provider_id: string
  idp_user_id: string
  idp_username: string
  idp_email: string
  status: 'active' | 'inactive' | 'revoked'
  last_sync_at?: string
  created_at: string
  updated_at: string
}

export interface IDPAccountResponse {
  id: string
  user_id: string
  idp_provider_id: string
  idp_user_id: string
  idp_username: string
  idp_email: string
  status: 'active' | 'inactive' | 'revoked'
  provider_name: string
  last_sync_at?: string
  created_at: string
  updated_at: string
}

export interface LinkIDPAccountRequest {
  user_id: string
  idp_provider_id: string
  idp_user_id: string
  idp_username?: string
  idp_email?: string
}

export interface IDPSyncResult {
  success: boolean
  updated_users: number
  created_users: number
  failed_users: number
  errors?: string[]
}

// 管理员认证相关类型定义
export interface AdminLoginRequest {
  username: string
  password: string
  tenant_id: string
}

export interface AdminLoginResponse {
  access_token: string
  refresh_token: string
  expires_in: number
  token_type: string
  user: User
}
