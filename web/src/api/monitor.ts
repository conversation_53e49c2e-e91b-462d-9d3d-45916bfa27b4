import request from '@/utils/request'
import type { ApiResponse, PaginatedResponse, MetricData } from '@/types'

// 系统指标数据
export interface SystemMetrics {
  system: {
    cpu_usage: number
    memory_usage: number
    disk_usage: number
    load_average: number[]
    uptime: number
  }
  applications: {
    total: number
    running: number
    stopped: number
    failed: number
  }
  requests: {
    total: number
    per_second: number
    error_rate: number
    avg_response_time: number
  }
  database: {
    connections: number
    queries_per_second: number
    slow_queries: number
  }
  cache: {
    hit_rate: number
    memory_usage: number
    keys_count: number
  }
}

// 指标查询参数
export interface MetricsQueryParams {
  start_time?: string
  end_time?: string
  interval?: string
  metrics?: string[]
  aggregation?: 'avg' | 'sum' | 'min' | 'max' | 'count'
}

// 历史指标数据
export interface HistoricalMetrics {
  timestamps: number[]
  metrics: {
    [key: string]: number[]
  }
}

// 日志查询参数
export interface LogQueryParams {
  page?: number
  pageSize?: number
  app_id?: string
  level?: 'debug' | 'info' | 'warn' | 'error' | 'fatal'
  start_time?: string
  end_time?: string
  keyword?: string
  source?: string
}

// 日志条目
export interface LogEntry {
  id: string
  timestamp: string
  level: 'debug' | 'info' | 'warn' | 'error' | 'fatal'
  message: string
  source: string
  app_id?: string
  user_id?: string
  request_id?: string
  metadata?: Record<string, any>
}

// 告警规则
export interface AlertRule {
  id: string
  name: string
  description: string
  metric: string
  condition: 'gt' | 'lt' | 'eq' | 'ne' | 'gte' | 'lte'
  threshold: number
  duration: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  enabled: boolean
  notifications: {
    email?: string[]
    webhook?: string
    slack?: string
  }
  created_at: string
  updated_at: string
}

// 创建告警规则请求
export interface CreateAlertRuleRequest {
  name: string
  description: string
  metric: string
  condition: 'gt' | 'lt' | 'eq' | 'ne' | 'gte' | 'lte'
  threshold: number
  duration: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  notifications: {
    email?: string[]
    webhook?: string
    slack?: string
  }
}

// 告警事件
export interface Alert {
  id: string
  rule_id: string
  rule_name: string
  status: 'firing' | 'resolved'
  severity: 'low' | 'medium' | 'high' | 'critical'
  message: string
  current_value: number
  threshold: number
  started_at: string
  resolved_at?: string
  duration?: string
  labels: Record<string, string>
  annotations: Record<string, string>
}

// 告警查询参数
export interface AlertQueryParams {
  page?: number
  pageSize?: number
  status?: 'firing' | 'resolved'
  severity?: 'low' | 'medium' | 'high' | 'critical'
  rule_id?: string
  start_time?: string
  end_time?: string
}

// 性能监控数据
export interface PerformanceMetrics {
  response_times: {
    p50: number
    p95: number
    p99: number
    avg: number
  }
  throughput: {
    requests_per_second: number
    bytes_per_second: number
  }
  error_rates: {
    total_errors: number
    error_rate: number
    by_status: Record<string, number>
  }
  resource_usage: {
    cpu_percent: number
    memory_percent: number
    disk_io: number
    network_io: number
  }
}

// 健康检查结果
export interface HealthCheckResult {
  status: 'healthy' | 'unhealthy' | 'degraded'
  services: {
    [serviceName: string]: {
      status: 'up' | 'down' | 'degraded'
      response_time: number
      last_check: string
      error?: string
    }
  }
  dependencies: {
    database: 'connected' | 'disconnected'
    cache: 'connected' | 'disconnected'
    external_apis: Record<string, 'up' | 'down'>
  }
  uptime: number
  version: string
}

/**
 * 监控相关API
 */
export const monitorApi = {
  // ==================== 系统指标 ====================
  
  /**
   * 获取当前系统指标
   */
  getCurrentMetrics(): Promise<ApiResponse<SystemMetrics>> {
    return request.get('/v1/monitoring/metrics/current')
  },

  /**
   * 获取最新指标
   */
  getLatestMetrics(): Promise<ApiResponse<SystemMetrics>> {
    return request.get('/v1/monitoring/metrics/latest')
  },

  /**
   * 获取历史指标
   */
  getHistoricalMetrics(params: MetricsQueryParams): Promise<ApiResponse<HistoricalMetrics>> {
    return request.get('/v1/monitoring/metrics/history', { params })
  },

  /**
   * 获取聚合指标
   */
  getAggregatedMetrics(params: MetricsQueryParams): Promise<ApiResponse<HistoricalMetrics>> {
    return request.get('/v1/monitoring/metrics/aggregated', { params })
  },

  /**
   * 获取指标摘要
   */
  getMetricsSummary(): Promise<ApiResponse<{
    current: SystemMetrics
    trends: {
      cpu_trend: 'up' | 'down' | 'stable'
      memory_trend: 'up' | 'down' | 'stable'
      request_trend: 'up' | 'down' | 'stable'
    }
    alerts_count: number
  }>> {
    return request.get('/v1/monitoring/metrics/summary')
  },

  /**
   * 获取性能指标
   */
  getPerformanceMetrics(params?: {
    start_time?: string
    end_time?: string
    app_id?: string
  }): Promise<ApiResponse<PerformanceMetrics>> {
    return request.get('/v1/monitoring/performance', { params })
  },

  /**
   * 导出指标数据
   */
  exportMetrics(params: {
    start_time: string
    end_time: string
    format?: 'json' | 'csv' | 'prometheus'
    metrics?: string[]
  }): Promise<ApiResponse<{ download_url: string }>> {
    return request.get('/v1/monitoring/metrics/export', { params })
  },

  // ==================== 日志管理 ====================
  
  /**
   * 获取日志列表
   */
  getLogs(params?: LogQueryParams): Promise<ApiResponse<PaginatedResponse<LogEntry>>> {
    return request.get('/v1/monitoring/logs', { params })
  },

  /**
   * 搜索日志
   */
  searchLogs(params: {
    query: string
    start_time?: string
    end_time?: string
    app_id?: string
    level?: string
  }): Promise<ApiResponse<LogEntry[]>> {
    return request.get('/v1/monitoring/logs/search', { params })
  },

  /**
   * 获取日志统计
   */
  getLogStats(params?: {
    start_time?: string
    end_time?: string
    app_id?: string
  }): Promise<ApiResponse<{
    total_logs: number
    by_level: Record<string, number>
    by_app: Record<string, number>
    error_rate: number
  }>> {
    return request.get('/v1/monitoring/logs/stats', { params })
  },

  // ==================== 告警管理 ====================
  
  /**
   * 获取告警列表
   */
  getAlerts(params?: AlertQueryParams): Promise<ApiResponse<PaginatedResponse<Alert>>> {
    return request.get('/v1/monitoring/alerts', { params })
  },

  /**
   * 获取告警规则列表
   */
  getAlertRules(): Promise<ApiResponse<AlertRule[]>> {
    return request.get('/v1/monitoring/alert-rules')
  },

  /**
   * 创建告警规则
   */
  createAlertRule(data: CreateAlertRuleRequest): Promise<ApiResponse<AlertRule>> {
    return request.post('/v1/monitoring/alert-rules', data)
  },

  /**
   * 更新告警规则
   */
  updateAlertRule(id: string, data: Partial<CreateAlertRuleRequest>): Promise<ApiResponse<AlertRule>> {
    return request.put(`/v1/monitoring/alert-rules/${id}`, data)
  },

  /**
   * 删除告警规则
   */
  deleteAlertRule(id: string): Promise<ApiResponse<void>> {
    return request.delete(`/v1/monitoring/alert-rules/${id}`)
  },

  /**
   * 启用/禁用告警规则
   */
  toggleAlertRule(id: string, enabled: boolean): Promise<ApiResponse<AlertRule>> {
    return request.patch(`/v1/monitoring/alert-rules/${id}`, { enabled })
  },

  /**
   * 确认告警
   */
  acknowledgeAlert(id: string): Promise<ApiResponse<void>> {
    return request.post(`/v1/monitoring/alerts/${id}/acknowledge`)
  },

  /**
   * 解决告警
   */
  resolveAlert(id: string): Promise<ApiResponse<void>> {
    return request.post(`/v1/monitoring/alerts/${id}/resolve`)
  },

  // ==================== 健康检查 ====================
  
  /**
   * 获取系统健康状态
   */
  getHealthStatus(): Promise<ApiResponse<HealthCheckResult>> {
    return request.get('/v1/monitoring/health')
  },

  /**
   * 获取服务状态
   */
  getServiceStatus(): Promise<ApiResponse<{
    services: Array<{
      name: string
      status: 'up' | 'down' | 'degraded'
      uptime: number
      version: string
      last_check: string
    }>
  }>> {
    return request.get('/v1/monitoring/services/status')
  }
}
