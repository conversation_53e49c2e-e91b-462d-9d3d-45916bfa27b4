import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { faasApi } from '@/api/faas'
import type { 
  FaaSFunction,
  FunctionExecution,
  FunctionVersion,
  FunctionTrigger,
  FunctionMetrics,
  FunctionQueryParams,
  ExecutionQueryParams,
  CreateFunctionRequest,
  UpdateFunctionRequest,
  FunctionExecuteRequest,
  FunctionExecuteResponse,
  CreateTriggerRequest,
  ExecutorMetrics,
  PoolStats
} from '@/api/faas'
import type { PaginatedResponse } from '@/types'

export const useFaasStore = defineStore('faas', () => {
  // 函数相关状态
  const functions = ref<FaaSFunction[]>([])
  const currentFunction = ref<FaaSFunction | null>(null)
  const functionsLoading = ref(false)
  const functionsPagination = ref({
    page: 1,
    pageSize: 20,
    total: 0
  })

  // 执行记录相关状态
  const executions = ref<FunctionExecution[]>([])
  const currentExecution = ref<FunctionExecution | null>(null)
  const executionsLoading = ref(false)
  const executionsPagination = ref({
    page: 1,
    pageSize: 20,
    total: 0
  })

  // 函数版本相关状态
  const functionVersions = ref<FunctionVersion[]>([])
  const versionsLoading = ref(false)

  // 触发器相关状态
  const triggers = ref<FunctionTrigger[]>([])
  const currentTrigger = ref<FunctionTrigger | null>(null)
  const triggersLoading = ref(false)

  // 指标相关状态
  const functionMetrics = ref<FunctionMetrics | null>(null)
  const executorMetrics = ref<ExecutorMetrics | null>(null)
  const poolStats = ref<PoolStats | null>(null)
  const metricsLoading = ref(false)

  // 执行状态
  const executing = ref(false)
  const executionResult = ref<FunctionExecuteResponse | null>(null)

  // 错误状态
  const error = ref<string | null>(null)

  // 计算属性
  const activeFunctions = computed(() => 
    functions.value.filter(func => func.status === 'active')
  )

  const inactiveFunctions = computed(() => 
    functions.value.filter(func => func.status === 'inactive')
  )

  const deprecatedFunctions = computed(() => 
    functions.value.filter(func => func.status === 'deprecated')
  )

  const functionsByRuntime = computed(() => {
    const runtimeGroups: Record<string, FaaSFunction[]> = {}
    functions.value.forEach(func => {
      if (!runtimeGroups[func.runtime]) {
        runtimeGroups[func.runtime] = []
      }
      runtimeGroups[func.runtime].push(func)
    })
    return runtimeGroups
  })

  const runningExecutions = computed(() => 
    executions.value.filter(exec => exec.status === 'running')
  )

  const successfulExecutions = computed(() => 
    executions.value.filter(exec => exec.status === 'success')
  )

  const failedExecutions = computed(() => 
    executions.value.filter(exec => exec.status === 'failed')
  )

  const pendingExecutions = computed(() => 
    executions.value.filter(exec => exec.status === 'pending')
  )

  const executionsByFunction = computed(() => {
    const functionGroups: Record<string, FunctionExecution[]> = {}
    executions.value.forEach(exec => {
      if (!functionGroups[exec.function_id]) {
        functionGroups[exec.function_id] = []
      }
      functionGroups[exec.function_id].push(exec)
    })
    return functionGroups
  })

  const enabledTriggers = computed(() => 
    triggers.value.filter(trigger => trigger.enabled)
  )

  const disabledTriggers = computed(() => 
    triggers.value.filter(trigger => !trigger.enabled)
  )

  const triggersByType = computed(() => {
    const typeGroups: Record<string, FunctionTrigger[]> = {}
    triggers.value.forEach(trigger => {
      if (!typeGroups[trigger.type]) {
        typeGroups[trigger.type] = []
      }
      typeGroups[trigger.type].push(trigger)
    })
    return typeGroups
  })

  // 函数管理操作
  const fetchFunctions = async (params?: FunctionQueryParams) => {
    try {
      functionsLoading.value = true
      error.value = null
      
      const response = await faasApi.getFunctions(params)
      const data = response.data as PaginatedResponse<FaaSFunction>
      
      functions.value = data.items
      functionsPagination.value = {
        page: data.page,
        pageSize: data.pageSize,
        total: data.total
      }
    } catch (err: any) {
      error.value = err.message || '获取函数列表失败'
      console.error('获取函数列表失败:', err)
    } finally {
      functionsLoading.value = false
    }
  }

  const fetchFunction = async (functionId: string) => {
    try {
      functionsLoading.value = true
      error.value = null
      
      const response = await faasApi.getFunction(functionId)
      currentFunction.value = response.data
    } catch (err: any) {
      error.value = err.message || '获取函数详情失败'
      console.error('获取函数详情失败:', err)
    } finally {
      functionsLoading.value = false
    }
  }

  const createFunction = async (data: CreateFunctionRequest) => {
    try {
      functionsLoading.value = true
      error.value = null
      
      const response = await faasApi.createFunction(data)
      const newFunction = response.data
      
      functions.value.unshift(newFunction)
      functionsPagination.value.total += 1
      
      return newFunction
    } catch (err: any) {
      error.value = err.message || '创建函数失败'
      console.error('创建函数失败:', err)
      throw err
    } finally {
      functionsLoading.value = false
    }
  }

  const updateFunction = async (functionId: string, data: UpdateFunctionRequest) => {
    try {
      functionsLoading.value = true
      error.value = null
      
      const response = await faasApi.updateFunction(functionId, data)
      const updatedFunction = response.data
      
      const index = functions.value.findIndex(func => func.function_id === functionId)
      if (index !== -1) {
        functions.value[index] = updatedFunction
      }
      
      if (currentFunction.value?.function_id === functionId) {
        currentFunction.value = updatedFunction
      }
      
      return updatedFunction
    } catch (err: any) {
      error.value = err.message || '更新函数失败'
      console.error('更新函数失败:', err)
      throw err
    } finally {
      functionsLoading.value = false
    }
  }

  const deleteFunction = async (functionId: string) => {
    try {
      functionsLoading.value = true
      error.value = null
      
      await faasApi.deleteFunction(functionId)
      
      functions.value = functions.value.filter(func => func.function_id !== functionId)
      functionsPagination.value.total -= 1
      
      if (currentFunction.value?.function_id === functionId) {
        currentFunction.value = null
      }
    } catch (err: any) {
      error.value = err.message || '删除函数失败'
      console.error('删除函数失败:', err)
      throw err
    } finally {
      functionsLoading.value = false
    }
  }

  // 函数执行操作
  const executeFunction = async (data: FunctionExecuteRequest) => {
    try {
      executing.value = true
      error.value = null
      
      const response = await faasApi.executeFunction(data)
      executionResult.value = response.data
      
      return response.data
    } catch (err: any) {
      error.value = err.message || '执行函数失败'
      console.error('执行函数失败:', err)
      throw err
    } finally {
      executing.value = false
    }
  }

  const asyncExecuteFunction = async (data: FunctionExecuteRequest) => {
    try {
      executing.value = true
      error.value = null
      
      const response = await faasApi.asyncExecuteFunction(data)
      return response.data
    } catch (err: any) {
      error.value = err.message || '异步执行函数失败'
      console.error('异步执行函数失败:', err)
      throw err
    } finally {
      executing.value = false
    }
  }

  const getExecutionResult = async (requestId: string) => {
    try {
      executionsLoading.value = true
      error.value = null
      
      const response = await faasApi.getExecutionResult(requestId)
      return response.data
    } catch (err: any) {
      error.value = err.message || '获取执行结果失败'
      console.error('获取执行结果失败:', err)
      throw err
    } finally {
      executionsLoading.value = false
    }
  }

  // 执行记录管理
  const fetchExecutions = async (params?: ExecutionQueryParams) => {
    try {
      executionsLoading.value = true
      error.value = null
      
      const response = await faasApi.getExecutions(params)
      const data = response.data as PaginatedResponse<FunctionExecution>
      
      executions.value = data.items
      executionsPagination.value = {
        page: data.page,
        pageSize: data.pageSize,
        total: data.total
      }
    } catch (err: any) {
      error.value = err.message || '获取执行记录失败'
      console.error('获取执行记录失败:', err)
    } finally {
      executionsLoading.value = false
    }
  }

  const fetchFunctionExecutions = async (functionId: string, params?: ExecutionQueryParams) => {
    try {
      executionsLoading.value = true
      error.value = null
      
      const response = await faasApi.getFunctionExecutions(functionId, params)
      const data = response.data as PaginatedResponse<FunctionExecution>
      
      executions.value = data.items
      executionsPagination.value = {
        page: data.page,
        pageSize: data.pageSize,
        total: data.total
      }
    } catch (err: any) {
      error.value = err.message || '获取函数执行记录失败'
      console.error('获取函数执行记录失败:', err)
    } finally {
      executionsLoading.value = false
    }
  }

  const cancelExecution = async (requestId: string) => {
    try {
      executionsLoading.value = true
      error.value = null
      
      await faasApi.cancelExecution(requestId)
      
      // 更新本地状态
      const index = executions.value.findIndex(exec => exec.request_id === requestId)
      if (index !== -1) {
        executions.value[index].status = 'failed'
      }
    } catch (err: any) {
      error.value = err.message || '取消执行失败'
      console.error('取消执行失败:', err)
      throw err
    } finally {
      executionsLoading.value = false
    }
  }

  // 版本管理
  const fetchFunctionVersions = async (functionId: string) => {
    try {
      versionsLoading.value = true
      error.value = null
      
      const response = await faasApi.getFunctionVersions(functionId)
      functionVersions.value = response.data
    } catch (err: any) {
      error.value = err.message || '获取函数版本失败'
      console.error('获取函数版本失败:', err)
    } finally {
      versionsLoading.value = false
    }
  }

  const createFunctionVersion = async (functionId: string, data: any) => {
    try {
      versionsLoading.value = true
      error.value = null
      
      const response = await faasApi.createFunctionVersion(functionId, data)
      const newVersion = response.data
      
      functionVersions.value.unshift(newVersion)
      
      return newVersion
    } catch (err: any) {
      error.value = err.message || '创建函数版本失败'
      console.error('创建函数版本失败:', err)
      throw err
    } finally {
      versionsLoading.value = false
    }
  }

  const activateFunctionVersion = async (functionId: string, version: string) => {
    try {
      versionsLoading.value = true
      error.value = null
      
      await faasApi.activateFunctionVersion(functionId, version)
      
      // 更新版本状态
      functionVersions.value.forEach(v => {
        v.is_active = v.version === version
      })
    } catch (err: any) {
      error.value = err.message || '激活函数版本失败'
      console.error('激活函数版本失败:', err)
      throw err
    } finally {
      versionsLoading.value = false
    }
  }

  // 触发器管理
  const fetchFunctionTriggers = async (functionId: string) => {
    try {
      triggersLoading.value = true
      error.value = null
      
      const response = await faasApi.getFunctionTriggers(functionId)
      triggers.value = response.data
    } catch (err: any) {
      error.value = err.message || '获取函数触发器失败'
      console.error('获取函数触发器失败:', err)
    } finally {
      triggersLoading.value = false
    }
  }

  const createTrigger = async (data: CreateTriggerRequest) => {
    try {
      triggersLoading.value = true
      error.value = null
      
      const response = await faasApi.createTrigger(data)
      const newTrigger = response.data
      
      triggers.value.unshift(newTrigger)
      
      return newTrigger
    } catch (err: any) {
      error.value = err.message || '创建触发器失败'
      console.error('创建触发器失败:', err)
      throw err
    } finally {
      triggersLoading.value = false
    }
  }

  const updateTrigger = async (triggerId: number, data: Partial<CreateTriggerRequest>) => {
    try {
      triggersLoading.value = true
      error.value = null
      
      const response = await faasApi.updateTrigger(triggerId, data)
      const updatedTrigger = response.data
      
      const index = triggers.value.findIndex(trigger => trigger.id === triggerId)
      if (index !== -1) {
        triggers.value[index] = updatedTrigger
      }
      
      return updatedTrigger
    } catch (err: any) {
      error.value = err.message || '更新触发器失败'
      console.error('更新触发器失败:', err)
      throw err
    } finally {
      triggersLoading.value = false
    }
  }

  const deleteTrigger = async (triggerId: number) => {
    try {
      triggersLoading.value = true
      error.value = null
      
      await faasApi.deleteTrigger(triggerId)
      
      triggers.value = triggers.value.filter(trigger => trigger.id !== triggerId)
    } catch (err: any) {
      error.value = err.message || '删除触发器失败'
      console.error('删除触发器失败:', err)
      throw err
    } finally {
      triggersLoading.value = false
    }
  }

  const toggleTrigger = async (triggerId: number, enabled: boolean) => {
    try {
      triggersLoading.value = true
      error.value = null
      
      const response = await faasApi.toggleTrigger(triggerId, enabled)
      const updatedTrigger = response.data
      
      const index = triggers.value.findIndex(trigger => trigger.id === triggerId)
      if (index !== -1) {
        triggers.value[index] = updatedTrigger
      }
      
      return updatedTrigger
    } catch (err: any) {
      error.value = err.message || '切换触发器状态失败'
      console.error('切换触发器状态失败:', err)
      throw err
    } finally {
      triggersLoading.value = false
    }
  }

  // 监控和指标
  const fetchExecutorMetrics = async () => {
    try {
      metricsLoading.value = true
      error.value = null
      
      const response = await faasApi.getExecutorMetrics()
      executorMetrics.value = response.data
    } catch (err: any) {
      error.value = err.message || '获取执行器指标失败'
      console.error('获取执行器指标失败:', err)
    } finally {
      metricsLoading.value = false
    }
  }

  const fetchPoolStats = async () => {
    try {
      metricsLoading.value = true
      error.value = null
      
      const response = await faasApi.getPoolStats()
      poolStats.value = response.data
    } catch (err: any) {
      error.value = err.message || '获取容器池统计失败'
      console.error('获取容器池统计失败:', err)
    } finally {
      metricsLoading.value = false
    }
  }

  const fetchFunctionMetrics = async (functionId: string) => {
    try {
      metricsLoading.value = true
      error.value = null
      
      const response = await faasApi.getFunctionMetrics(functionId)
      functionMetrics.value = response.data
    } catch (err: any) {
      error.value = err.message || '获取函数指标失败'
      console.error('获取函数指标失败:', err)
    } finally {
      metricsLoading.value = false
    }
  }

  const healthCheck = async () => {
    try {
      const response = await faasApi.healthCheck()
      return response.data
    } catch (err: any) {
      error.value = err.message || '健康检查失败'
      console.error('健康检查失败:', err)
      throw err
    }
  }

  // 清理状态
  const clearError = () => {
    error.value = null
  }

  const clearCurrentFunction = () => {
    currentFunction.value = null
  }

  const clearCurrentExecution = () => {
    currentExecution.value = null
  }

  const clearExecutionResult = () => {
    executionResult.value = null
  }

  const clearFunctions = () => {
    functions.value = []
    functionsPagination.value = {
      page: 1,
      pageSize: 20,
      total: 0
    }
  }

  const clearExecutions = () => {
    executions.value = []
    executionsPagination.value = {
      page: 1,
      pageSize: 20,
      total: 0
    }
  }

  return {
    // 状态
    functions,
    currentFunction,
    functionsLoading,
    functionsPagination,
    executions,
    currentExecution,
    executionsLoading,
    executionsPagination,
    functionVersions,
    versionsLoading,
    triggers,
    currentTrigger,
    triggersLoading,
    functionMetrics,
    executorMetrics,
    poolStats,
    metricsLoading,
    executing,
    executionResult,
    error,

    // 计算属性
    activeFunctions,
    inactiveFunctions,
    deprecatedFunctions,
    functionsByRuntime,
    runningExecutions,
    successfulExecutions,
    failedExecutions,
    pendingExecutions,
    executionsByFunction,
    enabledTriggers,
    disabledTriggers,
    triggersByType,

    // 方法
    fetchFunctions,
    fetchFunction,
    createFunction,
    updateFunction,
    deleteFunction,
    executeFunction,
    asyncExecuteFunction,
    getExecutionResult,
    fetchExecutions,
    fetchFunctionExecutions,
    cancelExecution,
    fetchFunctionVersions,
    createFunctionVersion,
    activateFunctionVersion,
    fetchFunctionTriggers,
    createTrigger,
    updateTrigger,
    deleteTrigger,
    toggleTrigger,
    fetchExecutorMetrics,
    fetchPoolStats,
    fetchFunctionMetrics,
    healthCheck,
    clearError,
    clearCurrentFunction,
    clearCurrentExecution,
    clearExecutionResult,
    clearFunctions,
    clearExecutions
  }
})
