import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { cicdApi } from '@/api/cicd'
import type { 
  Pipeline, 
  Build, 
  Deployment,
  PipelineQueryParams,
  BuildQueryParams,
  DeploymentQueryParams,
  CreatePipelineRequest,
  UpdatePipelineRequest,
  CreateBuildRequest,
  CreateDeploymentRequest
} from '@/api/cicd'
import type { PaginatedResponse } from '@/types'

export const useCicdStore = defineStore('cicd', () => {
  // 流水线相关状态
  const pipelines = ref<Pipeline[]>([])
  const currentPipeline = ref<Pipeline | null>(null)
  const pipelinesLoading = ref(false)
  const pipelinesPagination = ref({
    page: 1,
    pageSize: 20,
    total: 0
  })

  // 构建相关状态
  const builds = ref<Build[]>([])
  const currentBuild = ref<Build | null>(null)
  const buildsLoading = ref(false)
  const buildsPagination = ref({
    page: 1,
    pageSize: 20,
    total: 0
  })

  // 部署相关状态
  const deployments = ref<Deployment[]>([])
  const currentDeployment = ref<Deployment | null>(null)
  const deploymentsLoading = ref(false)
  const deploymentsPagination = ref({
    page: 1,
    pageSize: 20,
    total: 0
  })

  // 错误状态
  const error = ref<string | null>(null)

  // 计算属性
  const activePipelines = computed(() => 
    pipelines.value.filter(pipeline => pipeline.status === 'active')
  )

  const runningBuilds = computed(() => 
    builds.value.filter(build => build.status === 'running')
  )

  const successfulBuilds = computed(() => 
    builds.value.filter(build => build.status === 'success')
  )

  const failedBuilds = computed(() => 
    builds.value.filter(build => build.status === 'failed')
  )

  const runningDeployments = computed(() => 
    deployments.value.filter(deployment => deployment.status === 'running')
  )

  const successfulDeployments = computed(() => 
    deployments.value.filter(deployment => deployment.status === 'success')
  )

  const failedDeployments = computed(() => 
    deployments.value.filter(deployment => deployment.status === 'failed')
  )

  const buildsByPipeline = computed(() => {
    const pipelineGroups: Record<string, Build[]> = {}
    builds.value.forEach(build => {
      if (!pipelineGroups[build.pipeline_id]) {
        pipelineGroups[build.pipeline_id] = []
      }
      pipelineGroups[build.pipeline_id].push(build)
    })
    return pipelineGroups
  })

  // 流水线管理操作
  const fetchPipelines = async (params?: PipelineQueryParams) => {
    try {
      pipelinesLoading.value = true
      error.value = null
      
      const response = await cicdApi.getPipelines(params)
      const data = response.data as PaginatedResponse<Pipeline>
      
      pipelines.value = data.items
      pipelinesPagination.value = {
        page: data.page,
        pageSize: data.pageSize,
        total: data.total
      }
    } catch (err: any) {
      error.value = err.message || '获取流水线列表失败'
      console.error('获取流水线列表失败:', err)
    } finally {
      pipelinesLoading.value = false
    }
  }

  const fetchPipeline = async (id: string) => {
    try {
      pipelinesLoading.value = true
      error.value = null
      
      const response = await cicdApi.getPipeline(id)
      currentPipeline.value = response.data
    } catch (err: any) {
      error.value = err.message || '获取流水线详情失败'
      console.error('获取流水线详情失败:', err)
    } finally {
      pipelinesLoading.value = false
    }
  }

  const createPipeline = async (data: CreatePipelineRequest) => {
    try {
      pipelinesLoading.value = true
      error.value = null
      
      const response = await cicdApi.createPipeline(data)
      const newPipeline = response.data
      
      pipelines.value.unshift(newPipeline)
      pipelinesPagination.value.total += 1
      
      return newPipeline
    } catch (err: any) {
      error.value = err.message || '创建流水线失败'
      console.error('创建流水线失败:', err)
      throw err
    } finally {
      pipelinesLoading.value = false
    }
  }

  const updatePipeline = async (id: string, data: UpdatePipelineRequest) => {
    try {
      pipelinesLoading.value = true
      error.value = null
      
      const response = await cicdApi.updatePipeline(id, data)
      const updatedPipeline = response.data
      
      const index = pipelines.value.findIndex(pipeline => pipeline.id === id)
      if (index !== -1) {
        pipelines.value[index] = updatedPipeline
      }
      
      if (currentPipeline.value?.id === id) {
        currentPipeline.value = updatedPipeline
      }
      
      return updatedPipeline
    } catch (err: any) {
      error.value = err.message || '更新流水线失败'
      console.error('更新流水线失败:', err)
      throw err
    } finally {
      pipelinesLoading.value = false
    }
  }

  const deletePipeline = async (id: string) => {
    try {
      pipelinesLoading.value = true
      error.value = null
      
      await cicdApi.deletePipeline(id)
      
      pipelines.value = pipelines.value.filter(pipeline => pipeline.id !== id)
      pipelinesPagination.value.total -= 1
      
      if (currentPipeline.value?.id === id) {
        currentPipeline.value = null
      }
    } catch (err: any) {
      error.value = err.message || '删除流水线失败'
      console.error('删除流水线失败:', err)
      throw err
    } finally {
      pipelinesLoading.value = false
    }
  }

  const triggerPipeline = async (id: string) => {
    try {
      pipelinesLoading.value = true
      error.value = null
      
      const response = await cicdApi.triggerPipeline(id)
      return response.data
    } catch (err: any) {
      error.value = err.message || '触发流水线失败'
      console.error('触发流水线失败:', err)
      throw err
    } finally {
      pipelinesLoading.value = false
    }
  }

  // 构建管理操作
  const fetchBuilds = async (params?: BuildQueryParams) => {
    try {
      buildsLoading.value = true
      error.value = null
      
      const response = await cicdApi.getBuilds(params)
      const data = response.data as PaginatedResponse<Build>
      
      builds.value = data.items
      buildsPagination.value = {
        page: data.page,
        pageSize: data.pageSize,
        total: data.total
      }
    } catch (err: any) {
      error.value = err.message || '获取构建列表失败'
      console.error('获取构建列表失败:', err)
    } finally {
      buildsLoading.value = false
    }
  }

  const fetchBuild = async (id: string) => {
    try {
      buildsLoading.value = true
      error.value = null
      
      const response = await cicdApi.getBuild(id)
      currentBuild.value = response.data
    } catch (err: any) {
      error.value = err.message || '获取构建详情失败'
      console.error('获取构建详情失败:', err)
    } finally {
      buildsLoading.value = false
    }
  }

  const createBuild = async (data: CreateBuildRequest) => {
    try {
      buildsLoading.value = true
      error.value = null
      
      const response = await cicdApi.createBuild(data)
      const newBuild = response.data
      
      builds.value.unshift(newBuild)
      buildsPagination.value.total += 1
      
      return newBuild
    } catch (err: any) {
      error.value = err.message || '创建构建失败'
      console.error('创建构建失败:', err)
      throw err
    } finally {
      buildsLoading.value = false
    }
  }

  const cancelBuild = async (id: string) => {
    try {
      buildsLoading.value = true
      error.value = null
      
      await cicdApi.cancelBuild(id)
      
      // 更新构建状态
      const index = builds.value.findIndex(build => build.id === id)
      if (index !== -1) {
        builds.value[index].status = 'cancelled'
      }
      
      if (currentBuild.value?.id === id) {
        currentBuild.value.status = 'cancelled'
      }
    } catch (err: any) {
      error.value = err.message || '取消构建失败'
      console.error('取消构建失败:', err)
      throw err
    } finally {
      buildsLoading.value = false
    }
  }

  // 部署管理操作
  const fetchDeployments = async (params?: DeploymentQueryParams) => {
    try {
      deploymentsLoading.value = true
      error.value = null
      
      const response = await cicdApi.getDeployments(params)
      const data = response.data as PaginatedResponse<Deployment>
      
      deployments.value = data.items
      deploymentsPagination.value = {
        page: data.page,
        pageSize: data.pageSize,
        total: data.total
      }
    } catch (err: any) {
      error.value = err.message || '获取部署列表失败'
      console.error('获取部署列表失败:', err)
    } finally {
      deploymentsLoading.value = false
    }
  }

  const fetchDeployment = async (id: string) => {
    try {
      deploymentsLoading.value = true
      error.value = null
      
      const response = await cicdApi.getDeployment(id)
      currentDeployment.value = response.data
    } catch (err: any) {
      error.value = err.message || '获取部署详情失败'
      console.error('获取部署详情失败:', err)
    } finally {
      deploymentsLoading.value = false
    }
  }

  const createDeployment = async (data: CreateDeploymentRequest) => {
    try {
      deploymentsLoading.value = true
      error.value = null
      
      const response = await cicdApi.createDeployment(data)
      const newDeployment = response.data
      
      deployments.value.unshift(newDeployment)
      deploymentsPagination.value.total += 1
      
      return newDeployment
    } catch (err: any) {
      error.value = err.message || '创建部署失败'
      console.error('创建部署失败:', err)
      throw err
    } finally {
      deploymentsLoading.value = false
    }
  }

  const rollbackDeployment = async (id: string) => {
    try {
      deploymentsLoading.value = true
      error.value = null
      
      const response = await cicdApi.rollbackDeployment(id)
      return response.data
    } catch (err: any) {
      error.value = err.message || '回滚部署失败'
      console.error('回滚部署失败:', err)
      throw err
    } finally {
      deploymentsLoading.value = false
    }
  }

  // 清理状态
  const clearError = () => {
    error.value = null
  }

  const clearCurrentPipeline = () => {
    currentPipeline.value = null
  }

  const clearCurrentBuild = () => {
    currentBuild.value = null
  }

  const clearCurrentDeployment = () => {
    currentDeployment.value = null
  }

  return {
    // 状态
    pipelines,
    currentPipeline,
    pipelinesLoading,
    pipelinesPagination,
    builds,
    currentBuild,
    buildsLoading,
    buildsPagination,
    deployments,
    currentDeployment,
    deploymentsLoading,
    deploymentsPagination,
    error,

    // 计算属性
    activePipelines,
    runningBuilds,
    successfulBuilds,
    failedBuilds,
    runningDeployments,
    successfulDeployments,
    failedDeployments,
    buildsByPipeline,

    // 方法
    fetchPipelines,
    fetchPipeline,
    createPipeline,
    updatePipeline,
    deletePipeline,
    triggerPipeline,
    fetchBuilds,
    fetchBuild,
    createBuild,
    cancelBuild,
    fetchDeployments,
    fetchDeployment,
    createDeployment,
    rollbackDeployment,
    clearError,
    clearCurrentPipeline,
    clearCurrentBuild,
    clearCurrentDeployment
  }
})
