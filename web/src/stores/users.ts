import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { usersApi } from '@/api/users'
import type { 
  User, 
  Role, 
  Permission,
  Tenant,
  UserQueryParams,
  RoleQueryParams,
  TenantQueryParams,
  CreateUserRequest,
  UpdateUserRequest,
  CreateRoleRequest,
  UpdateRoleRequest,
  CreateTenantRequest,
  UpdateTenantRequest,
  UserSession,
  TenantStats
} from '@/api/users'
import type { PaginatedResponse } from '@/types'

export const useUsersStore = defineStore('users', () => {
  // 用户相关状态
  const users = ref<User[]>([])
  const currentUser = ref<User | null>(null)
  const usersLoading = ref(false)
  const usersPagination = ref({
    page: 1,
    pageSize: 20,
    total: 0
  })

  // 角色相关状态
  const roles = ref<Role[]>([])
  const currentRole = ref<Role | null>(null)
  const rolesLoading = ref(false)
  const rolesPagination = ref({
    page: 1,
    pageSize: 20,
    total: 0
  })

  // 权限相关状态
  const permissions = ref<Permission[]>([])
  const permissionsLoading = ref(false)

  // 租户相关状态
  const tenants = ref<Tenant[]>([])
  const currentTenant = ref<Tenant | null>(null)
  const tenantsLoading = ref(false)
  const tenantsPagination = ref({
    page: 1,
    pageSize: 20,
    total: 0
  })

  // 会话相关状态
  const userSessions = ref<UserSession[]>([])
  const sessionsLoading = ref(false)

  // 租户统计
  const tenantStats = ref<TenantStats | null>(null)
  const statsLoading = ref(false)

  // 错误状态
  const error = ref<string | null>(null)

  // 计算属性
  const activeUsers = computed(() => 
    users.value.filter(user => user.status === 'active')
  )

  const inactiveUsers = computed(() => 
    users.value.filter(user => user.status === 'inactive')
  )

  const suspendedUsers = computed(() => 
    users.value.filter(user => user.status === 'suspended')
  )

  const totalUsers = computed(() => users.value.length)

  const usersByTenant = computed(() => {
    const tenantGroups: Record<string, User[]> = {}
    users.value.forEach(user => {
      if (!tenantGroups[user.tenantId]) {
        tenantGroups[user.tenantId] = []
      }
      tenantGroups[user.tenantId].push(user)
    })
    return tenantGroups
  })

  const activeRoles = computed(() => 
    roles.value.filter(role => role.status === 'active')
  )

  const activeTenants = computed(() => 
    tenants.value.filter(tenant => tenant.status === 'active')
  )

  // 用户管理操作
  const fetchUsers = async (params?: UserQueryParams) => {
    try {
      usersLoading.value = true
      error.value = null
      
      const response = await usersApi.getUsers(params)
      const data = response.data as PaginatedResponse<User>
      
      users.value = data.items
      usersPagination.value = {
        page: data.page,
        pageSize: data.pageSize,
        total: data.total
      }
    } catch (err: any) {
      error.value = err.message || '获取用户列表失败'
      console.error('获取用户列表失败:', err)
    } finally {
      usersLoading.value = false
    }
  }

  const fetchUser = async (id: string) => {
    try {
      usersLoading.value = true
      error.value = null
      
      const response = await usersApi.getUser(id)
      currentUser.value = response.data
    } catch (err: any) {
      error.value = err.message || '获取用户详情失败'
      console.error('获取用户详情失败:', err)
    } finally {
      usersLoading.value = false
    }
  }

  const createUser = async (data: CreateUserRequest) => {
    try {
      usersLoading.value = true
      error.value = null
      
      const response = await usersApi.createUser(data)
      const newUser = response.data
      
      users.value.unshift(newUser)
      usersPagination.value.total += 1
      
      return newUser
    } catch (err: any) {
      error.value = err.message || '创建用户失败'
      console.error('创建用户失败:', err)
      throw err
    } finally {
      usersLoading.value = false
    }
  }

  const updateUser = async (id: string, data: UpdateUserRequest) => {
    try {
      usersLoading.value = true
      error.value = null
      
      const response = await usersApi.updateUser(id, data)
      const updatedUser = response.data
      
      const index = users.value.findIndex(user => user.id === id)
      if (index !== -1) {
        users.value[index] = updatedUser
      }
      
      if (currentUser.value?.id === id) {
        currentUser.value = updatedUser
      }
      
      return updatedUser
    } catch (err: any) {
      error.value = err.message || '更新用户失败'
      console.error('更新用户失败:', err)
      throw err
    } finally {
      usersLoading.value = false
    }
  }

  const deleteUser = async (id: string) => {
    try {
      usersLoading.value = true
      error.value = null
      
      await usersApi.deleteUser(id)
      
      users.value = users.value.filter(user => user.id !== id)
      usersPagination.value.total -= 1
      
      if (currentUser.value?.id === id) {
        currentUser.value = null
      }
    } catch (err: any) {
      error.value = err.message || '删除用户失败'
      console.error('删除用户失败:', err)
      throw err
    } finally {
      usersLoading.value = false
    }
  }

  const assignUserRoles = async (userId: string, roleIds: string[]) => {
    try {
      usersLoading.value = true
      error.value = null
      
      await usersApi.assignUserRoles(userId, { roleIds })
      
      // 刷新用户信息
      if (currentUser.value?.id === userId) {
        await fetchUser(userId)
      }
    } catch (err: any) {
      error.value = err.message || '分配用户角色失败'
      console.error('分配用户角色失败:', err)
      throw err
    } finally {
      usersLoading.value = false
    }
  }

  // 角色管理操作
  const fetchRoles = async (params?: RoleQueryParams) => {
    try {
      rolesLoading.value = true
      error.value = null
      
      const response = await usersApi.getRoles(params)
      const data = response.data as PaginatedResponse<Role>
      
      roles.value = data.items
      rolesPagination.value = {
        page: data.page,
        pageSize: data.pageSize,
        total: data.total
      }
    } catch (err: any) {
      error.value = err.message || '获取角色列表失败'
      console.error('获取角色列表失败:', err)
    } finally {
      rolesLoading.value = false
    }
  }

  const fetchRole = async (id: string) => {
    try {
      rolesLoading.value = true
      error.value = null
      
      const response = await usersApi.getRole(id)
      currentRole.value = response.data
    } catch (err: any) {
      error.value = err.message || '获取角色详情失败'
      console.error('获取角色详情失败:', err)
    } finally {
      rolesLoading.value = false
    }
  }

  const createRole = async (data: CreateRoleRequest) => {
    try {
      rolesLoading.value = true
      error.value = null
      
      const response = await usersApi.createRole(data)
      const newRole = response.data
      
      roles.value.unshift(newRole)
      rolesPagination.value.total += 1
      
      return newRole
    } catch (err: any) {
      error.value = err.message || '创建角色失败'
      console.error('创建角色失败:', err)
      throw err
    } finally {
      rolesLoading.value = false
    }
  }

  const updateRole = async (id: string, data: UpdateRoleRequest) => {
    try {
      rolesLoading.value = true
      error.value = null
      
      const response = await usersApi.updateRole(id, data)
      const updatedRole = response.data
      
      const index = roles.value.findIndex(role => role.id === id)
      if (index !== -1) {
        roles.value[index] = updatedRole
      }
      
      if (currentRole.value?.id === id) {
        currentRole.value = updatedRole
      }
      
      return updatedRole
    } catch (err: any) {
      error.value = err.message || '更新角色失败'
      console.error('更新角色失败:', err)
      throw err
    } finally {
      rolesLoading.value = false
    }
  }

  const deleteRole = async (id: string) => {
    try {
      rolesLoading.value = true
      error.value = null
      
      await usersApi.deleteRole(id)
      
      roles.value = roles.value.filter(role => role.id !== id)
      rolesPagination.value.total -= 1
      
      if (currentRole.value?.id === id) {
        currentRole.value = null
      }
    } catch (err: any) {
      error.value = err.message || '删除角色失败'
      console.error('删除角色失败:', err)
      throw err
    } finally {
      rolesLoading.value = false
    }
  }

  // 权限管理操作
  const fetchPermissions = async () => {
    try {
      permissionsLoading.value = true
      error.value = null
      
      const response = await usersApi.getPermissions()
      permissions.value = response.data
    } catch (err: any) {
      error.value = err.message || '获取权限列表失败'
      console.error('获取权限列表失败:', err)
    } finally {
      permissionsLoading.value = false
    }
  }

  // 租户管理操作
  const fetchTenants = async (params?: TenantQueryParams) => {
    try {
      tenantsLoading.value = true
      error.value = null
      
      const response = await usersApi.getTenants(params)
      const data = response.data as PaginatedResponse<Tenant>
      
      tenants.value = data.items
      tenantsPagination.value = {
        page: data.page,
        pageSize: data.pageSize,
        total: data.total
      }
    } catch (err: any) {
      error.value = err.message || '获取租户列表失败'
      console.error('获取租户列表失败:', err)
    } finally {
      tenantsLoading.value = false
    }
  }

  const fetchTenant = async (id: string) => {
    try {
      tenantsLoading.value = true
      error.value = null
      
      const response = await usersApi.getTenant(id)
      currentTenant.value = response.data
    } catch (err: any) {
      error.value = err.message || '获取租户详情失败'
      console.error('获取租户详情失败:', err)
    } finally {
      tenantsLoading.value = false
    }
  }

  const fetchTenantStats = async (id: string) => {
    try {
      statsLoading.value = true
      error.value = null
      
      const response = await usersApi.getTenantStats(id)
      tenantStats.value = response.data
    } catch (err: any) {
      error.value = err.message || '获取租户统计失败'
      console.error('获取租户统计失败:', err)
    } finally {
      statsLoading.value = false
    }
  }

  // 会话管理
  const fetchUserSessions = async (userId: string) => {
    try {
      sessionsLoading.value = true
      error.value = null
      
      const response = await usersApi.getUserSessions(userId)
      userSessions.value = response.data
    } catch (err: any) {
      error.value = err.message || '获取用户会话失败'
      console.error('获取用户会话失败:', err)
    } finally {
      sessionsLoading.value = false
    }
  }

  // 清理状态
  const clearError = () => {
    error.value = null
  }

  const clearCurrentUser = () => {
    currentUser.value = null
  }

  const clearCurrentRole = () => {
    currentRole.value = null
  }

  const clearCurrentTenant = () => {
    currentTenant.value = null
  }

  return {
    // 状态
    users,
    currentUser,
    usersLoading,
    usersPagination,
    roles,
    currentRole,
    rolesLoading,
    rolesPagination,
    permissions,
    permissionsLoading,
    tenants,
    currentTenant,
    tenantsLoading,
    tenantsPagination,
    userSessions,
    sessionsLoading,
    tenantStats,
    statsLoading,
    error,

    // 计算属性
    activeUsers,
    inactiveUsers,
    suspendedUsers,
    totalUsers,
    usersByTenant,
    activeRoles,
    activeTenants,

    // 方法
    fetchUsers,
    fetchUser,
    createUser,
    updateUser,
    deleteUser,
    assignUserRoles,
    fetchRoles,
    fetchRole,
    createRole,
    updateRole,
    deleteRole,
    fetchPermissions,
    fetchTenants,
    fetchTenant,
    fetchTenantStats,
    fetchUserSessions,
    clearError,
    clearCurrentUser,
    clearCurrentRole,
    clearCurrentTenant
  }
})
