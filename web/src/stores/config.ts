import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { configApi } from '@/api/config'
import type { 
  Config,
  Secret,
  ConfigQueryParams,
  SecretQueryParams,
  CreateConfigRequest,
  UpdateConfigRequest,
  CreateSecretRequest,
  UpdateSecretRequest,
  ConfigVersion,
  BatchConfigRequest,
  BatchUpdateConfigRequest,
  ExportConfigsRequest,
  ImportConfigsRequest
} from '@/api/config'
import type { PaginatedResponse } from '@/types'

export const useConfigStore = defineStore('config', () => {
  // 配置相关状态
  const configs = ref<Config[]>([])
  const currentConfig = ref<Config | null>(null)
  const configsLoading = ref(false)
  const configsPagination = ref({
    page: 1,
    pageSize: 20,
    total: 0
  })

  // 配置版本
  const configVersions = ref<ConfigVersion[]>([])
  const versionsLoading = ref(false)

  // 密钥相关状态
  const secrets = ref<Secret[]>([])
  const currentSecret = ref<Secret | null>(null)
  const secretsLoading = ref(false)
  const secretsPagination = ref({
    page: 1,
    pageSize: 20,
    total: 0
  })

  // 批量操作状态
  const batchLoading = ref(false)

  // 错误状态
  const error = ref<string | null>(null)

  // 计算属性
  const configsByScope = computed(() => {
    const scopeGroups: Record<string, Config[]> = {}
    configs.value.forEach(config => {
      const scopeKey = `${config.scope}:${config.scopeId || 'global'}`
      if (!scopeGroups[scopeKey]) {
        scopeGroups[scopeKey] = []
      }
      scopeGroups[scopeKey].push(config)
    })
    return scopeGroups
  })

  const configsByEnvironment = computed(() => {
    const envGroups: Record<string, Config[]> = {}
    configs.value.forEach(config => {
      if (!envGroups[config.environment]) {
        envGroups[config.environment] = []
      }
      envGroups[config.environment].push(config)
    })
    return envGroups
  })

  const configsByType = computed(() => {
    const typeGroups: Record<string, Config[]> = {}
    configs.value.forEach(config => {
      if (!typeGroups[config.type]) {
        typeGroups[config.type] = []
      }
      typeGroups[config.type].push(config)
    })
    return typeGroups
  })

  const activeSecrets = computed(() => 
    secrets.value.filter(secret => secret.status === 'active')
  )

  const expiredSecrets = computed(() => 
    secrets.value.filter(secret => secret.status === 'expired')
  )

  const secretsByScope = computed(() => {
    const scopeGroups: Record<string, Secret[]> = {}
    secrets.value.forEach(secret => {
      const scopeKey = `${secret.scope}:${secret.scopeId || 'global'}`
      if (!scopeGroups[scopeKey]) {
        scopeGroups[scopeKey] = []
      }
      scopeGroups[scopeKey].push(secret)
    })
    return scopeGroups
  })

  // 配置管理操作
  const fetchConfigs = async (params?: ConfigQueryParams) => {
    try {
      configsLoading.value = true
      error.value = null
      
      const response = await configApi.getConfigs(params)
      const data = response.data as PaginatedResponse<Config>
      
      configs.value = data.items
      configsPagination.value = {
        page: data.page,
        pageSize: data.pageSize,
        total: data.total
      }
    } catch (err: any) {
      error.value = err.message || '获取配置列表失败'
      console.error('获取配置列表失败:', err)
    } finally {
      configsLoading.value = false
    }
  }

  const fetchConfig = async (id: string) => {
    try {
      configsLoading.value = true
      error.value = null
      
      const response = await configApi.getConfig(id)
      currentConfig.value = response.data
    } catch (err: any) {
      error.value = err.message || '获取配置详情失败'
      console.error('获取配置详情失败:', err)
    } finally {
      configsLoading.value = false
    }
  }

  const createConfig = async (data: CreateConfigRequest) => {
    try {
      configsLoading.value = true
      error.value = null
      
      const response = await configApi.createConfig(data)
      const newConfig = response.data
      
      configs.value.unshift(newConfig)
      configsPagination.value.total += 1
      
      return newConfig
    } catch (err: any) {
      error.value = err.message || '创建配置失败'
      console.error('创建配置失败:', err)
      throw err
    } finally {
      configsLoading.value = false
    }
  }

  const updateConfig = async (id: string, data: UpdateConfigRequest) => {
    try {
      configsLoading.value = true
      error.value = null
      
      const response = await configApi.updateConfig(id, data)
      const updatedConfig = response.data
      
      const index = configs.value.findIndex(config => config.id === id)
      if (index !== -1) {
        configs.value[index] = updatedConfig
      }
      
      if (currentConfig.value?.id === id) {
        currentConfig.value = updatedConfig
      }
      
      return updatedConfig
    } catch (err: any) {
      error.value = err.message || '更新配置失败'
      console.error('更新配置失败:', err)
      throw err
    } finally {
      configsLoading.value = false
    }
  }

  const deleteConfig = async (id: string) => {
    try {
      configsLoading.value = true
      error.value = null
      
      await configApi.deleteConfig(id)
      
      configs.value = configs.value.filter(config => config.id !== id)
      configsPagination.value.total -= 1
      
      if (currentConfig.value?.id === id) {
        currentConfig.value = null
      }
    } catch (err: any) {
      error.value = err.message || '删除配置失败'
      console.error('删除配置失败:', err)
      throw err
    } finally {
      configsLoading.value = false
    }
  }

  const searchConfigs = async (query: string, scope?: string, scopeId?: string, environment?: string) => {
    try {
      configsLoading.value = true
      error.value = null
      
      const response = await configApi.searchConfigs({ query, scope, scopeId, environment })
      return response.data
    } catch (err: any) {
      error.value = err.message || '搜索配置失败'
      console.error('搜索配置失败:', err)
      throw err
    } finally {
      configsLoading.value = false
    }
  }

  const batchGetConfigs = async (data: BatchConfigRequest) => {
    try {
      batchLoading.value = true
      error.value = null
      
      const response = await configApi.batchGetConfigs(data)
      return response.data
    } catch (err: any) {
      error.value = err.message || '批量获取配置失败'
      console.error('批量获取配置失败:', err)
      throw err
    } finally {
      batchLoading.value = false
    }
  }

  const batchUpdateConfigs = async (data: BatchUpdateConfigRequest) => {
    try {
      batchLoading.value = true
      error.value = null
      
      const response = await configApi.batchUpdateConfigs(data)
      const updatedConfigs = response.data
      
      // 更新本地状态
      updatedConfigs.forEach(updatedConfig => {
        const index = configs.value.findIndex(config => config.id === updatedConfig.id)
        if (index !== -1) {
          configs.value[index] = updatedConfig
        }
      })
      
      return updatedConfigs
    } catch (err: any) {
      error.value = err.message || '批量更新配置失败'
      console.error('批量更新配置失败:', err)
      throw err
    } finally {
      batchLoading.value = false
    }
  }

  // 配置版本管理
  const fetchConfigVersions = async (configId: string) => {
    try {
      versionsLoading.value = true
      error.value = null
      
      const response = await configApi.getConfigVersions(configId)
      configVersions.value = response.data
    } catch (err: any) {
      error.value = err.message || '获取配置版本失败'
      console.error('获取配置版本失败:', err)
    } finally {
      versionsLoading.value = false
    }
  }

  const rollbackConfig = async (configId: string, version: number, reason: string) => {
    try {
      configsLoading.value = true
      error.value = null
      
      const response = await configApi.rollbackConfig(configId, { version, reason })
      const rolledBackConfig = response.data
      
      const index = configs.value.findIndex(config => config.id === configId)
      if (index !== -1) {
        configs.value[index] = rolledBackConfig
      }
      
      if (currentConfig.value?.id === configId) {
        currentConfig.value = rolledBackConfig
      }
      
      return rolledBackConfig
    } catch (err: any) {
      error.value = err.message || '回滚配置失败'
      console.error('回滚配置失败:', err)
      throw err
    } finally {
      configsLoading.value = false
    }
  }

  // 配置导入导出
  const exportConfigs = async (data: ExportConfigsRequest) => {
    try {
      batchLoading.value = true
      error.value = null
      
      const response = await configApi.exportConfigs(data)
      return response.data
    } catch (err: any) {
      error.value = err.message || '导出配置失败'
      console.error('导出配置失败:', err)
      throw err
    } finally {
      batchLoading.value = false
    }
  }

  const importConfigs = async (data: ImportConfigsRequest) => {
    try {
      batchLoading.value = true
      error.value = null
      
      const response = await configApi.importConfigs(data)
      
      // 刷新配置列表
      await fetchConfigs()
      
      return response.data
    } catch (err: any) {
      error.value = err.message || '导入配置失败'
      console.error('导入配置失败:', err)
      throw err
    } finally {
      batchLoading.value = false
    }
  }

  // 密钥管理操作
  const fetchSecrets = async (params?: SecretQueryParams) => {
    try {
      secretsLoading.value = true
      error.value = null
      
      const response = await configApi.getSecrets(params)
      const data = response.data as PaginatedResponse<Secret>
      
      secrets.value = data.items
      secretsPagination.value = {
        page: data.page,
        pageSize: data.pageSize,
        total: data.total
      }
    } catch (err: any) {
      error.value = err.message || '获取密钥列表失败'
      console.error('获取密钥列表失败:', err)
    } finally {
      secretsLoading.value = false
    }
  }

  const fetchSecret = async (id: string) => {
    try {
      secretsLoading.value = true
      error.value = null
      
      const response = await configApi.getSecret(id)
      currentSecret.value = response.data
    } catch (err: any) {
      error.value = err.message || '获取密钥详情失败'
      console.error('获取密钥详情失败:', err)
    } finally {
      secretsLoading.value = false
    }
  }

  const createSecret = async (data: CreateSecretRequest) => {
    try {
      secretsLoading.value = true
      error.value = null
      
      const response = await configApi.createSecret(data)
      const newSecret = response.data
      
      secrets.value.unshift(newSecret)
      secretsPagination.value.total += 1
      
      return newSecret
    } catch (err: any) {
      error.value = err.message || '创建密钥失败'
      console.error('创建密钥失败:', err)
      throw err
    } finally {
      secretsLoading.value = false
    }
  }

  const updateSecret = async (id: string, data: UpdateSecretRequest) => {
    try {
      secretsLoading.value = true
      error.value = null
      
      const response = await configApi.updateSecret(id, data)
      const updatedSecret = response.data
      
      const index = secrets.value.findIndex(secret => secret.id === id)
      if (index !== -1) {
        secrets.value[index] = updatedSecret
      }
      
      if (currentSecret.value?.id === id) {
        currentSecret.value = updatedSecret
      }
      
      return updatedSecret
    } catch (err: any) {
      error.value = err.message || '更新密钥失败'
      console.error('更新密钥失败:', err)
      throw err
    } finally {
      secretsLoading.value = false
    }
  }

  const deleteSecret = async (id: string) => {
    try {
      secretsLoading.value = true
      error.value = null
      
      await configApi.deleteSecret(id)
      
      secrets.value = secrets.value.filter(secret => secret.id !== id)
      secretsPagination.value.total -= 1
      
      if (currentSecret.value?.id === id) {
        currentSecret.value = null
      }
    } catch (err: any) {
      error.value = err.message || '删除密钥失败'
      console.error('删除密钥失败:', err)
      throw err
    } finally {
      secretsLoading.value = false
    }
  }

  const rotateSecret = async (id: string) => {
    try {
      secretsLoading.value = true
      error.value = null
      
      const response = await configApi.rotateSecret(id)
      const rotatedSecret = response.data
      
      const index = secrets.value.findIndex(secret => secret.id === id)
      if (index !== -1) {
        secrets.value[index] = rotatedSecret
      }
      
      if (currentSecret.value?.id === id) {
        currentSecret.value = rotatedSecret
      }
      
      return rotatedSecret
    } catch (err: any) {
      error.value = err.message || '轮换密钥失败'
      console.error('轮换密钥失败:', err)
      throw err
    } finally {
      secretsLoading.value = false
    }
  }

  // 清理状态
  const clearError = () => {
    error.value = null
  }

  const clearCurrentConfig = () => {
    currentConfig.value = null
  }

  const clearCurrentSecret = () => {
    currentSecret.value = null
  }

  const clearConfigs = () => {
    configs.value = []
    configsPagination.value = {
      page: 1,
      pageSize: 20,
      total: 0
    }
  }

  const clearSecrets = () => {
    secrets.value = []
    secretsPagination.value = {
      page: 1,
      pageSize: 20,
      total: 0
    }
  }

  return {
    // 状态
    configs,
    currentConfig,
    configsLoading,
    configsPagination,
    configVersions,
    versionsLoading,
    secrets,
    currentSecret,
    secretsLoading,
    secretsPagination,
    batchLoading,
    error,

    // 计算属性
    configsByScope,
    configsByEnvironment,
    configsByType,
    activeSecrets,
    expiredSecrets,
    secretsByScope,

    // 方法
    fetchConfigs,
    fetchConfig,
    createConfig,
    updateConfig,
    deleteConfig,
    searchConfigs,
    batchGetConfigs,
    batchUpdateConfigs,
    fetchConfigVersions,
    rollbackConfig,
    exportConfigs,
    importConfigs,
    fetchSecrets,
    fetchSecret,
    createSecret,
    updateSecret,
    deleteSecret,
    rotateSecret,
    clearError,
    clearCurrentConfig,
    clearCurrentSecret,
    clearConfigs,
    clearSecrets
  }
})
