import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { monitorApi } from '@/api/monitor'
import type { 
  SystemMetrics,
  HistoricalMetrics,
  LogEntry,
  Alert,
  AlertRule,
  PerformanceMetrics,
  HealthCheckResult,
  MetricsQueryParams,
  LogQueryParams,
  AlertQueryParams,
  CreateAlertRuleRequest
} from '@/api/monitor'
import type { PaginatedResponse } from '@/types'

export const useMonitorStore = defineStore('monitor', () => {
  // 系统指标相关状态
  const currentMetrics = ref<SystemMetrics | null>(null)
  const historicalMetrics = ref<HistoricalMetrics | null>(null)
  const performanceMetrics = ref<PerformanceMetrics | null>(null)
  const metricsLoading = ref(false)

  // 日志相关状态
  const logs = ref<LogEntry[]>([])
  const logsLoading = ref(false)
  const logsPagination = ref({
    page: 1,
    pageSize: 50,
    total: 0
  })

  // 告警相关状态
  const alerts = ref<Alert[]>([])
  const alertRules = ref<AlertRule[]>([])
  const currentAlert = ref<Alert | null>(null)
  const currentAlertRule = ref<AlertRule | null>(null)
  const alertsLoading = ref(false)
  const alertRulesLoading = ref(false)
  const alertsPagination = ref({
    page: 1,
    pageSize: 20,
    total: 0
  })

  // 健康检查状态
  const healthStatus = ref<HealthCheckResult | null>(null)
  const healthLoading = ref(false)

  // 错误状态
  const error = ref<string | null>(null)

  // 实时更新控制
  const autoRefresh = ref(false)
  const refreshInterval = ref<number | null>(null)

  // 计算属性
  const firingAlerts = computed(() => 
    alerts.value.filter(alert => alert.status === 'firing')
  )

  const resolvedAlerts = computed(() => 
    alerts.value.filter(alert => alert.status === 'resolved')
  )

  const criticalAlerts = computed(() => 
    alerts.value.filter(alert => alert.severity === 'critical')
  )

  const highAlerts = computed(() => 
    alerts.value.filter(alert => alert.severity === 'high')
  )

  const enabledAlertRules = computed(() => 
    alertRules.value.filter(rule => rule.enabled)
  )

  const disabledAlertRules = computed(() => 
    alertRules.value.filter(rule => !rule.enabled)
  )

  const logsByLevel = computed(() => {
    const levelGroups: Record<string, LogEntry[]> = {}
    logs.value.forEach(log => {
      if (!levelGroups[log.level]) {
        levelGroups[log.level] = []
      }
      levelGroups[log.level].push(log)
    })
    return levelGroups
  })

  const errorLogs = computed(() => 
    logs.value.filter(log => log.level === 'error' || log.level === 'fatal')
  )

  const systemHealth = computed(() => {
    if (!healthStatus.value) return 'unknown'
    return healthStatus.value.status
  })

  const systemUptime = computed(() => {
    if (!healthStatus.value) return 0
    return healthStatus.value.uptime
  })

  // 指标监控操作
  const fetchCurrentMetrics = async () => {
    try {
      metricsLoading.value = true
      error.value = null
      
      const response = await monitorApi.getCurrentMetrics()
      currentMetrics.value = response.data
    } catch (err: any) {
      error.value = err.message || '获取当前指标失败'
      console.error('获取当前指标失败:', err)
    } finally {
      metricsLoading.value = false
    }
  }

  const fetchHistoricalMetrics = async (params: MetricsQueryParams) => {
    try {
      metricsLoading.value = true
      error.value = null
      
      const response = await monitorApi.getHistoricalMetrics(params)
      historicalMetrics.value = response.data
    } catch (err: any) {
      error.value = err.message || '获取历史指标失败'
      console.error('获取历史指标失败:', err)
    } finally {
      metricsLoading.value = false
    }
  }

  const fetchPerformanceMetrics = async (params?: any) => {
    try {
      metricsLoading.value = true
      error.value = null
      
      const response = await monitorApi.getPerformanceMetrics(params)
      performanceMetrics.value = response.data
    } catch (err: any) {
      error.value = err.message || '获取性能指标失败'
      console.error('获取性能指标失败:', err)
    } finally {
      metricsLoading.value = false
    }
  }

  const fetchMetricsSummary = async () => {
    try {
      metricsLoading.value = true
      error.value = null
      
      const response = await monitorApi.getMetricsSummary()
      return response.data
    } catch (err: any) {
      error.value = err.message || '获取指标摘要失败'
      console.error('获取指标摘要失败:', err)
      throw err
    } finally {
      metricsLoading.value = false
    }
  }

  // 日志管理操作
  const fetchLogs = async (params?: LogQueryParams) => {
    try {
      logsLoading.value = true
      error.value = null
      
      const response = await monitorApi.getLogs(params)
      const data = response.data as PaginatedResponse<LogEntry>
      
      logs.value = data.items
      logsPagination.value = {
        page: data.page,
        pageSize: data.pageSize,
        total: data.total
      }
    } catch (err: any) {
      error.value = err.message || '获取日志列表失败'
      console.error('获取日志列表失败:', err)
    } finally {
      logsLoading.value = false
    }
  }

  const searchLogs = async (query: string, params?: any) => {
    try {
      logsLoading.value = true
      error.value = null
      
      const response = await monitorApi.searchLogs({ query, ...params })
      return response.data
    } catch (err: any) {
      error.value = err.message || '搜索日志失败'
      console.error('搜索日志失败:', err)
      throw err
    } finally {
      logsLoading.value = false
    }
  }

  const fetchLogStats = async (params?: any) => {
    try {
      logsLoading.value = true
      error.value = null
      
      const response = await monitorApi.getLogStats(params)
      return response.data
    } catch (err: any) {
      error.value = err.message || '获取日志统计失败'
      console.error('获取日志统计失败:', err)
      throw err
    } finally {
      logsLoading.value = false
    }
  }

  // 告警管理操作
  const fetchAlerts = async (params?: AlertQueryParams) => {
    try {
      alertsLoading.value = true
      error.value = null
      
      const response = await monitorApi.getAlerts(params)
      const data = response.data as PaginatedResponse<Alert>
      
      alerts.value = data.items
      alertsPagination.value = {
        page: data.page,
        pageSize: data.pageSize,
        total: data.total
      }
    } catch (err: any) {
      error.value = err.message || '获取告警列表失败'
      console.error('获取告警列表失败:', err)
    } finally {
      alertsLoading.value = false
    }
  }

  const fetchAlertRules = async () => {
    try {
      alertRulesLoading.value = true
      error.value = null
      
      const response = await monitorApi.getAlertRules()
      alertRules.value = response.data
    } catch (err: any) {
      error.value = err.message || '获取告警规则失败'
      console.error('获取告警规则失败:', err)
    } finally {
      alertRulesLoading.value = false
    }
  }

  const createAlertRule = async (data: CreateAlertRuleRequest) => {
    try {
      alertRulesLoading.value = true
      error.value = null
      
      const response = await monitorApi.createAlertRule(data)
      const newRule = response.data
      
      alertRules.value.unshift(newRule)
      
      return newRule
    } catch (err: any) {
      error.value = err.message || '创建告警规则失败'
      console.error('创建告警规则失败:', err)
      throw err
    } finally {
      alertRulesLoading.value = false
    }
  }

  const updateAlertRule = async (id: string, data: Partial<CreateAlertRuleRequest>) => {
    try {
      alertRulesLoading.value = true
      error.value = null
      
      const response = await monitorApi.updateAlertRule(id, data)
      const updatedRule = response.data
      
      const index = alertRules.value.findIndex(rule => rule.id === id)
      if (index !== -1) {
        alertRules.value[index] = updatedRule
      }
      
      if (currentAlertRule.value?.id === id) {
        currentAlertRule.value = updatedRule
      }
      
      return updatedRule
    } catch (err: any) {
      error.value = err.message || '更新告警规则失败'
      console.error('更新告警规则失败:', err)
      throw err
    } finally {
      alertRulesLoading.value = false
    }
  }

  const deleteAlertRule = async (id: string) => {
    try {
      alertRulesLoading.value = true
      error.value = null
      
      await monitorApi.deleteAlertRule(id)
      
      alertRules.value = alertRules.value.filter(rule => rule.id !== id)
      
      if (currentAlertRule.value?.id === id) {
        currentAlertRule.value = null
      }
    } catch (err: any) {
      error.value = err.message || '删除告警规则失败'
      console.error('删除告警规则失败:', err)
      throw err
    } finally {
      alertRulesLoading.value = false
    }
  }

  const toggleAlertRule = async (id: string, enabled: boolean) => {
    try {
      alertRulesLoading.value = true
      error.value = null
      
      const response = await monitorApi.toggleAlertRule(id, enabled)
      const updatedRule = response.data
      
      const index = alertRules.value.findIndex(rule => rule.id === id)
      if (index !== -1) {
        alertRules.value[index] = updatedRule
      }
      
      return updatedRule
    } catch (err: any) {
      error.value = err.message || '切换告警规则状态失败'
      console.error('切换告警规则状态失败:', err)
      throw err
    } finally {
      alertRulesLoading.value = false
    }
  }

  const acknowledgeAlert = async (id: string) => {
    try {
      alertsLoading.value = true
      error.value = null
      
      await monitorApi.acknowledgeAlert(id)
      
      // 更新本地状态
      const index = alerts.value.findIndex(alert => alert.id === id)
      if (index !== -1) {
        // 这里可以添加确认状态字段
      }
    } catch (err: any) {
      error.value = err.message || '确认告警失败'
      console.error('确认告警失败:', err)
      throw err
    } finally {
      alertsLoading.value = false
    }
  }

  const resolveAlert = async (id: string) => {
    try {
      alertsLoading.value = true
      error.value = null
      
      await monitorApi.resolveAlert(id)
      
      // 更新本地状态
      const index = alerts.value.findIndex(alert => alert.id === id)
      if (index !== -1) {
        alerts.value[index].status = 'resolved'
        alerts.value[index].resolved_at = new Date().toISOString()
      }
    } catch (err: any) {
      error.value = err.message || '解决告警失败'
      console.error('解决告警失败:', err)
      throw err
    } finally {
      alertsLoading.value = false
    }
  }

  // 健康检查操作
  const fetchHealthStatus = async () => {
    try {
      healthLoading.value = true
      error.value = null
      
      const response = await monitorApi.getHealthStatus()
      healthStatus.value = response.data
    } catch (err: any) {
      error.value = err.message || '获取健康状态失败'
      console.error('获取健康状态失败:', err)
    } finally {
      healthLoading.value = false
    }
  }

  const fetchServiceStatus = async () => {
    try {
      healthLoading.value = true
      error.value = null
      
      const response = await monitorApi.getServiceStatus()
      return response.data
    } catch (err: any) {
      error.value = err.message || '获取服务状态失败'
      console.error('获取服务状态失败:', err)
      throw err
    } finally {
      healthLoading.value = false
    }
  }

  // 实时更新控制
  const startAutoRefresh = (interval: number = 30000) => {
    if (refreshInterval.value) {
      clearInterval(refreshInterval.value)
    }
    
    autoRefresh.value = true
    refreshInterval.value = setInterval(async () => {
      try {
        await Promise.all([
          fetchCurrentMetrics(),
          fetchHealthStatus(),
          fetchAlerts({ status: 'firing' })
        ])
      } catch (err) {
        console.error('自动刷新失败:', err)
      }
    }, interval)
  }

  const stopAutoRefresh = () => {
    if (refreshInterval.value) {
      clearInterval(refreshInterval.value)
      refreshInterval.value = null
    }
    autoRefresh.value = false
  }

  // 清理状态
  const clearError = () => {
    error.value = null
  }

  const clearCurrentAlert = () => {
    currentAlert.value = null
  }

  const clearCurrentAlertRule = () => {
    currentAlertRule.value = null
  }

  const clearLogs = () => {
    logs.value = []
    logsPagination.value = {
      page: 1,
      pageSize: 50,
      total: 0
    }
  }

  const clearAlerts = () => {
    alerts.value = []
    alertsPagination.value = {
      page: 1,
      pageSize: 20,
      total: 0
    }
  }

  return {
    // 状态
    currentMetrics,
    historicalMetrics,
    performanceMetrics,
    metricsLoading,
    logs,
    logsLoading,
    logsPagination,
    alerts,
    alertRules,
    currentAlert,
    currentAlertRule,
    alertsLoading,
    alertRulesLoading,
    alertsPagination,
    healthStatus,
    healthLoading,
    error,
    autoRefresh,

    // 计算属性
    firingAlerts,
    resolvedAlerts,
    criticalAlerts,
    highAlerts,
    enabledAlertRules,
    disabledAlertRules,
    logsByLevel,
    errorLogs,
    systemHealth,
    systemUptime,

    // 方法
    fetchCurrentMetrics,
    fetchHistoricalMetrics,
    fetchPerformanceMetrics,
    fetchMetricsSummary,
    fetchLogs,
    searchLogs,
    fetchLogStats,
    fetchAlerts,
    fetchAlertRules,
    createAlertRule,
    updateAlertRule,
    deleteAlertRule,
    toggleAlertRule,
    acknowledgeAlert,
    resolveAlert,
    fetchHealthStatus,
    fetchServiceStatus,
    startAutoRefresh,
    stopAutoRefresh,
    clearError,
    clearCurrentAlert,
    clearCurrentAlertRule,
    clearLogs,
    clearAlerts
  }
})
