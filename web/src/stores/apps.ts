import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { appsApi } from '@/api/apps'
import type { 
  App, 
  AppQueryParams,
  CreateAppRequest,
  UpdateAppRequest,
  AppDeployment,
  AppMetrics,
  AppLog
} from '@/api/apps'
import type { PaginatedResponse } from '@/types'

export const useAppsStore = defineStore('apps', () => {
  // 应用列表相关状态
  const apps = ref<App[]>([])
  const currentApp = ref<App | null>(null)
  const appsLoading = ref(false)
  const appsPagination = ref({
    page: 1,
    pageSize: 20,
    total: 0
  })

  // 部署相关状态
  const deployments = ref<AppDeployment[]>([])
  const currentDeployment = ref<AppDeployment | null>(null)
  const deploymentsLoading = ref(false)
  const deploymentsPagination = ref({
    page: 1,
    pageSize: 20,
    total: 0
  })

  // 指标相关状态
  const appMetrics = ref<AppMetrics | null>(null)
  const metricsLoading = ref(false)

  // 日志相关状态
  const appLogs = ref<AppLog[]>([])
  const logsLoading = ref(false)
  const logsPagination = ref({
    page: 1,
    pageSize: 50,
    total: 0
  })

  // 错误状态
  const error = ref<string | null>(null)

  // 计算属性
  const runningApps = computed(() => 
    apps.value.filter(app => app.status === 'running')
  )

  const stoppedApps = computed(() => 
    apps.value.filter(app => app.status === 'stopped')
  )

  const failedApps = computed(() => 
    apps.value.filter(app => app.status === 'failed')
  )

  const totalApps = computed(() => apps.value.length)

  const appsByEnvironment = computed(() => {
    const envGroups: Record<string, App[]> = {}
    apps.value.forEach(app => {
      if (!envGroups[app.environment]) {
        envGroups[app.environment] = []
      }
      envGroups[app.environment].push(app)
    })
    return envGroups
  })

  // 应用管理操作
  const fetchApps = async (params?: AppQueryParams) => {
    try {
      appsLoading.value = true
      error.value = null
      
      const response = await appsApi.getApps(params)
      const data = response.data as PaginatedResponse<App>
      
      apps.value = data.items
      appsPagination.value = {
        page: data.page,
        pageSize: data.pageSize,
        total: data.total
      }
    } catch (err: any) {
      error.value = err.message || '获取应用列表失败'
      console.error('获取应用列表失败:', err)
    } finally {
      appsLoading.value = false
    }
  }

  const fetchApp = async (id: string) => {
    try {
      appsLoading.value = true
      error.value = null
      
      const response = await appsApi.getApp(id)
      currentApp.value = response.data
    } catch (err: any) {
      error.value = err.message || '获取应用详情失败'
      console.error('获取应用详情失败:', err)
    } finally {
      appsLoading.value = false
    }
  }

  const createApp = async (data: CreateAppRequest) => {
    try {
      appsLoading.value = true
      error.value = null
      
      const response = await appsApi.createApp(data)
      const newApp = response.data
      
      apps.value.unshift(newApp)
      appsPagination.value.total += 1
      
      return newApp
    } catch (err: any) {
      error.value = err.message || '创建应用失败'
      console.error('创建应用失败:', err)
      throw err
    } finally {
      appsLoading.value = false
    }
  }

  const updateApp = async (id: string, data: UpdateAppRequest) => {
    try {
      appsLoading.value = true
      error.value = null
      
      const response = await appsApi.updateApp(id, data)
      const updatedApp = response.data
      
      const index = apps.value.findIndex(app => app.id === id)
      if (index !== -1) {
        apps.value[index] = updatedApp
      }
      
      if (currentApp.value?.id === id) {
        currentApp.value = updatedApp
      }
      
      return updatedApp
    } catch (err: any) {
      error.value = err.message || '更新应用失败'
      console.error('更新应用失败:', err)
      throw err
    } finally {
      appsLoading.value = false
    }
  }

  const deleteApp = async (id: string) => {
    try {
      appsLoading.value = true
      error.value = null
      
      await appsApi.deleteApp(id)
      
      apps.value = apps.value.filter(app => app.id !== id)
      appsPagination.value.total -= 1
      
      if (currentApp.value?.id === id) {
        currentApp.value = null
      }
    } catch (err: any) {
      error.value = err.message || '删除应用失败'
      console.error('删除应用失败:', err)
      throw err
    } finally {
      appsLoading.value = false
    }
  }

  // 应用操作
  const startApp = async (id: string) => {
    try {
      appsLoading.value = true
      error.value = null
      
      await appsApi.startApp(id)
      
      // 更新应用状态
      const index = apps.value.findIndex(app => app.id === id)
      if (index !== -1) {
        apps.value[index].status = 'starting'
      }
      
      if (currentApp.value?.id === id) {
        currentApp.value.status = 'starting'
      }
    } catch (err: any) {
      error.value = err.message || '启动应用失败'
      console.error('启动应用失败:', err)
      throw err
    } finally {
      appsLoading.value = false
    }
  }

  const stopApp = async (id: string) => {
    try {
      appsLoading.value = true
      error.value = null
      
      await appsApi.stopApp(id)
      
      // 更新应用状态
      const index = apps.value.findIndex(app => app.id === id)
      if (index !== -1) {
        apps.value[index].status = 'stopping'
      }
      
      if (currentApp.value?.id === id) {
        currentApp.value.status = 'stopping'
      }
    } catch (err: any) {
      error.value = err.message || '停止应用失败'
      console.error('停止应用失败:', err)
      throw err
    } finally {
      appsLoading.value = false
    }
  }

  const restartApp = async (id: string) => {
    try {
      appsLoading.value = true
      error.value = null
      
      await appsApi.restartApp(id)
      
      // 更新应用状态
      const index = apps.value.findIndex(app => app.id === id)
      if (index !== -1) {
        apps.value[index].status = 'restarting'
      }
      
      if (currentApp.value?.id === id) {
        currentApp.value.status = 'restarting'
      }
    } catch (err: any) {
      error.value = err.message || '重启应用失败'
      console.error('重启应用失败:', err)
      throw err
    } finally {
      appsLoading.value = false
    }
  }

  // 部署管理
  const fetchDeployments = async (appId: string) => {
    try {
      deploymentsLoading.value = true
      error.value = null
      
      const response = await appsApi.getAppDeployments(appId)
      const data = response.data as PaginatedResponse<AppDeployment>
      
      deployments.value = data.items
      deploymentsPagination.value = {
        page: data.page,
        pageSize: data.pageSize,
        total: data.total
      }
    } catch (err: any) {
      error.value = err.message || '获取部署记录失败'
      console.error('获取部署记录失败:', err)
    } finally {
      deploymentsLoading.value = false
    }
  }

  // 指标监控
  const fetchAppMetrics = async (appId: string) => {
    try {
      metricsLoading.value = true
      error.value = null
      
      const response = await appsApi.getAppMetrics(appId)
      appMetrics.value = response.data
    } catch (err: any) {
      error.value = err.message || '获取应用指标失败'
      console.error('获取应用指标失败:', err)
    } finally {
      metricsLoading.value = false
    }
  }

  // 日志管理
  const fetchAppLogs = async (appId: string, params?: any) => {
    try {
      logsLoading.value = true
      error.value = null
      
      const response = await appsApi.getAppLogs(appId, params)
      const data = response.data as PaginatedResponse<AppLog>
      
      appLogs.value = data.items
      logsPagination.value = {
        page: data.page,
        pageSize: data.pageSize,
        total: data.total
      }
    } catch (err: any) {
      error.value = err.message || '获取应用日志失败'
      console.error('获取应用日志失败:', err)
    } finally {
      logsLoading.value = false
    }
  }

  // 清理状态
  const clearError = () => {
    error.value = null
  }

  const clearCurrentApp = () => {
    currentApp.value = null
  }

  const clearApps = () => {
    apps.value = []
    appsPagination.value = {
      page: 1,
      pageSize: 20,
      total: 0
    }
  }

  return {
    // 状态
    apps,
    currentApp,
    appsLoading,
    appsPagination,
    deployments,
    currentDeployment,
    deploymentsLoading,
    deploymentsPagination,
    appMetrics,
    metricsLoading,
    appLogs,
    logsLoading,
    logsPagination,
    error,

    // 计算属性
    runningApps,
    stoppedApps,
    failedApps,
    totalApps,
    appsByEnvironment,

    // 方法
    fetchApps,
    fetchApp,
    createApp,
    updateApp,
    deleteApp,
    startApp,
    stopApp,
    restartApp,
    fetchDeployments,
    fetchAppMetrics,
    fetchAppLogs,
    clearError,
    clearCurrentApp,
    clearApps
  }
})
