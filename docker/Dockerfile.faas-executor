# FaaS 执行器服务 Dockerfile
# 多阶段构建，优化镜像大小和安全性

# 构建阶段
FROM golang:1.21-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装必要的系统依赖
RUN apk add --no-cache \
    git \
    ca-certificates \
    tzdata \
    upx

# 设置 Go 环境变量
ENV GO111MODULE=on \
    CGO_ENABLED=0 \
    GOOS=linux \
    GOARCH=amd64

# 复制 go mod 文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download && go mod verify

# 复制源代码
COPY . .

# 构建参数
ARG VERSION=latest
ARG BUILD_TIME
ARG GIT_COMMIT

# 构建应用
RUN go build \
    -ldflags="-s -w -X main.version=${VERSION} -X main.buildTime=${BUILD_TIME} -X main.gitCommit=${GIT_COMMIT} -extldflags '-static'" \
    -a -installsuffix cgo \
    -o faas-service \
    ./cmd/faas-service

# 使用 UPX 压缩二进制文件（可选）
RUN upx --best --lzma faas-service || true

# 运行阶段
FROM alpine:3.18

# 安装运行时依赖
RUN apk --no-cache add \
    ca-certificates \
    tzdata \
    curl \
    jq \
    docker-cli \
    && rm -rf /var/cache/apk/*

# 设置时区
ENV TZ=Asia/Shanghai

# 创建非 root 用户
RUN addgroup -g 1001 -S faas && \
    adduser -u 1001 -S faas -G faas

# 创建必要的目录
RUN mkdir -p /app/configs /app/logs /app/data /app/data/faas/workspaces /app/data/faas/artifacts && \
    chown -R faas:faas /app

# 创建 Docker socket 访问权限
RUN addgroup faas docker || true

# 切换到非 root 用户
USER faas

# 设置工作目录
WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder --chown=faas:faas /app/faas-service .

# 复制配置文件
COPY --chown=faas:faas configs/faas-service.yaml ./configs/
COPY --chown=faas:faas configs/faas-service.dev.yaml ./configs/

# 设置环境变量
ENV GIN_MODE=release \
    LOG_LEVEL=info \
    FAAS_SERVICE_CONFIG_FILE=/app/configs/faas-service.yaml

# 暴露端口
EXPOSE 8087

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8087/health || exit 1

# 添加启动脚本
COPY --chown=faas:faas <<'EOF' /app/entrypoint.sh
#!/bin/sh

# 等待 Docker socket 可用
echo "等待 Docker socket 可用..."
while ! docker version >/dev/null 2>&1; do
    echo "Docker socket 不可用，等待中..."
    sleep 2
done
echo "Docker socket 已就绪"

# 检查配置文件
if [ ! -f "$FAAS_SERVICE_CONFIG_FILE" ]; then
    echo "配置文件不存在: $FAAS_SERVICE_CONFIG_FILE"
    echo "使用默认配置启动服务"
    exec ./faas-service
else
    echo "使用配置文件: $FAAS_SERVICE_CONFIG_FILE"
    exec ./faas-service --config="$FAAS_SERVICE_CONFIG_FILE"
fi
EOF

RUN chmod +x /app/entrypoint.sh

# 启动命令
ENTRYPOINT ["/app/entrypoint.sh"]

# 标签信息
LABEL maintainer="PaaS Team <<EMAIL>>" \
      version="${VERSION}" \
      description="FaaS executor service for PaaS platform" \
      org.opencontainers.image.title="FaaS Executor" \
      org.opencontainers.image.description="Function as a Service executor for PaaS platform" \
      org.opencontainers.image.version="${VERSION}" \
      org.opencontainers.image.created="${BUILD_TIME}" \
      org.opencontainers.image.revision="${GIT_COMMIT}" \
      org.opencontainers.image.source="https://github.com/paas/platform" \
      org.opencontainers.image.documentation="https://docs.paas.com/faas" \
      org.opencontainers.image.licenses="MIT" \
      paas.service="faas-executor" \
      paas.component="faas" \
      paas.version="${VERSION}"

# 添加构建信息
RUN echo "FaaS Executor v${VERSION:-latest}" > /app/VERSION && \
    echo "Built at: ${BUILD_TIME:-unknown}" >> /app/VERSION && \
    echo "Git commit: ${GIT_COMMIT:-unknown}" >> /app/VERSION
