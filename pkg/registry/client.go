package registry

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"paas-platform/pkg/logger"
)

// RegistryClientInterface 注册客户端接口
type RegistryClientInterface interface {
	RegisterService(ctx context.Context, registration *ServiceRegistration) error
	DeregisterService(ctx context.Context, serviceName, instanceID string) error
	UpdateServiceHealth(ctx context.Context, serviceName, instanceID string, healthy bool) error
	GetServiceInstances(ctx context.Context, serviceName string) ([]ServiceInstance, error)
}

// RegistryClient 服务注册客户端
type RegistryClient struct {
	baseURL    string
	httpClient *http.Client
	logger     logger.Logger
	retryCount int
	timeout    time.Duration
}

// ServiceRegistration 服务注册信息
type ServiceRegistration struct {
	ServiceName string            `json:"service_name"`
	InstanceID  string            `json:"instance_id"`
	Host        string            `json:"host"`
	Port        int               `json:"port"`
	Protocol    string            `json:"protocol"`
	Path        string            `json:"path"`
	Weight      int               `json:"weight"`
	Metadata    map[string]string `json:"metadata"`
	
	// 健康检查配置
	HealthCheckURL      string `json:"health_check_url"`
	HealthCheckInterval string `json:"health_check_interval"`
	HealthCheckTimeout  string `json:"health_check_timeout"`
	
	// 应用信息
	AppID       string `json:"app_id"`
	AppName     string `json:"app_name"`
	Version     string `json:"version"`
	Environment string `json:"environment"`
	TenantID    string `json:"tenant_id"`
}

// RegistryConfig 注册客户端配置
type RegistryConfig struct {
	BaseURL     string        `json:"base_url"`
	Timeout     time.Duration `json:"timeout"`
	RetryCount  int           `json:"retry_count"`
	RetryDelay  time.Duration `json:"retry_delay"`
	EnableRetry bool          `json:"enable_retry"`
}

// NewRegistryClient 创建服务注册客户端
func NewRegistryClient(config RegistryConfig, logger logger.Logger) *RegistryClient {
	if config.Timeout == 0 {
		config.Timeout = 30 * time.Second
	}
	if config.RetryCount == 0 {
		config.RetryCount = 3
	}

	return &RegistryClient{
		baseURL: config.BaseURL,
		httpClient: &http.Client{
			Timeout: config.Timeout,
		},
		logger:     logger,
		retryCount: config.RetryCount,
		timeout:    config.Timeout,
	}
}

// RegisterService 注册服务
func (rc *RegistryClient) RegisterService(ctx context.Context, registration *ServiceRegistration) error {
	rc.logger.Info("开始注册服务",
		"service_name", registration.ServiceName,
		"instance_id", registration.InstanceID,
		"endpoint", fmt.Sprintf("%s:%d", registration.Host, registration.Port))

	// 构建请求
	reqBody, err := json.Marshal(registration)
	if err != nil {
		return fmt.Errorf("序列化注册请求失败: %w", err)
	}

	url := fmt.Sprintf("%s/api/v1/loadbalancer/services/register", rc.baseURL)
	
	// 执行注册请求（带重试）
	err = rc.executeWithRetry(ctx, "POST", url, reqBody)
	if err != nil {
		rc.logger.Error("服务注册失败",
			"service_name", registration.ServiceName,
			"instance_id", registration.InstanceID,
			"error", err)
		return fmt.Errorf("服务注册失败: %w", err)
	}

	rc.logger.Info("服务注册成功",
		"service_name", registration.ServiceName,
		"instance_id", registration.InstanceID)
	
	return nil
}

// DeregisterService 注销服务
func (rc *RegistryClient) DeregisterService(ctx context.Context, serviceName, instanceID string) error {
	rc.logger.Info("开始注销服务",
		"service_name", serviceName,
		"instance_id", instanceID)

	url := fmt.Sprintf("%s/api/v1/loadbalancer/services/%s/instances/%s", 
		rc.baseURL, serviceName, instanceID)
	
	// 执行注销请求（带重试）
	err := rc.executeWithRetry(ctx, "DELETE", url, nil)
	if err != nil {
		rc.logger.Error("服务注销失败",
			"service_name", serviceName,
			"instance_id", instanceID,
			"error", err)
		return fmt.Errorf("服务注销失败: %w", err)
	}

	rc.logger.Info("服务注销成功",
		"service_name", serviceName,
		"instance_id", instanceID)
	
	return nil
}

// UpdateServiceHealth 更新服务健康状态
func (rc *RegistryClient) UpdateServiceHealth(ctx context.Context, serviceName, instanceID string, healthy bool) error {
	status := "healthy"
	if !healthy {
		status = "unhealthy"
	}

	reqBody := map[string]interface{}{
		"status": status,
	}

	bodyBytes, err := json.Marshal(reqBody)
	if err != nil {
		return fmt.Errorf("序列化健康状态请求失败: %w", err)
	}

	url := fmt.Sprintf("%s/api/v1/loadbalancer/services/%s/instances/%s/health", 
		rc.baseURL, serviceName, instanceID)
	
	err = rc.executeWithRetry(ctx, "PUT", url, bodyBytes)
	if err != nil {
		rc.logger.Error("更新服务健康状态失败",
			"service_name", serviceName,
			"instance_id", instanceID,
			"status", status,
			"error", err)
		return fmt.Errorf("更新服务健康状态失败: %w", err)
	}

	rc.logger.Debug("服务健康状态更新成功",
		"service_name", serviceName,
		"instance_id", instanceID,
		"status", status)
	
	return nil
}

// GetServiceInstances 获取服务实例列表
func (rc *RegistryClient) GetServiceInstances(ctx context.Context, serviceName string) ([]ServiceInstance, error) {
	url := fmt.Sprintf("%s/api/v1/loadbalancer/services/%s/instances", rc.baseURL, serviceName)
	
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	resp, err := rc.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("执行请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("获取服务实例失败，状态码: %d", resp.StatusCode)
	}

	var response struct {
		Data []ServiceInstance `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return response.Data, nil
}

// executeWithRetry 执行带重试的 HTTP 请求
func (rc *RegistryClient) executeWithRetry(ctx context.Context, method, url string, body []byte) error {
	var lastErr error
	
	for attempt := 0; attempt <= rc.retryCount; attempt++ {
		if attempt > 0 {
			// 等待重试间隔
			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(time.Duration(attempt) * time.Second):
			}
			
			rc.logger.Warn("重试服务注册请求",
				"attempt", attempt,
				"max_attempts", rc.retryCount,
				"url", url)
		}

		err := rc.executeRequest(ctx, method, url, body)
		if err == nil {
			return nil
		}
		
		lastErr = err
		
		// 检查是否应该重试
		if !rc.shouldRetry(err) {
			break
		}
	}
	
	return fmt.Errorf("请求失败，已重试 %d 次: %w", rc.retryCount, lastErr)
}

// executeRequest 执行单次 HTTP 请求
func (rc *RegistryClient) executeRequest(ctx context.Context, method, url string, body []byte) error {
	var reqBody *bytes.Buffer
	if body != nil {
		reqBody = bytes.NewBuffer(body)
	}

	req, err := http.NewRequestWithContext(ctx, method, url, reqBody)
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}

	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	resp, err := rc.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("执行请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return fmt.Errorf("请求失败，状态码: %d", resp.StatusCode)
	}

	return nil
}

// shouldRetry 判断是否应该重试
func (rc *RegistryClient) shouldRetry(err error) bool {
	// 网络错误或服务器错误可以重试
	// 客户端错误（4xx）通常不应该重试
	return true // 简化实现，实际应该根据错误类型判断
}

// ServiceInstance 服务实例信息
type ServiceInstance struct {
	ID       string            `json:"id"`
	Host     string            `json:"host"`
	Port     int               `json:"port"`
	Protocol string            `json:"protocol"`
	Path     string            `json:"path"`
	Weight   int               `json:"weight"`
	Status   string            `json:"status"`
	Metadata map[string]string `json:"metadata"`
}

// DefaultRegistryConfig 默认注册客户端配置
func DefaultRegistryConfig() RegistryConfig {
	return RegistryConfig{
		BaseURL:     "http://localhost:8080",
		Timeout:     30 * time.Second,
		RetryCount:  3,
		RetryDelay:  2 * time.Second,
		EnableRetry: true,
	}
}

// RegistryClientFromConfig 从配置创建注册客户端
func RegistryClientFromConfig(baseURL string, logger logger.Logger) *RegistryClient {
	config := DefaultRegistryConfig()
	config.BaseURL = baseURL
	return NewRegistryClient(config, logger)
}
