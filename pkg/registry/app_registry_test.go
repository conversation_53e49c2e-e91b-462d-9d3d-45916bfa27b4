package registry

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"paas-platform/pkg/logger"
)

func TestAppRegistryManager_RegisterApp(t *testing.T) {
	// 创建测试配置
	config := DefaultAppRegistryConfig()
	config.EnableHealthCheck = false // 禁用健康检查以简化测试

	testLogger := logger.NewTestLogger()
	manager, mockClient := NewMockAppRegistryManager(config, testLogger)
	defer manager.Stop()

	_ = mockClient // 使用模拟客户端

	// 创建测试注册信息
	registration := &AppRegistration{
		AppID:       "test-app-001",
		AppName:     "test-app",
		InstanceID:  "test-instance-001",
		ServiceName: "test-service",
		Host:        "127.0.0.1",
		Port:        8080,
		Version:     "1.0.0",
		Environment: "test",
		TenantID:    "tenant-001",
		Metadata: map[string]string{
			"language": "go",
			"framework": "gin",
		},
	}

	ctx := context.Background()

	// 测试注册应用
	err := manager.RegisterApp(ctx, registration)
	require.NoError(t, err)

	// 验证注册信息
	savedReg, exists := manager.GetRegistration(registration.InstanceID)
	assert.True(t, exists)
	assert.Equal(t, registration.AppID, savedReg.AppID)
	assert.Equal(t, registration.ServiceName, savedReg.ServiceName)
	assert.Equal(t, "registered", savedReg.Status)
	assert.Equal(t, "http", savedReg.Protocol) // 默认协议
	assert.Equal(t, "/health", savedReg.HealthCheckPath) // 默认健康检查路径
}

func TestAppRegistryManager_DeregisterApp(t *testing.T) {
	config := DefaultAppRegistryConfig()
	config.EnableHealthCheck = false

	testLogger := logger.NewTestLogger()
	manager, _ := NewMockAppRegistryManager(config, testLogger)
	defer manager.Stop()

	// 先注册一个应用
	registration := &AppRegistration{
		AppID:       "test-app-002",
		AppName:     "test-app",
		InstanceID:  "test-instance-002",
		ServiceName: "test-service",
		Host:        "127.0.0.1",
		Port:        8080,
	}

	ctx := context.Background()
	err := manager.RegisterApp(ctx, registration)
	require.NoError(t, err)

	// 验证注册成功
	_, exists := manager.GetRegistration(registration.InstanceID)
	assert.True(t, exists)

	// 测试注销应用
	err = manager.DeregisterApp(ctx, registration.InstanceID)
	require.NoError(t, err)

	// 验证注销成功
	_, exists = manager.GetRegistration(registration.InstanceID)
	assert.False(t, exists)
}

func TestAppRegistryManager_ValidateRegistration(t *testing.T) {
	config := DefaultAppRegistryConfig()
	testLogger := logger.NewTestLogger()
	manager, _ := NewMockAppRegistryManager(config, testLogger)
	defer manager.Stop()

	tests := []struct {
		name          string
		registration  *AppRegistration
		expectedError bool
	}{
		{
			name: "有效注册信息",
			registration: &AppRegistration{
				AppID:       "valid-app",
				InstanceID:  "valid-instance",
				ServiceName: "valid-service",
				Host:        "127.0.0.1",
				Port:        8080,
			},
			expectedError: false,
		},
		{
			name: "缺少应用ID",
			registration: &AppRegistration{
				InstanceID:  "valid-instance",
				ServiceName: "valid-service",
				Host:        "127.0.0.1",
				Port:        8080,
			},
			expectedError: true,
		},
		{
			name: "缺少实例ID",
			registration: &AppRegistration{
				AppID:       "valid-app",
				ServiceName: "valid-service",
				Host:        "127.0.0.1",
				Port:        8080,
			},
			expectedError: true,
		},
		{
			name: "无效端口",
			registration: &AppRegistration{
				AppID:       "valid-app",
				InstanceID:  "valid-instance",
				ServiceName: "valid-service",
				Host:        "127.0.0.1",
				Port:        0,
			},
			expectedError: true,
		},
		{
			name: "无效主机地址",
			registration: &AppRegistration{
				AppID:       "valid-app",
				InstanceID:  "valid-instance",
				ServiceName: "valid-service",
				Host:        "",
				Port:        8080,
			},
			expectedError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := manager.validateRegistration(tt.registration)
			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestAppRegistryManager_SetRegistrationDefaults(t *testing.T) {
	config := DefaultAppRegistryConfig()
	testLogger := logger.NewTestLogger()
	manager, _ := NewMockAppRegistryManager(config, testLogger)
	defer manager.Stop()

	registration := &AppRegistration{
		AppID:       "test-app",
		InstanceID:  "test-instance",
		ServiceName: "test-service",
		Host:        "127.0.0.1",
		Port:        8080,
		// 其他字段留空，测试默认值设置
	}

	manager.setRegistrationDefaults(registration)

	assert.Equal(t, "http", registration.Protocol)
	assert.Equal(t, "/", registration.Path)
	assert.Equal(t, "/health", registration.HealthCheckPath)
	assert.Equal(t, 30*time.Second, registration.HealthCheckInterval)
	assert.Equal(t, 5*time.Second, registration.HealthCheckTimeout)
	assert.Equal(t, "development", registration.Environment)
	assert.NotNil(t, registration.Metadata)
}

func TestAppRegistryManager_ListRegistrations(t *testing.T) {
	config := DefaultAppRegistryConfig()
	config.EnableHealthCheck = false

	testLogger := logger.NewTestLogger()
	manager, _ := NewMockAppRegistryManager(config, testLogger)
	defer manager.Stop()

	ctx := context.Background()

	// 注册多个应用
	registrations := []*AppRegistration{
		{
			AppID:       "app-001",
			InstanceID:  "instance-001",
			ServiceName: "service-001",
			Host:        "127.0.0.1",
			Port:        8080,
		},
		{
			AppID:       "app-002",
			InstanceID:  "instance-002",
			ServiceName: "service-002",
			Host:        "127.0.0.1",
			Port:        8081,
		},
	}

	for _, reg := range registrations {
		err := manager.RegisterApp(ctx, reg)
		require.NoError(t, err)
	}

	// 获取所有注册信息
	allRegistrations := manager.ListRegistrations()
	assert.Len(t, allRegistrations, 2)

	// 验证注册信息
	instanceIDs := make(map[string]bool)
	for _, reg := range allRegistrations {
		instanceIDs[reg.InstanceID] = true
	}

	assert.True(t, instanceIDs["instance-001"])
	assert.True(t, instanceIDs["instance-002"])
}

func TestAppRegistryManager_UpdateAppStatus(t *testing.T) {
	config := DefaultAppRegistryConfig()
	config.EnableHealthCheck = false

	testLogger := logger.NewTestLogger()
	manager, _ := NewMockAppRegistryManager(config, testLogger)
	defer manager.Stop()

	// 注册一个应用
	registration := &AppRegistration{
		AppID:       "test-app",
		InstanceID:  "test-instance",
		ServiceName: "test-service",
		Host:        "127.0.0.1",
		Port:        8080,
	}

	ctx := context.Background()
	err := manager.RegisterApp(ctx, registration)
	require.NoError(t, err)

	// 更新状态
	err = manager.UpdateAppStatus(registration.InstanceID, "running")
	require.NoError(t, err)

	// 验证状态更新
	savedReg, exists := manager.GetRegistration(registration.InstanceID)
	assert.True(t, exists)
	assert.Equal(t, "running", savedReg.Status)

	// 测试更新不存在的实例
	err = manager.UpdateAppStatus("non-existent", "running")
	assert.Error(t, err)
}

func TestDefaultAppRegistryConfig(t *testing.T) {
	config := DefaultAppRegistryConfig()

	assert.Equal(t, "http://localhost:8080", config.RegistryURL)
	assert.Equal(t, "/health", config.DefaultHealthCheckPath)
	assert.Equal(t, 30*time.Second, config.DefaultHealthInterval)
	assert.Equal(t, 5*time.Second, config.DefaultHealthTimeout)
	assert.Equal(t, 3, config.MaxFailureCount)
	assert.True(t, config.AutoDeregisterOnFailure)
	assert.True(t, config.EnableHealthCheck)
}

func TestIsValidHostname(t *testing.T) {
	tests := []struct {
		hostname string
		expected bool
	}{
		{"localhost", true},
		{"example.com", true},
		{"sub.example.com", true},
		{"127.0.0.1", true}, // IP 地址在我们的简化验证中被认为是有效的
		{"", false},
		{repeat("a", 64), false}, // 超过63字符的部分
		{"valid-hostname", true},
		{"invalid_hostname", true}, // 下划线在某些情况下是允许的
	}

	for _, tt := range tests {
		t.Run(tt.hostname, func(t *testing.T) {
			result := isValidHostname(tt.hostname)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// 辅助函数：重复字符串
func repeat(s string, count int) string {
	result := ""
	for i := 0; i < count; i++ {
		result += s
	}
	return result
}
