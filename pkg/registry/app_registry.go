package registry

import (
	"context"
	"fmt"
	"net"
	"strings"
	"sync"
	"time"

	"paas-platform/pkg/logger"
)

// AppRegistryManager 应用注册管理器
type AppRegistryManager struct {
	client           RegistryClientInterface
	logger           logger.Logger
	registrations    map[string]*AppRegistration
	mutex            sync.RWMutex
	healthCheckTicker *time.Ticker
	ctx              context.Context
	cancel           context.CancelFunc
	config           AppRegistryConfig
}

// AppRegistration 应用注册信息
type AppRegistration struct {
	AppID       string            `json:"app_id"`
	AppName     string            `json:"app_name"`
	InstanceID  string            `json:"instance_id"`
	ServiceName string            `json:"service_name"`
	Host        string            `json:"host"`
	Port        int               `json:"port"`
	Protocol    string            `json:"protocol"`
	Path        string            `json:"path"`
	Version     string            `json:"version"`
	Environment string            `json:"environment"`
	TenantID    string            `json:"tenant_id"`
	Metadata    map[string]string `json:"metadata"`
	
	// 健康检查配置
	HealthCheckPath     string        `json:"health_check_path"`
	HealthCheckInterval time.Duration `json:"health_check_interval"`
	HealthCheckTimeout  time.Duration `json:"health_check_timeout"`
	
	// 状态信息
	Status       string    `json:"status"`
	RegisteredAt time.Time `json:"registered_at"`
	LastHealthy  time.Time `json:"last_healthy"`
	FailureCount int       `json:"failure_count"`
}

// AppRegistryConfig 应用注册配置
type AppRegistryConfig struct {
	RegistryURL             string        `json:"registry_url"`
	DefaultHealthCheckPath  string        `json:"default_health_check_path"`
	DefaultHealthInterval   time.Duration `json:"default_health_interval"`
	DefaultHealthTimeout    time.Duration `json:"default_health_timeout"`
	MaxFailureCount         int           `json:"max_failure_count"`
	AutoDeregisterOnFailure bool          `json:"auto_deregister_on_failure"`
	EnableHealthCheck       bool          `json:"enable_health_check"`
}

// NewAppRegistryManager 创建应用注册管理器
func NewAppRegistryManager(config AppRegistryConfig, logger logger.Logger) *AppRegistryManager {
	// 设置默认值
	if config.DefaultHealthCheckPath == "" {
		config.DefaultHealthCheckPath = "/health"
	}
	if config.DefaultHealthInterval == 0 {
		config.DefaultHealthInterval = 30 * time.Second
	}
	if config.DefaultHealthTimeout == 0 {
		config.DefaultHealthTimeout = 5 * time.Second
	}
	if config.MaxFailureCount == 0 {
		config.MaxFailureCount = 3
	}

	ctx, cancel := context.WithCancel(context.Background())
	
	client := RegistryClientFromConfig(config.RegistryURL, logger)
	
	manager := &AppRegistryManager{
		client:        client,
		logger:        logger,
		registrations: make(map[string]*AppRegistration),
		ctx:           ctx,
		cancel:        cancel,
		config:        config,
	}

	// 启动健康检查
	if config.EnableHealthCheck {
		manager.startHealthCheck()
	}

	return manager
}

// RegisterApp 注册应用实例
func (arm *AppRegistryManager) RegisterApp(ctx context.Context, registration *AppRegistration) error {
	arm.logger.Info("开始注册应用实例",
		"app_id", registration.AppID,
		"instance_id", registration.InstanceID,
		"service_name", registration.ServiceName)

	// 验证注册信息
	if err := arm.validateRegistration(registration); err != nil {
		return fmt.Errorf("注册信息验证失败: %w", err)
	}

	// 设置默认值
	arm.setRegistrationDefaults(registration)

	// 构建服务注册信息
	serviceReg := &ServiceRegistration{
		ServiceName: registration.ServiceName,
		InstanceID:  registration.InstanceID,
		Host:        registration.Host,
		Port:        registration.Port,
		Protocol:    registration.Protocol,
		Path:        registration.Path,
		Weight:      100, // 默认权重
		Metadata: map[string]string{
			"app_id":      registration.AppID,
			"app_name":    registration.AppName,
			"version":     registration.Version,
			"environment": registration.Environment,
			"tenant_id":   registration.TenantID,
			"registered_at": time.Now().Format(time.RFC3339),
		},
		HealthCheckURL:      fmt.Sprintf("%s://%s:%d%s", registration.Protocol, registration.Host, registration.Port, registration.HealthCheckPath),
		HealthCheckInterval: registration.HealthCheckInterval.String(),
		HealthCheckTimeout:  registration.HealthCheckTimeout.String(),
		AppID:               registration.AppID,
		AppName:             registration.AppName,
		Version:             registration.Version,
		Environment:         registration.Environment,
		TenantID:            registration.TenantID,
	}

	// 合并自定义元数据
	for k, v := range registration.Metadata {
		serviceReg.Metadata[k] = v
	}

	// 注册到服务注册中心
	if err := arm.client.RegisterService(ctx, serviceReg); err != nil {
		return fmt.Errorf("注册服务失败: %w", err)
	}

	// 保存注册信息
	registration.Status = "registered"
	registration.RegisteredAt = time.Now()
	registration.LastHealthy = time.Now()
	registration.FailureCount = 0

	arm.mutex.Lock()
	arm.registrations[registration.InstanceID] = registration
	arm.mutex.Unlock()

	arm.logger.Info("应用实例注册成功",
		"app_id", registration.AppID,
		"instance_id", registration.InstanceID,
		"service_name", registration.ServiceName,
		"endpoint", fmt.Sprintf("%s://%s:%d", registration.Protocol, registration.Host, registration.Port))

	return nil
}

// DeregisterApp 注销应用实例
func (arm *AppRegistryManager) DeregisterApp(ctx context.Context, instanceID string) error {
	arm.mutex.Lock()
	registration, exists := arm.registrations[instanceID]
	if !exists {
		arm.mutex.Unlock()
		return fmt.Errorf("应用实例不存在: %s", instanceID)
	}
	delete(arm.registrations, instanceID)
	arm.mutex.Unlock()

	arm.logger.Info("开始注销应用实例",
		"app_id", registration.AppID,
		"instance_id", instanceID,
		"service_name", registration.ServiceName)

	// 从服务注册中心注销
	if err := arm.client.DeregisterService(ctx, registration.ServiceName, instanceID); err != nil {
		arm.logger.Error("注销服务失败", "error", err, "instance_id", instanceID)
		return fmt.Errorf("注销服务失败: %w", err)
	}

	arm.logger.Info("应用实例注销成功",
		"app_id", registration.AppID,
		"instance_id", instanceID,
		"service_name", registration.ServiceName)

	return nil
}

// GetRegistration 获取注册信息
func (arm *AppRegistryManager) GetRegistration(instanceID string) (*AppRegistration, bool) {
	arm.mutex.RLock()
	defer arm.mutex.RUnlock()
	
	registration, exists := arm.registrations[instanceID]
	return registration, exists
}

// ListRegistrations 列出所有注册信息
func (arm *AppRegistryManager) ListRegistrations() []*AppRegistration {
	arm.mutex.RLock()
	defer arm.mutex.RUnlock()
	
	registrations := make([]*AppRegistration, 0, len(arm.registrations))
	for _, reg := range arm.registrations {
		registrations = append(registrations, reg)
	}
	
	return registrations
}

// UpdateAppStatus 更新应用状态
func (arm *AppRegistryManager) UpdateAppStatus(instanceID, status string) error {
	arm.mutex.Lock()
	defer arm.mutex.Unlock()
	
	registration, exists := arm.registrations[instanceID]
	if !exists {
		return fmt.Errorf("应用实例不存在: %s", instanceID)
	}
	
	registration.Status = status
	
	arm.logger.Debug("应用状态更新",
		"instance_id", instanceID,
		"status", status)
	
	return nil
}

// validateRegistration 验证注册信息
func (arm *AppRegistryManager) validateRegistration(registration *AppRegistration) error {
	if registration.AppID == "" {
		return fmt.Errorf("应用ID不能为空")
	}
	if registration.InstanceID == "" {
		return fmt.Errorf("实例ID不能为空")
	}
	if registration.ServiceName == "" {
		return fmt.Errorf("服务名称不能为空")
	}
	if registration.Host == "" {
		return fmt.Errorf("主机地址不能为空")
	}
	if registration.Port <= 0 || registration.Port > 65535 {
		return fmt.Errorf("端口号无效: %d", registration.Port)
	}
	
	// 验证主机地址格式
	if net.ParseIP(registration.Host) == nil && !isValidHostname(registration.Host) {
		return fmt.Errorf("主机地址格式无效: %s", registration.Host)
	}
	
	return nil
}

// setRegistrationDefaults 设置注册默认值
func (arm *AppRegistryManager) setRegistrationDefaults(registration *AppRegistration) {
	if registration.Protocol == "" {
		registration.Protocol = "http"
	}
	if registration.Path == "" {
		registration.Path = "/"
	}
	if registration.HealthCheckPath == "" {
		registration.HealthCheckPath = arm.config.DefaultHealthCheckPath
	}
	if registration.HealthCheckInterval == 0 {
		registration.HealthCheckInterval = arm.config.DefaultHealthInterval
	}
	if registration.HealthCheckTimeout == 0 {
		registration.HealthCheckTimeout = arm.config.DefaultHealthTimeout
	}
	if registration.Environment == "" {
		registration.Environment = "development"
	}
	if registration.Metadata == nil {
		registration.Metadata = make(map[string]string)
	}
}

// startHealthCheck 启动健康检查
func (arm *AppRegistryManager) startHealthCheck() {
	arm.healthCheckTicker = time.NewTicker(10 * time.Second) // 每10秒检查一次
	
	go func() {
		for {
			select {
			case <-arm.ctx.Done():
				return
			case <-arm.healthCheckTicker.C:
				arm.performHealthCheck()
			}
		}
	}()
	
	arm.logger.Info("健康检查已启动")
}

// performHealthCheck 执行健康检查
func (arm *AppRegistryManager) performHealthCheck() {
	arm.mutex.RLock()
	registrations := make([]*AppRegistration, 0, len(arm.registrations))
	for _, reg := range arm.registrations {
		registrations = append(registrations, reg)
	}
	arm.mutex.RUnlock()

	for _, registration := range registrations {
		go arm.checkInstanceHealth(registration)
	}
}

// checkInstanceHealth 检查实例健康状态
func (arm *AppRegistryManager) checkInstanceHealth(registration *AppRegistration) {
	// 简化的健康检查实现
	// 实际应该发送 HTTP 请求到健康检查端点
	healthy := true // 假设健康
	
	if healthy {
		arm.mutex.Lock()
		registration.LastHealthy = time.Now()
		registration.FailureCount = 0
		arm.mutex.Unlock()
		
		// 更新服务注册中心的健康状态
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		
		arm.client.UpdateServiceHealth(ctx, registration.ServiceName, registration.InstanceID, true)
	} else {
		arm.mutex.Lock()
		registration.FailureCount++
		failureCount := registration.FailureCount
		arm.mutex.Unlock()
		
		arm.logger.Warn("应用实例健康检查失败",
			"instance_id", registration.InstanceID,
			"failure_count", failureCount)
		
		// 如果失败次数超过阈值，自动注销
		if arm.config.AutoDeregisterOnFailure && failureCount >= arm.config.MaxFailureCount {
			arm.logger.Error("应用实例健康检查失败次数过多，自动注销",
				"instance_id", registration.InstanceID,
				"failure_count", failureCount)
			
			ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
			defer cancel()
			
			arm.DeregisterApp(ctx, registration.InstanceID)
		}
	}
}

// Stop 停止注册管理器
func (arm *AppRegistryManager) Stop() {
	arm.logger.Info("停止应用注册管理器")
	
	if arm.healthCheckTicker != nil {
		arm.healthCheckTicker.Stop()
	}
	
	arm.cancel()
	
	// 注销所有注册的应用
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	arm.mutex.RLock()
	instanceIDs := make([]string, 0, len(arm.registrations))
	for instanceID := range arm.registrations {
		instanceIDs = append(instanceIDs, instanceID)
	}
	arm.mutex.RUnlock()
	
	for _, instanceID := range instanceIDs {
		if err := arm.DeregisterApp(ctx, instanceID); err != nil {
			arm.logger.Error("停止时注销应用失败", "error", err, "instance_id", instanceID)
		}
	}
}

// isValidHostname 验证主机名格式
func isValidHostname(hostname string) bool {
	if len(hostname) == 0 || len(hostname) > 253 {
		return false
	}
	
	// 简化的主机名验证
	parts := strings.Split(hostname, ".")
	for _, part := range parts {
		if len(part) == 0 || len(part) > 63 {
			return false
		}
	}
	
	return true
}

// DefaultAppRegistryConfig 默认应用注册配置
func DefaultAppRegistryConfig() AppRegistryConfig {
	return AppRegistryConfig{
		RegistryURL:             "http://localhost:8080",
		DefaultHealthCheckPath:  "/health",
		DefaultHealthInterval:   30 * time.Second,
		DefaultHealthTimeout:    5 * time.Second,
		MaxFailureCount:         3,
		AutoDeregisterOnFailure: true,
		EnableHealthCheck:       true,
	}
}
