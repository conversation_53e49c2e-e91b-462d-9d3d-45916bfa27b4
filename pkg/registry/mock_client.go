package registry

import (
	"context"
	"fmt"
	"sync"

	"paas-platform/pkg/logger"
)

// MockRegistryClient 模拟注册客户端（用于测试）
type MockRegistryClient struct {
	logger        logger.Logger
	registrations map[string]*ServiceRegistration
	mutex         sync.RWMutex
	shouldFail    bool
	failureError  error
}

// NewMockRegistryClient 创建模拟注册客户端
func NewMockRegistryClient(logger logger.Logger) *MockRegistryClient {
	return &MockRegistryClient{
		logger:        logger,
		registrations: make(map[string]*ServiceRegistration),
		shouldFail:    false,
	}
}

// RegisterService 模拟注册服务
func (mrc *MockRegistryClient) RegisterService(ctx context.Context, registration *ServiceRegistration) error {
	if mrc.shouldFail {
		return mrc.failureError
	}

	mrc.mutex.Lock()
	defer mrc.mutex.Unlock()

	key := fmt.Sprintf("%s:%s", registration.ServiceName, registration.InstanceID)
	mrc.registrations[key] = registration

	mrc.logger.Debug("模拟注册服务成功",
		"service_name", registration.ServiceName,
		"instance_id", registration.InstanceID)

	return nil
}

// DeregisterService 模拟注销服务
func (mrc *MockRegistryClient) DeregisterService(ctx context.Context, serviceName, instanceID string) error {
	if mrc.shouldFail {
		return mrc.failureError
	}

	mrc.mutex.Lock()
	defer mrc.mutex.Unlock()

	key := fmt.Sprintf("%s:%s", serviceName, instanceID)
	delete(mrc.registrations, key)

	mrc.logger.Debug("模拟注销服务成功",
		"service_name", serviceName,
		"instance_id", instanceID)

	return nil
}

// UpdateServiceHealth 模拟更新服务健康状态
func (mrc *MockRegistryClient) UpdateServiceHealth(ctx context.Context, serviceName, instanceID string, healthy bool) error {
	if mrc.shouldFail {
		return mrc.failureError
	}

	mrc.mutex.RLock()
	defer mrc.mutex.RUnlock()

	key := fmt.Sprintf("%s:%s", serviceName, instanceID)
	if _, exists := mrc.registrations[key]; !exists {
		return fmt.Errorf("服务实例不存在: %s", key)
	}

	status := "healthy"
	if !healthy {
		status = "unhealthy"
	}

	mrc.logger.Debug("模拟更新服务健康状态",
		"service_name", serviceName,
		"instance_id", instanceID,
		"status", status)

	return nil
}

// GetServiceInstances 模拟获取服务实例列表
func (mrc *MockRegistryClient) GetServiceInstances(ctx context.Context, serviceName string) ([]ServiceInstance, error) {
	if mrc.shouldFail {
		return nil, mrc.failureError
	}

	mrc.mutex.RLock()
	defer mrc.mutex.RUnlock()

	var instances []ServiceInstance
	for _, reg := range mrc.registrations {
		if reg.ServiceName == serviceName {
			instances = append(instances, ServiceInstance{
				ID:       reg.InstanceID,
				Host:     reg.Host,
				Port:     reg.Port,
				Protocol: reg.Protocol,
				Path:     reg.Path,
				Weight:   reg.Weight,
				Status:   "healthy",
				Metadata: reg.Metadata,
			})
		}
	}

	return instances, nil
}

// SetShouldFail 设置是否应该失败
func (mrc *MockRegistryClient) SetShouldFail(shouldFail bool, err error) {
	mrc.shouldFail = shouldFail
	mrc.failureError = err
}

// GetRegistrations 获取所有注册信息（测试用）
func (mrc *MockRegistryClient) GetRegistrations() map[string]*ServiceRegistration {
	mrc.mutex.RLock()
	defer mrc.mutex.RUnlock()

	result := make(map[string]*ServiceRegistration)
	for k, v := range mrc.registrations {
		result[k] = v
	}
	return result
}

// Clear 清空所有注册信息（测试用）
func (mrc *MockRegistryClient) Clear() {
	mrc.mutex.Lock()
	defer mrc.mutex.Unlock()

	mrc.registrations = make(map[string]*ServiceRegistration)
}

// NewMockAppRegistryManager 创建使用模拟客户端的应用注册管理器
func NewMockAppRegistryManager(config AppRegistryConfig, logger logger.Logger) (*AppRegistryManager, *MockRegistryClient) {
	ctx, cancel := context.WithCancel(context.Background())

	mockClient := NewMockRegistryClient(logger)

	manager := &AppRegistryManager{
		client:        RegistryClientInterface(mockClient),
		logger:        logger,
		registrations: make(map[string]*AppRegistration),
		ctx:           ctx,
		cancel:        cancel,
		config:        config,
	}

	// 不启动健康检查，因为这是测试环境
	return manager, mockClient
}
