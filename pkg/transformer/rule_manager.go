package transformer

import (
	"encoding/json"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"strings"
	"sync"

	"paas-platform/pkg/logger"
)

// RuleManager 转换规则管理器
type RuleManager struct {
	rules       map[string]*TransformRule
	mutex       sync.RWMutex
	logger      logger.Logger
	rulesDir    string
	autoReload  bool
}

// NewRuleManager 创建规则管理器
func NewRuleManager(logger logger.Logger, rulesDir string, autoReload bool) *RuleManager {
	return &RuleManager{
		rules:      make(map[string]*TransformRule),
		logger:     logger,
		rulesDir:   rulesDir,
		autoReload: autoReload,
	}
}

// LoadRulesFromDirectory 从目录加载规则
func (rm *RuleManager) LoadRulesFromDirectory() error {
	if rm.rulesDir == "" {
		return fmt.Errorf("规则目录未设置")
	}
	
	if _, err := os.Stat(rm.rulesDir); os.IsNotExist(err) {
		rm.logger.Warn("规则目录不存在", "dir", rm.rulesDir)
		return nil
	}
	
	rm.logger.Info("开始加载转换规则", "dir", rm.rulesDir)
	
	err := filepath.WalkDir(rm.rulesDir, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}
		
		if d.IsDir() || !strings.HasSuffix(path, ".json") {
			return nil
		}
		
		return rm.loadRuleFromFile(path)
	})
	
	if err != nil {
		return fmt.Errorf("加载规则失败: %w", err)
	}
	
	rm.logger.Info("转换规则加载完成", "count", len(rm.rules))
	return nil
}

// loadRuleFromFile 从文件加载规则
func (rm *RuleManager) loadRuleFromFile(filePath string) error {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("读取规则文件失败 %s: %w", filePath, err)
	}
	
	var rule TransformRule
	if err := json.Unmarshal(data, &rule); err != nil {
		return fmt.Errorf("解析规则文件失败 %s: %w", filePath, err)
	}
	
	if err := rm.validateRule(&rule); err != nil {
		return fmt.Errorf("规则验证失败 %s: %w", filePath, err)
	}
	
	rm.AddRule(&rule)
	rm.logger.Debug("加载规则文件", "file", filePath, "path", rule.Path, "method", rule.Method)
	
	return nil
}

// validateRule 验证规则
func (rm *RuleManager) validateRule(rule *TransformRule) error {
	if rule.Path == "" {
		return fmt.Errorf("规则路径不能为空")
	}
	
	if rule.Method == "" {
		return fmt.Errorf("规则方法不能为空")
	}
	
	// 验证请求转换规则
	if rule.RequestRules != nil {
		if err := rm.validateRequestRules(rule.RequestRules); err != nil {
			return fmt.Errorf("请求转换规则验证失败: %w", err)
		}
	}
	
	// 验证响应转换规则
	if rule.ResponseRules != nil {
		if err := rm.validateResponseRules(rule.ResponseRules); err != nil {
			return fmt.Errorf("响应转换规则验证失败: %w", err)
		}
	}
	
	return nil
}

// validateRequestRules 验证请求转换规则
func (rm *RuleManager) validateRequestRules(rules *RequestTransformRule) error {
	// 验证请求头规则
	for i, rule := range rules.Headers {
		if err := rm.validateHeaderRule(rule); err != nil {
			return fmt.Errorf("请求头规则 %d 验证失败: %w", i, err)
		}
	}
	
	// 验证查询参数规则
	for i, rule := range rules.QueryParams {
		if err := rm.validateParamRule(rule); err != nil {
			return fmt.Errorf("查询参数规则 %d 验证失败: %w", i, err)
		}
	}
	
	// 验证路径参数规则
	for i, rule := range rules.PathParams {
		if err := rm.validateParamRule(rule); err != nil {
			return fmt.Errorf("路径参数规则 %d 验证失败: %w", i, err)
		}
	}
	
	// 验证请求体规则
	if rules.Body != nil {
		if err := rm.validateBodyRule(rules.Body); err != nil {
			return fmt.Errorf("请求体规则验证失败: %w", err)
		}
	}
	
	return nil
}

// validateResponseRules 验证响应转换规则
func (rm *RuleManager) validateResponseRules(rules *ResponseTransformRule) error {
	// 验证响应头规则
	for i, rule := range rules.Headers {
		if err := rm.validateHeaderRule(rule); err != nil {
			return fmt.Errorf("响应头规则 %d 验证失败: %w", i, err)
		}
	}
	
	// 验证响应体规则
	if rules.Body != nil {
		if err := rm.validateResponseBodyRule(rules.Body); err != nil {
			return fmt.Errorf("响应体规则验证失败: %w", err)
		}
	}
	
	// 验证状态码规则
	if rules.StatusCode != nil {
		if err := rm.validateStatusCodeRule(rules.StatusCode); err != nil {
			return fmt.Errorf("状态码规则验证失败: %w", err)
		}
	}
	
	return nil
}

// validateHeaderRule 验证请求头规则
func (rm *RuleManager) validateHeaderRule(rule HeaderRule) error {
	validActions := []string{"add", "remove", "rename", "replace"}
	if !rm.contains(validActions, rule.Action) {
		return fmt.Errorf("无效的操作类型: %s", rule.Action)
	}
	
	if rule.Name == "" {
		return fmt.Errorf("请求头名称不能为空")
	}
	
	return nil
}

// validateParamRule 验证参数规则
func (rm *RuleManager) validateParamRule(rule ParamRule) error {
	validActions := []string{"add", "remove", "rename", "replace", "convert"}
	if !rm.contains(validActions, rule.Action) {
		return fmt.Errorf("无效的操作类型: %s", rule.Action)
	}
	
	if rule.Name == "" {
		return fmt.Errorf("参数名称不能为空")
	}
	
	if rule.Action == "convert" {
		validTypes := []string{"string", "int", "float", "bool"}
		if !rm.contains(validTypes, rule.DataType) {
			return fmt.Errorf("无效的数据类型: %s", rule.DataType)
		}
	}
	
	return nil
}

// validateBodyRule 验证请求体规则
func (rm *RuleManager) validateBodyRule(rule *BodyRule) error {
	validTransforms := []string{"json_transform", "xml_transform", "form_transform"}
	if !rm.contains(validTransforms, rule.Transform) {
		return fmt.Errorf("无效的转换类型: %s", rule.Transform)
	}
	
	return nil
}

// validateResponseBodyRule 验证响应体规则
func (rm *RuleManager) validateResponseBodyRule(rule *ResponseBodyRule) error {
	validTransforms := []string{"json_transform", "xml_transform"}
	if !rm.contains(validTransforms, rule.Transform) {
		return fmt.Errorf("无效的转换类型: %s", rule.Transform)
	}
	
	// 验证响应包装器
	if rule.Wrapper != nil && rule.Wrapper.Enabled {
		if rule.Wrapper.CodeField == "" || rule.Wrapper.DataField == "" {
			return fmt.Errorf("响应包装器的代码字段和数据字段不能为空")
		}
	}
	
	return nil
}

// validateStatusCodeRule 验证状态码规则
func (rm *RuleManager) validateStatusCodeRule(rule *StatusCodeRule) error {
	if rule.From < 100 || rule.From > 599 {
		return fmt.Errorf("无效的源状态码: %d", rule.From)
	}
	
	if rule.To < 100 || rule.To > 599 {
		return fmt.Errorf("无效的目标状态码: %d", rule.To)
	}
	
	return nil
}

// contains 检查切片是否包含元素
func (rm *RuleManager) contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// AddRule 添加规则
func (rm *RuleManager) AddRule(rule *TransformRule) {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()
	
	key := fmt.Sprintf("%s:%s", rule.Method, rule.Path)
	rm.rules[key] = rule
	
	rm.logger.Debug("添加转换规则", "method", rule.Method, "path", rule.Path, "enabled", rule.Enabled)
}

// RemoveRule 移除规则
func (rm *RuleManager) RemoveRule(method, path string) {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()
	
	key := fmt.Sprintf("%s:%s", method, path)
	delete(rm.rules, key)
	
	rm.logger.Debug("移除转换规则", "method", method, "path", path)
}

// GetRule 获取规则
func (rm *RuleManager) GetRule(method, path string) *TransformRule {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()
	
	key := fmt.Sprintf("%s:%s", method, path)
	return rm.rules[key]
}

// GetAllRules 获取所有规则
func (rm *RuleManager) GetAllRules() []*TransformRule {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()
	
	rules := make([]*TransformRule, 0, len(rm.rules))
	for _, rule := range rm.rules {
		rules = append(rules, rule)
	}
	
	return rules
}

// EnableRule 启用规则
func (rm *RuleManager) EnableRule(method, path string) error {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()
	
	key := fmt.Sprintf("%s:%s", method, path)
	if rule, exists := rm.rules[key]; exists {
		rule.Enabled = true
		rm.logger.Info("启用转换规则", "method", method, "path", path)
		return nil
	}
	
	return fmt.Errorf("规则不存在: %s %s", method, path)
}

// DisableRule 禁用规则
func (rm *RuleManager) DisableRule(method, path string) error {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()
	
	key := fmt.Sprintf("%s:%s", method, path)
	if rule, exists := rm.rules[key]; exists {
		rule.Enabled = false
		rm.logger.Info("禁用转换规则", "method", method, "path", path)
		return nil
	}
	
	return fmt.Errorf("规则不存在: %s %s", method, path)
}

// SaveRuleToFile 保存规则到文件
func (rm *RuleManager) SaveRuleToFile(rule *TransformRule, filename string) error {
	if rm.rulesDir == "" {
		return fmt.Errorf("规则目录未设置")
	}
	
	// 确保目录存在
	if err := os.MkdirAll(rm.rulesDir, 0755); err != nil {
		return fmt.Errorf("创建规则目录失败: %w", err)
	}
	
	filePath := filepath.Join(rm.rulesDir, filename)
	data, err := json.MarshalIndent(rule, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化规则失败: %w", err)
	}
	
	if err := os.WriteFile(filePath, data, 0644); err != nil {
		return fmt.Errorf("写入规则文件失败: %w", err)
	}
	
	rm.logger.Info("保存规则到文件", "file", filePath, "method", rule.Method, "path", rule.Path)
	return nil
}

// GetStats 获取统计信息
func (rm *RuleManager) GetStats() map[string]interface{} {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()
	
	enabledCount := 0
	disabledCount := 0
	
	for _, rule := range rm.rules {
		if rule.Enabled {
			enabledCount++
		} else {
			disabledCount++
		}
	}
	
	return map[string]interface{}{
		"total_rules":    len(rm.rules),
		"enabled_rules":  enabledCount,
		"disabled_rules": disabledCount,
		"rules_dir":      rm.rulesDir,
		"auto_reload":    rm.autoReload,
	}
}
