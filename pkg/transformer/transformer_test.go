package transformer

import (
	"bytes"
	"encoding/json"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"paas-platform/pkg/logger"
)

func TestRequestTransformer_TransformHeaders(t *testing.T) {
	logger := logger.NewTestLogger()
	transformer := NewRequestTransformer(logger)

	// 创建测试规则
	rule := &TransformRule{
		Path:    "/api/v1/test",
		Method:  "GET",
		Enabled: true,
		RequestRules: &RequestTransformRule{
			Headers: []HeaderRule{
				{
					Action: "add",
					Name:   "X-Test-Header",
					Value:  "test-value",
				},
				{
					Action: "remove",
					Name:   "X-Remove-Header",
				},
				{
					Action: "rename",
					Name:   "X-Old-Header",
					NewName: "X-New-Header",
				},
			},
		},
	}

	err := transformer.RegisterRule(rule)
	require.NoError(t, err)

	// 创建测试请求
	req := httptest.NewRequest("GET", "/api/v1/test", nil)
	req.Header.Set("X-Remove-Header", "should-be-removed")
	req.Header.Set("X-Old-Header", "old-value")

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 执行转换
	result := transformer.TransformRequest(c)

	// 验证结果
	assert.True(t, result.Success)
	assert.True(t, result.Modified)
	assert.Contains(t, result.Details, "添加请求头: X-Test-Header=test-value")
	assert.Contains(t, result.Details, "删除请求头: X-Remove-Header")
	assert.Contains(t, result.Details, "重命名请求头: X-Old-Header -> X-New-Header")

	// 验证请求头变化
	assert.Equal(t, "test-value", c.Request.Header.Get("X-Test-Header"))
	assert.Empty(t, c.Request.Header.Get("X-Remove-Header"))
	assert.Empty(t, c.Request.Header.Get("X-Old-Header"))
	assert.Equal(t, "old-value", c.Request.Header.Get("X-New-Header"))
}

func TestRequestTransformer_TransformQueryParams(t *testing.T) {
	logger := logger.NewTestLogger()
	transformer := NewRequestTransformer(logger)

	// 创建测试规则
	rule := &TransformRule{
		Path:    "/api/v1/test",
		Method:  "GET",
		Enabled: true,
		RequestRules: &RequestTransformRule{
			QueryParams: []ParamRule{
				{
					Action: "add",
					Name:   "default_param",
					Value:  "default_value",
				},
				{
					Action: "convert",
					Name:   "page",
					DataType: "int",
				},
				{
					Action: "rename",
					Name:   "old_param",
					NewName: "new_param",
				},
			},
		},
	}

	err := transformer.RegisterRule(rule)
	require.NoError(t, err)

	// 创建测试请求
	req := httptest.NewRequest("GET", "/api/v1/test?page=1&old_param=test", nil)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 执行转换
	result := transformer.TransformRequest(c)

	// 验证结果
	assert.True(t, result.Success)
	assert.True(t, result.Modified)

	// 验证查询参数变化
	query := c.Request.URL.Query()
	assert.Equal(t, "default_value", query.Get("default_param"))
	assert.Equal(t, "1", query.Get("page"))
	assert.Empty(t, query.Get("old_param"))
	assert.Equal(t, "test", query.Get("new_param"))
}

func TestRequestTransformer_TransformJSONBody(t *testing.T) {
	logger := logger.NewTestLogger()
	transformer := NewRequestTransformer(logger)

	// 创建测试规则
	rule := &TransformRule{
		Path:    "/api/v1/test",
		Method:  "POST",
		Enabled: true,
		RequestRules: &RequestTransformRule{
			Body: &BodyRule{
				ContentType: "application/json",
				Transform:   "json_transform",
				Rules: []BodyFieldRule{
					{
						Action: "add",
						Path:   "timestamp",
						Value:  "2023-01-01T00:00:00Z",
					},
					{
						Action: "remove",
						Path:   "sensitive_data",
					},
					{
						Action: "rename",
						Path:   "old_field",
						NewPath: "new_field",
					},
				},
			},
		},
	}

	err := transformer.RegisterRule(rule)
	require.NoError(t, err)

	// 创建测试请求体
	requestBody := map[string]interface{}{
		"name":           "test",
		"old_field":      "old_value",
		"sensitive_data": "secret",
	}
	bodyBytes, _ := json.Marshal(requestBody)

	req := httptest.NewRequest("POST", "/api/v1/test", bytes.NewBuffer(bodyBytes))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 执行转换
	result := transformer.TransformRequest(c)

	// 验证结果
	assert.True(t, result.Success)
	assert.True(t, result.Modified)

	// 读取转换后的请求体
	var transformedBody map[string]interface{}
	err = json.NewDecoder(c.Request.Body).Decode(&transformedBody)
	require.NoError(t, err)

	// 验证请求体变化
	assert.Equal(t, "test", transformedBody["name"])
	assert.Equal(t, "2023-01-01T00:00:00Z", transformedBody["timestamp"])
	assert.Equal(t, "old_value", transformedBody["new_field"])
	assert.NotContains(t, transformedBody, "old_field")
	assert.NotContains(t, transformedBody, "sensitive_data")
}

func TestResponseTransformer_MaskSensitiveData(t *testing.T) {
	logger := logger.NewTestLogger()
	transformer := NewResponseTransformer(logger)

	tests := []struct {
		name       string
		value      string
		maskType   string
		expected   string
	}{
		{
			name:     "手机号脱敏",
			value:    "13812345678",
			maskType: "phone",
			expected: "138****5678",
		},
		{
			name:     "邮箱脱敏",
			value:    "<EMAIL>",
			maskType: "email",
			expected: "u***<EMAIL>",
		},
		{
			name:     "身份证脱敏",
			value:    "110101199001011234",
			maskType: "id_card",
			expected: "110***********1234",
		},
		{
			name:     "信用卡脱敏",
			value:    "1234567890123456",
			maskType: "credit_card",
			expected: "1234 **** **** 3456",
		},
		{
			name:     "默认脱敏",
			value:    "sensitive",
			maskType: "default",
			expected: "se***ve",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := transformer.maskSensitiveData(tt.value, tt.maskType, "")
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestResponseTransformer_WrapResponse(t *testing.T) {
	logger := logger.NewTestLogger()
	transformer := NewResponseTransformer(logger)

	// 测试数据
	originalData := map[string]interface{}{
		"id":   1,
		"name": "test",
	}

	wrapper := &ResponseWrapper{
		Enabled:        true,
		CodeField:      "code",
		MessageField:   "message",
		DataField:      "data",
		TimestampField: "timestamp",
		SuccessCode:    0,
		ErrorCode:      -1,
	}

	// 测试成功响应包装
	result := transformer.wrapResponse(originalData, wrapper, 200)

	assert.Equal(t, 0, result["code"])
	assert.Equal(t, "success", result["message"])
	assert.Equal(t, originalData, result["data"])
	assert.Contains(t, result, "timestamp")

	// 测试错误响应包装
	result = transformer.wrapResponse(originalData, wrapper, 400)

	assert.Equal(t, -1, result["code"])
	assert.Equal(t, "error", result["message"])
	assert.Equal(t, originalData, result["data"])
}

func TestTransformRule_PathMatching(t *testing.T) {
	logger := logger.NewTestLogger()
	transformer := NewRequestTransformer(logger)

	tests := []struct {
		name        string
		rulePath    string
		requestPath string
		shouldMatch bool
	}{
		{
			name:        "精确匹配",
			rulePath:    "/api/v1/users",
			requestPath: "/api/v1/users",
			shouldMatch: true,
		},
		{
			name:        "路径参数匹配",
			rulePath:    "/api/v1/users/{id}",
			requestPath: "/api/v1/users/123",
			shouldMatch: true,
		},
		{
			name:        "通配符匹配",
			rulePath:    "*",
			requestPath: "/any/path",
			shouldMatch: true,
		},
		{
			name:        "不匹配",
			rulePath:    "/api/v1/users",
			requestPath: "/api/v1/apps",
			shouldMatch: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := transformer.matchPath(tt.rulePath, tt.requestPath)
			assert.Equal(t, tt.shouldMatch, result)
		})
	}
}

func TestDataTypeConversion(t *testing.T) {
	logger := logger.NewTestLogger()
	transformer := NewRequestTransformer(logger)

	tests := []struct {
		name     string
		value    string
		dataType string
		expected interface{}
		hasError bool
	}{
		{
			name:     "字符串转整数",
			value:    "123",
			dataType: "int",
			expected: 123,
			hasError: false,
		},
		{
			name:     "字符串转浮点数",
			value:    "123.45",
			dataType: "float",
			expected: 123.45,
			hasError: false,
		},
		{
			name:     "字符串转布尔值",
			value:    "true",
			dataType: "bool",
			expected: true,
			hasError: false,
		},
		{
			name:     "保持字符串",
			value:    "test",
			dataType: "string",
			expected: "test",
			hasError: false,
		},
		{
			name:     "无效整数转换",
			value:    "abc",
			dataType: "int",
			expected: nil,
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := transformer.convertDataType(tt.value, tt.dataType)
			if tt.hasError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}
