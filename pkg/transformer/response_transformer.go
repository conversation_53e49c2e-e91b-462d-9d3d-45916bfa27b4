package transformer

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// ResponseWriter 自定义响应写入器
type ResponseWriter struct {
	gin.ResponseWriter
	body       *bytes.Buffer
	statusCode int
}

// NewResponseWriter 创建响应写入器
func NewResponseWriter(w gin.ResponseWriter) *ResponseWriter {
	return &ResponseWriter{
		ResponseWriter: w,
		body:          bytes.NewBuffer(nil),
		statusCode:    http.StatusOK,
	}
}

// Write 写入响应数据
func (rw *ResponseWriter) Write(data []byte) (int, error) {
	rw.body.Write(data)
	return rw.ResponseWriter.Write(data)
}

// WriteHeader 写入响应头
func (rw *ResponseWriter) WriteHeader(statusCode int) {
	rw.statusCode = statusCode
	rw.ResponseWriter.WriteHeader(statusCode)
}

// GetBody 获取响应体
func (rw *ResponseWriter) GetBody() []byte {
	return rw.body.Bytes()
}

// GetStatusCode 获取状态码
func (rw *ResponseWriter) GetStatusCode() int {
	return rw.statusCode
}

// TransformResponse 转换响应
func (rt *ResponseTransformer) TransformResponse(c *gin.Context, responseData []byte, statusCode int) *TransformResult {
	rule := rt.getTransformRule(c.Request.Method, c.Request.URL.Path)
	if rule == nil || !rule.Enabled || rule.ResponseRules == nil {
		return &TransformResult{Success: true, Modified: false}
	}
	
	result := &TransformResult{
		Success:  true,
		Modified: false,
		Details:  make([]string, 0),
		Metadata: make(map[string]interface{}),
	}
	
	rt.logger.Debug("开始转换响应",
		"method", c.Request.Method,
		"path", c.Request.URL.Path,
		"status_code", statusCode)
	
	// 转换响应头
	if err := rt.transformResponseHeaders(c, rule.ResponseRules.Headers, result); err != nil {
		result.Success = false
		result.Error = err
		return result
	}
	
	// 转换状态码
	if rule.ResponseRules.StatusCode != nil {
		newStatusCode := rt.transformStatusCode(statusCode, rule.ResponseRules.StatusCode, c)
		if newStatusCode != statusCode {
			c.Status(newStatusCode)
			result.Modified = true
			result.Details = append(result.Details, fmt.Sprintf("转换状态码: %d -> %d", statusCode, newStatusCode))
		}
	}
	
	// 转换响应体
	if rule.ResponseRules.Body != nil && len(responseData) > 0 {
		transformedData, err := rt.transformResponseBody(responseData, rule.ResponseRules.Body, c, result)
		if err != nil {
			result.Success = false
			result.Error = err
			return result
		}
		
		if transformedData != nil {
			// 写入转换后的响应体
			c.Data(c.Writer.Status(), c.GetHeader("Content-Type"), transformedData)
			result.Modified = true
		}
	}
	
	if result.Modified {
		rt.logger.Info("响应转换完成",
			"method", c.Request.Method,
			"path", c.Request.URL.Path,
			"details", result.Details)
	}
	
	return result
}

// transformResponseHeaders 转换响应头
func (rt *ResponseTransformer) transformResponseHeaders(c *gin.Context, rules []HeaderRule, result *TransformResult) error {
	for _, rule := range rules {
		if !rt.evaluateCondition(rule.Condition, c) {
			continue
		}
		
		switch rule.Action {
		case "add":
			c.Header(rule.Name, rule.Value)
			result.Modified = true
			result.Details = append(result.Details, fmt.Sprintf("添加响应头: %s=%s", rule.Name, rule.Value))
			
		case "remove":
			c.Writer.Header().Del(rule.Name)
			result.Modified = true
			result.Details = append(result.Details, fmt.Sprintf("删除响应头: %s", rule.Name))
			
		case "rename":
			if value := c.Writer.Header().Get(rule.Name); value != "" {
				c.Writer.Header().Del(rule.Name)
				c.Header(rule.NewName, value)
				result.Modified = true
				result.Details = append(result.Details, fmt.Sprintf("重命名响应头: %s -> %s", rule.Name, rule.NewName))
			}
			
		case "replace":
			if rule.Pattern != "" {
				// 正则替换
				if value := c.Writer.Header().Get(rule.Name); value != "" {
					re, err := regexp.Compile(rule.Pattern)
					if err != nil {
						return fmt.Errorf("正则表达式错误: %w", err)
					}
					newValue := re.ReplaceAllString(value, rule.Replacement)
					c.Header(rule.Name, newValue)
					result.Modified = true
					result.Details = append(result.Details, fmt.Sprintf("替换响应头: %s=%s", rule.Name, newValue))
				}
			} else {
				// 直接替换
				c.Header(rule.Name, rule.Value)
				result.Modified = true
				result.Details = append(result.Details, fmt.Sprintf("替换响应头: %s=%s", rule.Name, rule.Value))
			}
		}
	}
	
	return nil
}

// transformStatusCode 转换状态码
func (rt *ResponseTransformer) transformStatusCode(statusCode int, rule *StatusCodeRule, c *gin.Context) int {
	if !rt.evaluateCondition(rule.Condition, c) {
		return statusCode
	}
	
	if rule.From == statusCode || rule.From == 0 {
		return rule.To
	}
	
	return statusCode
}

// transformResponseBody 转换响应体
func (rt *ResponseTransformer) transformResponseBody(responseData []byte, rule *ResponseBodyRule, c *gin.Context, result *TransformResult) ([]byte, error) {
	switch rule.Transform {
	case "json_transform":
		return rt.transformJSONResponse(responseData, rule, c, result)
	default:
		rt.logger.Warn("不支持的响应体转换类型", "transform", rule.Transform)
		return nil, nil
	}
}

// transformJSONResponse 转换 JSON 响应
func (rt *ResponseTransformer) transformJSONResponse(responseData []byte, rule *ResponseBodyRule, c *gin.Context, result *TransformResult) ([]byte, error) {
	var data interface{}
	if err := json.Unmarshal(responseData, &data); err != nil {
		return nil, fmt.Errorf("解析 JSON 响应失败: %w", err)
	}
	
	modified := false
	
	// 应用字段转换规则
	for _, fieldRule := range rule.Rules {
		if !rt.evaluateCondition(fieldRule.Condition, c) {
			continue
		}
		
		if rt.transformResponseField(&data, fieldRule) {
			modified = true
			result.Details = append(result.Details, fmt.Sprintf("转换响应字段: %s (%s)", fieldRule.Path, fieldRule.Action))
		}
	}
	
	// 应用响应包装器
	if rule.Wrapper != nil && rule.Wrapper.Enabled {
		data = rt.wrapResponse(data, rule.Wrapper, c.Writer.Status())
		modified = true
		result.Details = append(result.Details, "应用响应包装器")
	}
	
	if modified {
		return json.Marshal(data)
	}
	
	return nil, nil
}

// transformResponseField 转换响应字段
func (rt *ResponseTransformer) transformResponseField(data *interface{}, rule ResponseFieldRule) bool {
	switch rule.Action {
	case "add":
		return rt.addResponseField(data, rule.Path, rule.Value)
	case "remove":
		return rt.removeResponseField(data, rule.Path)
	case "rename":
		return rt.renameResponseField(data, rule.Path, rule.NewPath)
	case "replace":
		return rt.replaceResponseField(data, rule.Path, rule.Value)
	case "mask":
		return rt.maskResponseField(data, rule.Path, rule.MaskType, rule.MaskPattern)
	}
	
	return false
}

// maskResponseField 脱敏响应字段
func (rt *ResponseTransformer) maskResponseField(data *interface{}, path, maskType, maskPattern string) bool {
	if dataMap, ok := (*data).(map[string]interface{}); ok {
		if value, exists := dataMap[path]; exists {
			if strValue, ok := value.(string); ok {
				maskedValue := rt.maskSensitiveData(strValue, maskType, maskPattern)
				dataMap[path] = maskedValue
				return true
			}
		}
	}
	return false
}

// maskSensitiveData 脱敏敏感数据
func (rt *ResponseTransformer) maskSensitiveData(value, maskType, maskPattern string) string {
	switch maskType {
	case "phone":
		// 手机号脱敏：138****1234
		if len(value) == 11 {
			return value[:3] + "****" + value[7:]
		}
		return value
		
	case "email":
		// 邮箱脱敏：u***<EMAIL>
		parts := strings.Split(value, "@")
		if len(parts) == 2 && len(parts[0]) > 1 {
			masked := string(parts[0][0]) + "***"
			if len(parts[0]) > 1 {
				masked += string(parts[0][len(parts[0])-1])
			}
			return masked + "@" + parts[1]
		}
		return value
		
	case "id_card":
		// 身份证脱敏：110***********1234
		if len(value) == 18 {
			return value[:3] + "***********" + value[14:]
		}
		return value
		
	case "credit_card":
		// 信用卡脱敏：1234 **** **** 5678
		if len(value) >= 8 {
			return value[:4] + " **** **** " + value[len(value)-4:]
		}
		return value
		
	case "custom":
		// 自定义脱敏模式
		if maskPattern != "" {
			re, err := regexp.Compile(maskPattern)
			if err == nil {
				return re.ReplaceAllString(value, "***")
			}
		}
		return value
		
	default:
		// 默认脱敏：显示前后各2个字符
		if len(value) > 4 {
			return value[:2] + "***" + value[len(value)-2:]
		}
		return "***"
	}
}

// wrapResponse 包装响应
func (rt *ResponseTransformer) wrapResponse(data interface{}, wrapper *ResponseWrapper, statusCode int) map[string]interface{} {
	result := make(map[string]interface{})
	
	// 设置状态码
	if statusCode >= 200 && statusCode < 300 {
		result[wrapper.CodeField] = wrapper.SuccessCode
		result[wrapper.MessageField] = "success"
	} else {
		result[wrapper.CodeField] = wrapper.ErrorCode
		result[wrapper.MessageField] = "error"
	}
	
	// 设置数据
	result[wrapper.DataField] = data
	
	// 设置时间戳
	if wrapper.TimestampField != "" {
		result[wrapper.TimestampField] = time.Now().Format(time.RFC3339)
	}
	
	return result
}

// 响应字段操作函数（简化实现）
func (rt *ResponseTransformer) addResponseField(data *interface{}, path string, value interface{}) bool {
	if dataMap, ok := (*data).(map[string]interface{}); ok {
		dataMap[path] = value
		return true
	}
	return false
}

func (rt *ResponseTransformer) removeResponseField(data *interface{}, path string) bool {
	if dataMap, ok := (*data).(map[string]interface{}); ok {
		if _, exists := dataMap[path]; exists {
			delete(dataMap, path)
			return true
		}
	}
	return false
}

func (rt *ResponseTransformer) renameResponseField(data *interface{}, oldPath, newPath string) bool {
	if dataMap, ok := (*data).(map[string]interface{}); ok {
		if value, exists := dataMap[oldPath]; exists {
			delete(dataMap, oldPath)
			dataMap[newPath] = value
			return true
		}
	}
	return false
}

func (rt *ResponseTransformer) replaceResponseField(data *interface{}, path string, value interface{}) bool {
	if dataMap, ok := (*data).(map[string]interface{}); ok {
		if _, exists := dataMap[path]; exists {
			dataMap[path] = value
			return true
		}
	}
	return false
}

// getTransformRule 获取转换规则（复用请求转换器的逻辑）
func (rt *ResponseTransformer) getTransformRule(method, path string) *TransformRule {
	// 精确匹配
	key := fmt.Sprintf("%s:%s", method, path)
	if rule, exists := rt.rules[key]; exists {
		return rule
	}
	
	// 模式匹配
	for _, rule := range rt.rules {
		if rt.matchPath(rule.Path, path) && rule.Method == method {
			return rule
		}
	}
	
	return nil
}

// matchPath 路径匹配（复用请求转换器的逻辑）
func (rt *ResponseTransformer) matchPath(pattern, path string) bool {
	if pattern == "*" {
		return true
	}
	
	patternParts := strings.Split(pattern, "/")
	pathParts := strings.Split(path, "/")
	
	if len(patternParts) != len(pathParts) {
		return false
	}
	
	for i, part := range patternParts {
		if strings.HasPrefix(part, "{") && strings.HasSuffix(part, "}") {
			continue
		}
		if part != pathParts[i] {
			return false
		}
	}
	
	return true
}

// evaluateCondition 评估条件表达式（复用请求转换器的逻辑）
func (rt *ResponseTransformer) evaluateCondition(condition string, c *gin.Context) bool {
	if condition == "" {
		return true
	}
	
	switch condition {
	case "success":
		return c.Writer.Status() >= 200 && c.Writer.Status() < 300
	case "error":
		return c.Writer.Status() >= 400
	case "has_data":
		return c.Writer.Size() > 0
	default:
		return true
	}
}
