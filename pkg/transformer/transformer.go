package transformer

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"regexp"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"paas-platform/pkg/logger"
)

// RequestTransformer 请求转换器
type RequestTransformer struct {
	logger logger.Logger
	rules  map[string]*TransformRule
}

// ResponseTransformer 响应转换器
type ResponseTransformer struct {
	logger logger.Logger
	rules  map[string]*TransformRule
}

// TransformRule 转换规则
type TransformRule struct {
	Path           string                 `json:"path"`            // API 路径
	Method         string                 `json:"method"`          // HTTP 方法
	Enabled        bool                   `json:"enabled"`         // 是否启用
	RequestRules   *RequestTransformRule  `json:"request_rules"`   // 请求转换规则
	ResponseRules  *ResponseTransformRule `json:"response_rules"`  // 响应转换规则
	Description    string                 `json:"description"`     // 规则描述
}

// RequestTransformRule 请求转换规则
type RequestTransformRule struct {
	Headers    []HeaderRule    `json:"headers"`     // 请求头转换规则
	QueryParams []ParamRule    `json:"query_params"` // 查询参数转换规则
	PathParams  []ParamRule    `json:"path_params"`  // 路径参数转换规则
	Body        *BodyRule      `json:"body"`        // 请求体转换规则
}

// ResponseTransformRule 响应转换规则
type ResponseTransformRule struct {
	Headers     []HeaderRule     `json:"headers"`      // 响应头转换规则
	Body        *ResponseBodyRule `json:"body"`        // 响应体转换规则
	StatusCode  *StatusCodeRule  `json:"status_code"`  // 状态码转换规则
}

// HeaderRule 请求头转换规则
type HeaderRule struct {
	Action      string `json:"action"`       // 操作类型: add, remove, rename, replace
	Name        string `json:"name"`         // 头部名称
	NewName     string `json:"new_name"`     // 新名称（用于重命名）
	Value       string `json:"value"`        // 值（用于添加/替换）
	Pattern     string `json:"pattern"`      // 匹配模式（用于替换）
	Replacement string `json:"replacement"`  // 替换值
	Condition   string `json:"condition"`    // 条件表达式
}

// ParamRule 参数转换规则
type ParamRule struct {
	Action      string      `json:"action"`       // 操作类型: add, remove, rename, replace, convert
	Name        string      `json:"name"`         // 参数名称
	NewName     string      `json:"new_name"`     // 新名称（用于重命名）
	Value       interface{} `json:"value"`        // 值（用于添加/替换）
	Pattern     string      `json:"pattern"`      // 匹配模式（用于替换）
	Replacement string      `json:"replacement"`  // 替换值
	DataType    string      `json:"data_type"`    // 数据类型转换: string, int, float, bool
	Condition   string      `json:"condition"`    // 条件表达式
}

// BodyRule 请求体转换规则
type BodyRule struct {
	ContentType string           `json:"content_type"` // 内容类型
	Transform   string           `json:"transform"`    // 转换类型: json_transform, xml_transform, form_transform
	Rules       []BodyFieldRule  `json:"rules"`        // 字段转换规则
	Template    string           `json:"template"`     // 转换模板
}

// ResponseBodyRule 响应体转换规则
type ResponseBodyRule struct {
	ContentType string              `json:"content_type"` // 内容类型
	Transform   string              `json:"transform"`    // 转换类型
	Rules       []ResponseFieldRule `json:"rules"`        // 字段转换规则
	Template    string              `json:"template"`     // 转换模板
	Wrapper     *ResponseWrapper    `json:"wrapper"`      // 响应包装器
}

// BodyFieldRule 请求体字段转换规则
type BodyFieldRule struct {
	Action      string      `json:"action"`       // 操作类型: add, remove, rename, replace, convert
	Path        string      `json:"path"`         // 字段路径 (JSONPath)
	NewPath     string      `json:"new_path"`     // 新路径（用于重命名）
	Value       interface{} `json:"value"`        // 值（用于添加/替换）
	Pattern     string      `json:"pattern"`      // 匹配模式
	Replacement string      `json:"replacement"`  // 替换值
	DataType    string      `json:"data_type"`    // 数据类型转换
	Condition   string      `json:"condition"`    // 条件表达式
}

// ResponseFieldRule 响应体字段转换规则
type ResponseFieldRule struct {
	Action      string      `json:"action"`       // 操作类型
	Path        string      `json:"path"`         // 字段路径
	NewPath     string      `json:"new_path"`     // 新路径
	Value       interface{} `json:"value"`        // 值
	Pattern     string      `json:"pattern"`      // 匹配模式
	Replacement string      `json:"replacement"`  // 替换值
	DataType    string      `json:"data_type"`    // 数据类型转换
	Condition   string      `json:"condition"`    // 条件表达式
	Sensitive   bool        `json:"sensitive"`    // 是否敏感数据（需要脱敏）
	MaskType    string      `json:"mask_type"`    // 脱敏类型: phone, email, id_card, credit_card, custom
	MaskPattern string      `json:"mask_pattern"` // 自定义脱敏模式
}

// StatusCodeRule 状态码转换规则
type StatusCodeRule struct {
	From      int    `json:"from"`      // 原状态码
	To        int    `json:"to"`        // 目标状态码
	Condition string `json:"condition"` // 条件表达式
}

// ResponseWrapper 响应包装器
type ResponseWrapper struct {
	Enabled     bool   `json:"enabled"`      // 是否启用包装
	CodeField   string `json:"code_field"`   // 状态码字段名
	MessageField string `json:"message_field"` // 消息字段名
	DataField   string `json:"data_field"`   // 数据字段名
	TimestampField string `json:"timestamp_field"` // 时间戳字段名
	SuccessCode int    `json:"success_code"` // 成功状态码
	ErrorCode   int    `json:"error_code"`   // 错误状态码
}

// TransformResult 转换结果
type TransformResult struct {
	Success   bool        `json:"success"`
	Error     error       `json:"error"`
	Modified  bool        `json:"modified"`
	Details   []string    `json:"details"`
	Metadata  map[string]interface{} `json:"metadata"`
}

// NewRequestTransformer 创建请求转换器
func NewRequestTransformer(logger logger.Logger) *RequestTransformer {
	return &RequestTransformer{
		logger: logger,
		rules:  make(map[string]*TransformRule),
	}
}

// NewResponseTransformer 创建响应转换器
func NewResponseTransformer(logger logger.Logger) *ResponseTransformer {
	return &ResponseTransformer{
		logger: logger,
		rules:  make(map[string]*TransformRule),
	}
}

// RegisterRule 注册转换规则
func (rt *RequestTransformer) RegisterRule(rule *TransformRule) error {
	if rule.Path == "" || rule.Method == "" {
		return fmt.Errorf("路径和方法不能为空")
	}
	
	key := fmt.Sprintf("%s:%s", rule.Method, rule.Path)
	rt.rules[key] = rule
	
	rt.logger.Info("注册请求转换规则",
		"method", rule.Method,
		"path", rule.Path,
		"enabled", rule.Enabled)
	
	return nil
}

// RegisterRule 注册转换规则
func (rt *ResponseTransformer) RegisterRule(rule *TransformRule) error {
	if rule.Path == "" || rule.Method == "" {
		return fmt.Errorf("路径和方法不能为空")
	}
	
	key := fmt.Sprintf("%s:%s", rule.Method, rule.Path)
	rt.rules[key] = rule
	
	rt.logger.Info("注册响应转换规则",
		"method", rule.Method,
		"path", rule.Path,
		"enabled", rule.Enabled)
	
	return nil
}

// TransformRequest 转换请求
func (rt *RequestTransformer) TransformRequest(c *gin.Context) *TransformResult {
	rule := rt.getTransformRule(c.Request.Method, c.Request.URL.Path)
	if rule == nil || !rule.Enabled || rule.RequestRules == nil {
		return &TransformResult{Success: true, Modified: false}
	}
	
	result := &TransformResult{
		Success:  true,
		Modified: false,
		Details:  make([]string, 0),
		Metadata: make(map[string]interface{}),
	}
	
	rt.logger.Debug("开始转换请求",
		"method", c.Request.Method,
		"path", c.Request.URL.Path)
	
	// 转换请求头
	if err := rt.transformHeaders(c, rule.RequestRules.Headers, result); err != nil {
		result.Success = false
		result.Error = err
		return result
	}
	
	// 转换查询参数
	if err := rt.transformQueryParams(c, rule.RequestRules.QueryParams, result); err != nil {
		result.Success = false
		result.Error = err
		return result
	}
	
	// 转换路径参数
	if err := rt.transformPathParams(c, rule.RequestRules.PathParams, result); err != nil {
		result.Success = false
		result.Error = err
		return result
	}
	
	// 转换请求体
	if rule.RequestRules.Body != nil {
		if err := rt.transformRequestBody(c, rule.RequestRules.Body, result); err != nil {
			result.Success = false
			result.Error = err
			return result
		}
	}
	
	if result.Modified {
		rt.logger.Info("请求转换完成",
			"method", c.Request.Method,
			"path", c.Request.URL.Path,
			"details", result.Details)
	}
	
	return result
}

// getTransformRule 获取转换规则
func (rt *RequestTransformer) getTransformRule(method, path string) *TransformRule {
	// 精确匹配
	key := fmt.Sprintf("%s:%s", method, path)
	if rule, exists := rt.rules[key]; exists {
		return rule
	}
	
	// 模式匹配
	for _, rule := range rt.rules {
		if rt.matchPath(rule.Path, path) && rule.Method == method {
			return rule
		}
	}
	
	return nil
}

// matchPath 路径匹配
func (rt *RequestTransformer) matchPath(pattern, path string) bool {
	// 支持通配符匹配
	if pattern == "*" {
		return true
	}
	
	// 支持路径参数匹配 /api/v1/apps/{id}
	patternParts := strings.Split(pattern, "/")
	pathParts := strings.Split(path, "/")
	
	if len(patternParts) != len(pathParts) {
		return false
	}
	
	for i, part := range patternParts {
		if strings.HasPrefix(part, "{") && strings.HasSuffix(part, "}") {
			// 路径参数，跳过
			continue
		}
		if part != pathParts[i] {
			return false
		}
	}
	
	return true
}

// transformHeaders 转换请求头
func (rt *RequestTransformer) transformHeaders(c *gin.Context, rules []HeaderRule, result *TransformResult) error {
	for _, rule := range rules {
		if !rt.evaluateCondition(rule.Condition, c) {
			continue
		}

		switch rule.Action {
		case "add":
			c.Request.Header.Add(rule.Name, rule.Value)
			result.Modified = true
			result.Details = append(result.Details, fmt.Sprintf("添加请求头: %s=%s", rule.Name, rule.Value))

		case "remove":
			c.Request.Header.Del(rule.Name)
			result.Modified = true
			result.Details = append(result.Details, fmt.Sprintf("删除请求头: %s", rule.Name))

		case "rename":
			if value := c.Request.Header.Get(rule.Name); value != "" {
				c.Request.Header.Del(rule.Name)
				c.Request.Header.Set(rule.NewName, value)
				result.Modified = true
				result.Details = append(result.Details, fmt.Sprintf("重命名请求头: %s -> %s", rule.Name, rule.NewName))
			}

		case "replace":
			if rule.Pattern != "" {
				// 正则替换
				if value := c.Request.Header.Get(rule.Name); value != "" {
					re, err := regexp.Compile(rule.Pattern)
					if err != nil {
						return fmt.Errorf("正则表达式错误: %w", err)
					}
					newValue := re.ReplaceAllString(value, rule.Replacement)
					c.Request.Header.Set(rule.Name, newValue)
					result.Modified = true
					result.Details = append(result.Details, fmt.Sprintf("替换请求头: %s=%s", rule.Name, newValue))
				}
			} else {
				// 直接替换
				c.Request.Header.Set(rule.Name, rule.Value)
				result.Modified = true
				result.Details = append(result.Details, fmt.Sprintf("替换请求头: %s=%s", rule.Name, rule.Value))
			}
		}
	}

	return nil
}

// transformQueryParams 转换查询参数
func (rt *RequestTransformer) transformQueryParams(c *gin.Context, rules []ParamRule, result *TransformResult) error {
	query := c.Request.URL.Query()
	modified := false

	for _, rule := range rules {
		if !rt.evaluateCondition(rule.Condition, c) {
			continue
		}

		switch rule.Action {
		case "add":
			valueStr := rt.convertToString(rule.Value)
			query.Add(rule.Name, valueStr)
			modified = true
			result.Details = append(result.Details, fmt.Sprintf("添加查询参数: %s=%s", rule.Name, valueStr))

		case "remove":
			query.Del(rule.Name)
			modified = true
			result.Details = append(result.Details, fmt.Sprintf("删除查询参数: %s", rule.Name))

		case "rename":
			if values := query[rule.Name]; len(values) > 0 {
				query.Del(rule.Name)
				for _, value := range values {
					query.Add(rule.NewName, value)
				}
				modified = true
				result.Details = append(result.Details, fmt.Sprintf("重命名查询参数: %s -> %s", rule.Name, rule.NewName))
			}

		case "replace":
			if values := query[rule.Name]; len(values) > 0 {
				query.Del(rule.Name)
				if rule.Pattern != "" {
					// 正则替换
					re, err := regexp.Compile(rule.Pattern)
					if err != nil {
						return fmt.Errorf("正则表达式错误: %w", err)
					}
					for _, value := range values {
						newValue := re.ReplaceAllString(value, rule.Replacement)
						query.Add(rule.Name, newValue)
					}
				} else {
					// 直接替换
					valueStr := rt.convertToString(rule.Value)
					query.Add(rule.Name, valueStr)
				}
				modified = true
				result.Details = append(result.Details, fmt.Sprintf("替换查询参数: %s", rule.Name))
			}

		case "convert":
			if values := query[rule.Name]; len(values) > 0 {
				query.Del(rule.Name)
				for _, value := range values {
					convertedValue, err := rt.convertDataType(value, rule.DataType)
					if err != nil {
						return fmt.Errorf("数据类型转换错误: %w", err)
					}
					query.Add(rule.Name, rt.convertToString(convertedValue))
				}
				modified = true
				result.Details = append(result.Details, fmt.Sprintf("转换查询参数类型: %s -> %s", rule.Name, rule.DataType))
			}
		}
	}

	if modified {
		c.Request.URL.RawQuery = query.Encode()
		result.Modified = true
	}

	return nil
}

// transformPathParams 转换路径参数
func (rt *RequestTransformer) transformPathParams(c *gin.Context, rules []ParamRule, result *TransformResult) error {
	for _, rule := range rules {
		if !rt.evaluateCondition(rule.Condition, c) {
			continue
		}

		switch rule.Action {
		case "add":
			valueStr := rt.convertToString(rule.Value)
			c.Params = append(c.Params, gin.Param{Key: rule.Name, Value: valueStr})
			result.Modified = true
			result.Details = append(result.Details, fmt.Sprintf("添加路径参数: %s=%s", rule.Name, valueStr))

		case "remove":
			for i, param := range c.Params {
				if param.Key == rule.Name {
					c.Params = append(c.Params[:i], c.Params[i+1:]...)
					result.Modified = true
					result.Details = append(result.Details, fmt.Sprintf("删除路径参数: %s", rule.Name))
					break
				}
			}

		case "rename":
			for i, param := range c.Params {
				if param.Key == rule.Name {
					c.Params[i].Key = rule.NewName
					result.Modified = true
					result.Details = append(result.Details, fmt.Sprintf("重命名路径参数: %s -> %s", rule.Name, rule.NewName))
					break
				}
			}

		case "replace":
			for i, param := range c.Params {
				if param.Key == rule.Name {
					if rule.Pattern != "" {
						// 正则替换
						re, err := regexp.Compile(rule.Pattern)
						if err != nil {
							return fmt.Errorf("正则表达式错误: %w", err)
						}
						c.Params[i].Value = re.ReplaceAllString(param.Value, rule.Replacement)
					} else {
						// 直接替换
						c.Params[i].Value = rt.convertToString(rule.Value)
					}
					result.Modified = true
					result.Details = append(result.Details, fmt.Sprintf("替换路径参数: %s=%s", rule.Name, c.Params[i].Value))
					break
				}
			}

		case "convert":
			for i, param := range c.Params {
				if param.Key == rule.Name {
					convertedValue, err := rt.convertDataType(param.Value, rule.DataType)
					if err != nil {
						return fmt.Errorf("数据类型转换错误: %w", err)
					}
					c.Params[i].Value = rt.convertToString(convertedValue)
					result.Modified = true
					result.Details = append(result.Details, fmt.Sprintf("转换路径参数类型: %s -> %s", rule.Name, rule.DataType))
					break
				}
			}
		}
	}

	return nil
}

// transformRequestBody 转换请求体
func (rt *RequestTransformer) transformRequestBody(c *gin.Context, rule *BodyRule, result *TransformResult) error {
	// 读取请求体
	bodyBytes, err := io.ReadAll(c.Request.Body)
	if err != nil {
		return fmt.Errorf("读取请求体失败: %w", err)
	}

	// 重置请求体，以便后续处理
	c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	if len(bodyBytes) == 0 {
		return nil
	}

	switch rule.Transform {
	case "json_transform":
		return rt.transformJSONBody(bodyBytes, rule.Rules, c, result)
	case "form_transform":
		return rt.transformFormBody(bodyBytes, rule.Rules, c, result)
	default:
		rt.logger.Warn("不支持的请求体转换类型", "transform", rule.Transform)
	}

	return nil
}

// transformJSONBody 转换 JSON 请求体
func (rt *RequestTransformer) transformJSONBody(bodyBytes []byte, rules []BodyFieldRule, c *gin.Context, result *TransformResult) error {
	var data interface{}
	if err := json.Unmarshal(bodyBytes, &data); err != nil {
		return fmt.Errorf("解析 JSON 请求体失败: %w", err)
	}

	modified := false
	for _, rule := range rules {
		if !rt.evaluateCondition(rule.Condition, c) {
			continue
		}

		if rt.transformJSONField(&data, rule) {
			modified = true
			result.Details = append(result.Details, fmt.Sprintf("转换请求体字段: %s (%s)", rule.Path, rule.Action))
		}
	}

	if modified {
		// 重新序列化并设置请求体
		newBodyBytes, err := json.Marshal(data)
		if err != nil {
			return fmt.Errorf("序列化 JSON 请求体失败: %w", err)
		}

		c.Request.Body = io.NopCloser(bytes.NewBuffer(newBodyBytes))
		c.Request.ContentLength = int64(len(newBodyBytes))
		result.Modified = true
	}

	return nil
}

// transformJSONField 转换 JSON 字段
func (rt *RequestTransformer) transformJSONField(data *interface{}, rule BodyFieldRule) bool {
	// 简化的 JSONPath 实现
	// 实际项目中建议使用专业的 JSONPath 库

	switch rule.Action {
	case "add":
		return rt.addJSONField(data, rule.Path, rule.Value)
	case "remove":
		return rt.removeJSONField(data, rule.Path)
	case "rename":
		return rt.renameJSONField(data, rule.Path, rule.NewPath)
	case "replace":
		return rt.replaceJSONField(data, rule.Path, rule.Value)
	case "convert":
		return rt.convertJSONField(data, rule.Path, rule.DataType)
	}

	return false
}

// evaluateCondition 评估条件表达式
func (rt *RequestTransformer) evaluateCondition(condition string, c *gin.Context) bool {
	if condition == "" {
		return true
	}

	// 简化的条件评估实现
	// 实际项目中建议使用表达式引擎

	switch condition {
	case "has_auth":
		return c.GetHeader("Authorization") != ""
	case "is_json":
		return strings.Contains(c.GetHeader("Content-Type"), "application/json")
	case "is_form":
		return strings.Contains(c.GetHeader("Content-Type"), "application/x-www-form-urlencoded")
	default:
		return true
	}
}

// convertToString 转换为字符串
func (rt *RequestTransformer) convertToString(value interface{}) string {
	switch v := value.(type) {
	case string:
		return v
	case int:
		return strconv.Itoa(v)
	case int64:
		return strconv.FormatInt(v, 10)
	case float64:
		return strconv.FormatFloat(v, 'f', -1, 64)
	case bool:
		return strconv.FormatBool(v)
	default:
		return fmt.Sprintf("%v", v)
	}
}

// convertDataType 转换数据类型
func (rt *RequestTransformer) convertDataType(value string, dataType string) (interface{}, error) {
	switch dataType {
	case "string":
		return value, nil
	case "int":
		return strconv.Atoi(value)
	case "float":
		return strconv.ParseFloat(value, 64)
	case "bool":
		return strconv.ParseBool(value)
	default:
		return value, nil
	}
}

// 简化的 JSON 字段操作函数
func (rt *RequestTransformer) addJSONField(data *interface{}, path string, value interface{}) bool {
	// 简化实现：只支持顶级字段
	if dataMap, ok := (*data).(map[string]interface{}); ok {
		dataMap[path] = value
		return true
	}
	return false
}

func (rt *RequestTransformer) removeJSONField(data *interface{}, path string) bool {
	if dataMap, ok := (*data).(map[string]interface{}); ok {
		if _, exists := dataMap[path]; exists {
			delete(dataMap, path)
			return true
		}
	}
	return false
}

func (rt *RequestTransformer) renameJSONField(data *interface{}, oldPath, newPath string) bool {
	if dataMap, ok := (*data).(map[string]interface{}); ok {
		if value, exists := dataMap[oldPath]; exists {
			delete(dataMap, oldPath)
			dataMap[newPath] = value
			return true
		}
	}
	return false
}

func (rt *RequestTransformer) replaceJSONField(data *interface{}, path string, value interface{}) bool {
	if dataMap, ok := (*data).(map[string]interface{}); ok {
		if _, exists := dataMap[path]; exists {
			dataMap[path] = value
			return true
		}
	}
	return false
}

func (rt *RequestTransformer) convertJSONField(data *interface{}, path, dataType string) bool {
	if dataMap, ok := (*data).(map[string]interface{}); ok {
		if value, exists := dataMap[path]; exists {
			if strValue, ok := value.(string); ok {
				if convertedValue, err := rt.convertDataType(strValue, dataType); err == nil {
					dataMap[path] = convertedValue
					return true
				}
			}
		}
	}
	return false
}

// transformFormBody 转换表单请求体
func (rt *RequestTransformer) transformFormBody(bodyBytes []byte, rules []BodyFieldRule, c *gin.Context, result *TransformResult) error {
	// 简化实现：解析表单数据并应用转换规则
	// 实际项目中需要更完整的表单处理
	rt.logger.Debug("表单请求体转换暂未完全实现")
	return nil
}
