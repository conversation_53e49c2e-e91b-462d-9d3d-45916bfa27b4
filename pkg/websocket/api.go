package websocket

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"paas-platform/pkg/logger"
)

// APIHandler WebSocket API 处理器
type APIHandler struct {
	gateway *WebSocketGateway
	logger  logger.Logger
}

// NewAPIHandler 创建 API 处理器
func NewAPIHandler(gateway *WebSocketGateway, logger logger.Logger) *APIHandler {
	return &APIHandler{
		gateway: gateway,
		logger:  logger,
	}
}

// RegisterRoutes 注册路由
func (h *APIHandler) RegisterRoutes(router *gin.RouterGroup) {
	ws := router.Group("/websocket")
	{
		// 统计信息
		ws.GET("/stats", h.GetStats)
		
		// 连接管理
		ws.GET("/connections", h.GetConnections)
		ws.GET("/connections/:id", h.GetConnection)
		ws.DELETE("/connections/:id", h.CloseConnection)
		ws.DELETE("/connections", h.CloseAllConnections)
		
		// 房间管理
		ws.GET("/rooms", h.GetRooms)
		ws.POST("/rooms", h.CreateRoom)
		ws.GET("/rooms/:id", h.GetRoom)
		ws.DELETE("/rooms/:id", h.DeleteRoom)
		ws.POST("/rooms/:id/join", h.JoinRoom)
		ws.POST("/rooms/:id/leave", h.LeaveRoom)
		
		// 消息发送
		ws.POST("/broadcast", h.BroadcastMessage)
		ws.POST("/send", h.SendMessage)
		ws.POST("/rooms/:id/send", h.SendRoomMessage)
		ws.POST("/users/:id/send", h.SendUserMessage)
		
		// 用户管理
		ws.GET("/users/online", h.GetOnlineUsers)
		ws.GET("/users/:id/online", h.CheckUserOnline)
		ws.GET("/users/:id/connections", h.GetUserConnections)
	}
}

// GetStats 获取统计信息
func (h *APIHandler) GetStats(c *gin.Context) {
	stats := h.gateway.GetStats()
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    stats,
	})
}

// GetConnections 获取所有连接
func (h *APIHandler) GetConnections(c *gin.Context) {
	connections := h.gateway.GetConnections()
	
	// 转换为 API 响应格式
	var result []gin.H
	for _, conn := range connections {
		result = append(result, gin.H{
			"id":           conn.ID,
			"user_id":      conn.UserID,
			"session_id":   conn.SessionID,
			"connected_at": conn.ConnectedAt,
			"last_ping":    conn.LastPing,
			"is_alive":     conn.IsAlive,
			"remote_addr":  conn.RemoteAddr,
			"user_agent":   conn.UserAgent,
			"rooms":        conn.Rooms,
			"metadata":     conn.Metadata,
		})
	}
	
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    result,
		"total":   len(result),
	})
}

// GetConnection 获取指定连接
func (h *APIHandler) GetConnection(c *gin.Context) {
	id := c.Param("id")
	conn, exists := h.gateway.GetConnection(id)
	if !exists {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    -1,
			"message": "连接不存在",
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data": gin.H{
			"id":           conn.ID,
			"user_id":      conn.UserID,
			"session_id":   conn.SessionID,
			"connected_at": conn.ConnectedAt,
			"last_ping":    conn.LastPing,
			"is_alive":     conn.IsAlive,
			"remote_addr":  conn.RemoteAddr,
			"user_agent":   conn.UserAgent,
			"rooms":        conn.Rooms,
			"metadata":     conn.Metadata,
		},
	})
}

// CloseConnection 关闭连接
func (h *APIHandler) CloseConnection(c *gin.Context) {
	id := c.Param("id")
	if err := h.gateway.CloseConnection(id); err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    -1,
			"message": err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "连接已关闭",
	})
}

// CloseAllConnections 关闭所有连接
func (h *APIHandler) CloseAllConnections(c *gin.Context) {
	h.gateway.CloseAllConnections()
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "所有连接已关闭",
	})
}

// GetRooms 获取所有房间
func (h *APIHandler) GetRooms(c *gin.Context) {
	rooms := h.gateway.GetRooms()
	
	var result []gin.H
	for _, room := range rooms {
		result = append(result, gin.H{
			"id":          room.ID,
			"name":        room.Name,
			"description": room.Description,
			"created_at":  room.CreatedAt,
			"max_size":    room.MaxSize,
			"is_private":  room.IsPrivate,
			"owner":       room.Owner,
			"size":        len(room.Connections),
			"metadata":    room.Metadata,
		})
	}
	
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    result,
		"total":   len(result),
	})
}

// CreateRoom 创建房间
func (h *APIHandler) CreateRoom(c *gin.Context) {
	var req struct {
		ID          string                 `json:"id"`
		Name        string                 `json:"name" binding:"required"`
		Description string                 `json:"description"`
		MaxSize     int                    `json:"max_size"`
		IsPrivate   bool                   `json:"is_private"`
		Owner       string                 `json:"owner"`
		Metadata    map[string]interface{} `json:"metadata"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    -1,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}
	
	// 生成房间ID
	if req.ID == "" {
		req.ID = uuid.New().String()
	}
	
	if err := h.gateway.CreateRoom(req.ID, req.Name, req.Description, req.MaxSize, req.IsPrivate, req.Owner); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    -1,
			"message": err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusCreated, gin.H{
		"code":    0,
		"message": "房间创建成功",
		"data": gin.H{
			"id": req.ID,
		},
	})
}

// GetRoom 获取指定房间
func (h *APIHandler) GetRoom(c *gin.Context) {
	id := c.Param("id")
	room, exists := h.gateway.GetRoom(id)
	if !exists {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    -1,
			"message": "房间不存在",
		})
		return
	}
	
	// 获取房间内的连接信息
	var connections []gin.H
	for _, conn := range room.Connections {
		connections = append(connections, gin.H{
			"id":      conn.ID,
			"user_id": conn.UserID,
			"is_alive": conn.IsAlive,
		})
	}
	
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data": gin.H{
			"id":          room.ID,
			"name":        room.Name,
			"description": room.Description,
			"created_at":  room.CreatedAt,
			"max_size":    room.MaxSize,
			"is_private":  room.IsPrivate,
			"owner":       room.Owner,
			"size":        len(room.Connections),
			"connections": connections,
			"metadata":    room.Metadata,
		},
	})
}

// DeleteRoom 删除房间
func (h *APIHandler) DeleteRoom(c *gin.Context) {
	id := c.Param("id")
	if err := h.gateway.DeleteRoom(id); err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    -1,
			"message": err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "房间已删除",
	})
}

// JoinRoom 加入房间
func (h *APIHandler) JoinRoom(c *gin.Context) {
	roomID := c.Param("id")
	var req struct {
		ConnectionID string `json:"connection_id" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    -1,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}
	
	if err := h.gateway.JoinRoom(req.ConnectionID, roomID); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    -1,
			"message": err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "成功加入房间",
	})
}

// LeaveRoom 离开房间
func (h *APIHandler) LeaveRoom(c *gin.Context) {
	roomID := c.Param("id")
	var req struct {
		ConnectionID string `json:"connection_id" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    -1,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}
	
	if err := h.gateway.LeaveRoom(req.ConnectionID, roomID); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    -1,
			"message": err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "成功离开房间",
	})
}

// BroadcastMessage 广播消息
func (h *APIHandler) BroadcastMessage(c *gin.Context) {
	var req struct {
		Type     string      `json:"type" binding:"required"`
		Data     interface{} `json:"data"`
		Metadata map[string]interface{} `json:"metadata"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    -1,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}
	
	message := &Message{
		ID:        uuid.New().String(),
		Type:      req.Type,
		From:      "api",
		Data:      req.Data,
		Metadata:  req.Metadata,
		Timestamp: time.Now(),
	}
	
	if err := h.gateway.BroadcastMessage(message); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    -1,
			"message": err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "消息已广播",
		"data": gin.H{
			"message_id": message.ID,
		},
	})
}

// SendMessage 发送消息
func (h *APIHandler) SendMessage(c *gin.Context) {
	var req struct {
		ConnectionID string      `json:"connection_id" binding:"required"`
		Type         string      `json:"type" binding:"required"`
		Data         interface{} `json:"data"`
		Metadata     map[string]interface{} `json:"metadata"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    -1,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}
	
	message := &Message{
		ID:        uuid.New().String(),
		Type:      req.Type,
		From:      "api",
		To:        req.ConnectionID,
		Data:      req.Data,
		Metadata:  req.Metadata,
		Timestamp: time.Now(),
	}
	
	if err := h.gateway.SendMessage(req.ConnectionID, message); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    -1,
			"message": err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "消息已发送",
		"data": gin.H{
			"message_id": message.ID,
		},
	})
}

// SendRoomMessage 发送房间消息
func (h *APIHandler) SendRoomMessage(c *gin.Context) {
	roomID := c.Param("id")
	var req struct {
		Type     string      `json:"type" binding:"required"`
		Data     interface{} `json:"data"`
		Metadata map[string]interface{} `json:"metadata"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    -1,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}
	
	message := &Message{
		ID:        uuid.New().String(),
		Type:      req.Type,
		From:      "api",
		Room:      roomID,
		Data:      req.Data,
		Metadata:  req.Metadata,
		Timestamp: time.Now(),
	}
	
	if err := h.gateway.SendRoomMessage(roomID, message); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    -1,
			"message": err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "房间消息已发送",
		"data": gin.H{
			"message_id": message.ID,
		},
	})
}

// SendUserMessage 发送用户消息
func (h *APIHandler) SendUserMessage(c *gin.Context) {
	userID := c.Param("id")
	var req struct {
		Type     string      `json:"type" binding:"required"`
		Data     interface{} `json:"data"`
		Metadata map[string]interface{} `json:"metadata"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    -1,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}
	
	message := &Message{
		ID:        uuid.New().String(),
		Type:      req.Type,
		From:      "api",
		To:        userID,
		Data:      req.Data,
		Metadata:  req.Metadata,
		Timestamp: time.Now(),
	}
	
	if err := h.gateway.SendMessageToUser(userID, message); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    -1,
			"message": err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "用户消息已发送",
		"data": gin.H{
			"message_id": message.ID,
		},
	})
}

// GetOnlineUsers 获取在线用户
func (h *APIHandler) GetOnlineUsers(c *gin.Context) {
	users := h.gateway.GetOnlineUsers()
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    users,
		"total":   len(users),
	})
}

// CheckUserOnline 检查用户是否在线
func (h *APIHandler) CheckUserOnline(c *gin.Context) {
	userID := c.Param("id")
	online := h.gateway.IsUserOnline(userID)
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data": gin.H{
			"user_id": userID,
			"online":  online,
		},
	})
}

// GetUserConnections 获取用户连接
func (h *APIHandler) GetUserConnections(c *gin.Context) {
	userID := c.Param("id")
	connections := h.gateway.GetConnectionsByUser(userID)
	
	var result []gin.H
	for _, conn := range connections {
		result = append(result, gin.H{
			"id":           conn.ID,
			"session_id":   conn.SessionID,
			"connected_at": conn.ConnectedAt,
			"last_ping":    conn.LastPing,
			"is_alive":     conn.IsAlive,
			"remote_addr":  conn.RemoteAddr,
			"rooms":        conn.Rooms,
		})
	}
	
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    result,
		"total":   len(result),
	})
}
