package websocket

import (
	"encoding/json"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"paas-platform/pkg/logger"
)

func TestWebSocketGateway_Creation(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	
	gateway := NewWebSocketGateway(config, logger)
	
	assert.NotNil(t, gateway)
	assert.Equal(t, config.ReadBufferSize, gateway.config.ReadBufferSize)
	assert.Equal(t, config.WriteBufferSize, gateway.config.WriteBufferSize)
	assert.NotNil(t, gateway.connections)
	assert.NotNil(t, gateway.rooms)
	assert.NotNil(t, gateway.stats)
}

func TestWebSocketGateway_Stats(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	gateway := NewWebSocketGateway(config, logger)
	
	stats := gateway.GetStats()
	assert.NotNil(t, stats)
	assert.Equal(t, int64(0), stats.ActiveConnections)
	assert.Equal(t, int64(0), stats.ActiveRooms)
	assert.Equal(t, int64(0), stats.TotalMessages)
}

func TestWebSocketGateway_RoomManagement(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	gateway := NewWebSocketGateway(config, logger)
	
	// 测试创建房间
	err := gateway.CreateRoom("test-room", "测试房间", "这是一个测试房间", 10, false, "admin")
	require.NoError(t, err)
	
	// 验证房间已创建
	room, exists := gateway.GetRoom("test-room")
	assert.True(t, exists)
	assert.Equal(t, "test-room", room.ID)
	assert.Equal(t, "测试房间", room.Name)
	assert.Equal(t, "这是一个测试房间", room.Description)
	assert.Equal(t, 10, room.MaxSize)
	assert.False(t, room.IsPrivate)
	assert.Equal(t, "admin", room.Owner)
	
	// 测试获取所有房间
	rooms := gateway.GetRooms()
	assert.Len(t, rooms, 1)
	assert.Equal(t, "test-room", rooms[0].ID)
	
	// 测试重复创建房间
	err = gateway.CreateRoom("test-room", "重复房间", "", 5, true, "user")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "房间已存在")
	
	// 测试删除房间
	err = gateway.DeleteRoom("test-room")
	require.NoError(t, err)
	
	// 验证房间已删除
	_, exists = gateway.GetRoom("test-room")
	assert.False(t, exists)
	
	rooms = gateway.GetRooms()
	assert.Len(t, rooms, 0)
}

func TestWebSocketGateway_MessageBroadcast(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	gateway := NewWebSocketGateway(config, logger)
	
	// 创建测试消息
	message := &Message{
		ID:        "test-message-1",
		Type:      "test",
		From:      "server",
		Data:      "Hello, World!",
		Timestamp: time.Now(),
	}
	
	// 测试广播消息（没有连接时）
	err := gateway.BroadcastMessage(message)
	assert.NoError(t, err)
	
	// 验证统计信息
	stats := gateway.GetStats()
	assert.Equal(t, int64(1), stats.TotalMessages)
	assert.Equal(t, int64(0), stats.MessagesSent) // 没有连接，所以发送数为0
}

func TestWebSocketGateway_UserManagement(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	gateway := NewWebSocketGateway(config, logger)
	
	// 测试获取在线用户（空列表）
	users := gateway.GetOnlineUsers()
	assert.Len(t, users, 0)
	
	// 测试检查用户是否在线
	online := gateway.IsUserOnline("user123")
	assert.False(t, online)
	
	// 测试获取用户连接
	connections := gateway.GetConnectionsByUser("user123")
	assert.Len(t, connections, 0)
}

func TestWebSocketGateway_ConnectionLimits(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	config.MaxConnections = 2 // 设置最大连接数为2
	gateway := NewWebSocketGateway(config, logger)
	
	// 验证连接数限制
	assert.Equal(t, 2, gateway.config.MaxConnections)
	
	// 测试获取活跃连接数
	count := gateway.getActiveConnectionCount()
	assert.Equal(t, int64(0), count)
}

func TestWebSocketGateway_RoomLimits(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	config.MaxRooms = 2
	config.MaxRoomSize = 3
	gateway := NewWebSocketGateway(config, logger)
	
	// 创建房间直到达到限制
	err := gateway.CreateRoom("room1", "房间1", "", 0, false, "admin")
	require.NoError(t, err)
	
	err = gateway.CreateRoom("room2", "房间2", "", 0, false, "admin")
	require.NoError(t, err)
	
	// 尝试创建第三个房间，应该失败
	err = gateway.CreateRoom("room3", "房间3", "", 0, false, "admin")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "房间数量已达上限")
}

func TestMessage_Serialization(t *testing.T) {
	message := &Message{
		ID:        "test-msg-1",
		Type:      "chat",
		From:      "user1",
		To:        "user2",
		Room:      "room1",
		Data:      "Hello, World!",
		Metadata:  map[string]interface{}{"priority": "high"},
		Timestamp: time.Now(),
	}
	
	// 测试序列化
	data, err := json.Marshal(message)
	require.NoError(t, err)
	assert.Contains(t, string(data), "test-msg-1")
	assert.Contains(t, string(data), "chat")
	assert.Contains(t, string(data), "Hello, World!")
	
	// 测试反序列化
	var decoded Message
	err = json.Unmarshal(data, &decoded)
	require.NoError(t, err)
	assert.Equal(t, message.ID, decoded.ID)
	assert.Equal(t, message.Type, decoded.Type)
	assert.Equal(t, message.From, decoded.From)
	assert.Equal(t, message.To, decoded.To)
	assert.Equal(t, message.Room, decoded.Room)
	assert.Equal(t, message.Data, decoded.Data)
}

func TestConnection_MessageHandling(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	gateway := NewWebSocketGateway(config, logger)
	
	// 创建模拟连接
	conn := &Connection{
		ID:          "test-conn-1",
		UserID:      "user123",
		Gateway:     gateway,
		Send:        make(chan []byte, 256),
		Receive:     make(chan []byte, 256),
		Metadata:    make(map[string]interface{}),
		ConnectedAt: time.Now(),
		LastPing:    time.Now(),
		IsAlive:     true,
		Rooms:       make(map[string]bool),
		RemoteAddr:  "127.0.0.1",
		UserAgent:   "test-agent",
	}
	
	// 测试 Ping 消息处理
	pingMessage := &Message{
		ID:   "ping-1",
		Type: "ping",
		Data: "ping-data",
	}
	
	conn.handlePing(pingMessage)
	
	// 检查是否有 pong 响应
	select {
	case response := <-conn.Send:
		var msg Message
		err := json.Unmarshal(response, &msg)
		require.NoError(t, err)
		assert.Equal(t, "pong", msg.Type)
		assert.Equal(t, "server", msg.From)
		assert.Equal(t, conn.ID, msg.To)
	case <-time.After(100 * time.Millisecond):
		t.Fatal("没有收到 pong 响应")
	}
}

func TestConnection_ErrorHandling(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	gateway := NewWebSocketGateway(config, logger)
	
	conn := &Connection{
		ID:      "test-conn-error",
		Gateway: gateway,
		Send:    make(chan []byte, 256),
		IsAlive: true,
	}
	
	// 测试发送错误消息
	conn.sendError("测试错误消息")
	
	select {
	case response := <-conn.Send:
		var msg Message
		err := json.Unmarshal(response, &msg)
		require.NoError(t, err)
		assert.Equal(t, "error", msg.Type)
		assert.Equal(t, "server", msg.From)
		assert.Equal(t, "测试错误消息", msg.Data)
	case <-time.After(100 * time.Millisecond):
		t.Fatal("没有收到错误响应")
	}
}

func TestConnection_SuccessHandling(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	gateway := NewWebSocketGateway(config, logger)
	
	conn := &Connection{
		ID:      "test-conn-success",
		Gateway: gateway,
		Send:    make(chan []byte, 256),
		IsAlive: true,
	}
	
	// 测试发送成功消息
	conn.sendSuccess("操作成功", map[string]interface{}{"result": "ok"})
	
	select {
	case response := <-conn.Send:
		var msg Message
		err := json.Unmarshal(response, &msg)
		require.NoError(t, err)
		assert.Equal(t, "success", msg.Type)
		assert.Equal(t, "server", msg.From)
		
		data, ok := msg.Data.(map[string]interface{})
		require.True(t, ok)
		assert.Equal(t, "操作成功", data["message"])
		
		result, ok := data["data"].(map[string]interface{})
		require.True(t, ok)
		assert.Equal(t, "ok", result["result"])
	case <-time.After(100 * time.Millisecond):
		t.Fatal("没有收到成功响应")
	}
}

func TestDefaultConfig(t *testing.T) {
	config := DefaultConfig()
	
	assert.Equal(t, 4096, config.ReadBufferSize)
	assert.Equal(t, 4096, config.WriteBufferSize)
	assert.Equal(t, 10*time.Second, config.HandshakeTimeout)
	assert.Equal(t, 60*time.Second, config.ReadTimeout)
	assert.Equal(t, 10*time.Second, config.WriteTimeout)
	assert.Equal(t, 30*time.Second, config.PingInterval)
	assert.Equal(t, 10*time.Second, config.PongTimeout)
	assert.Equal(t, int64(1024*1024), config.MaxMessageSize)
	assert.Equal(t, 10000, config.MaxConnections)
	assert.True(t, config.EnableCompression)
	assert.True(t, config.CheckOrigin)
	assert.Equal(t, []string{"*"}, config.AllowedOrigins)
	assert.False(t, config.EnableAuth)
	assert.Equal(t, 30*time.Second, config.AuthTimeout)
	assert.True(t, config.EnableRooms)
	assert.Equal(t, 1000, config.MaxRooms)
	assert.Equal(t, 100, config.MaxRoomSize)
	assert.True(t, config.EnableMetrics)
	assert.Equal(t, 30*time.Second, config.MetricsInterval)
}

// 集成测试辅助函数
func setupTestServer(t *testing.T) (*WebSocketGateway, *httptest.Server) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	gateway := NewWebSocketGateway(config, logger)
	
	gin.SetMode(gin.TestMode)
	router := gin.New()
	
	router.GET("/ws", func(c *gin.Context) {
		gateway.HandleUpgrade(c)
	})
	
	server := httptest.NewServer(router)
	return gateway, server
}

func TestWebSocketGateway_Integration(t *testing.T) {
	gateway, server := setupTestServer(t)
	defer server.Close()
	
	// 将 HTTP URL 转换为 WebSocket URL
	wsURL := "ws" + strings.TrimPrefix(server.URL, "http") + "/ws"
	
	// 尝试连接 WebSocket（这里只是测试 URL 格式）
	assert.Contains(t, wsURL, "ws://")
	assert.Contains(t, wsURL, "/ws")
	
	// 验证网关状态
	stats := gateway.GetStats()
	assert.NotNil(t, stats)
	assert.Equal(t, int64(0), stats.ActiveConnections)
}
