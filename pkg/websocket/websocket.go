package websocket

import (
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/gorilla/websocket"
	"paas-platform/pkg/logger"
)

// WebSocketGateway WebSocket 网关
type WebSocketGateway struct {
	connections map[string]*Connection
	rooms       map[string]*Room
	routes      map[string]*Route
	mutex       sync.RWMutex
	logger      logger.Logger
	config      Config
	upgrader    websocket.Upgrader
	stats       *Stats
}

// Connection WebSocket 连接
type Connection struct {
	ID          string                 `json:"id"`
	UserID      string                 `json:"user_id"`
	SessionID   string                 `json:"session_id"`
	Conn        *websocket.Conn        `json:"-"`
	Gateway     *WebSocketGateway      `json:"-"`
	Send        chan []byte            `json:"-"`
	Receive     chan []byte            `json:"-"`
	Metadata    map[string]interface{} `json:"metadata"`
	ConnectedAt time.Time              `json:"connected_at"`
	LastPing    time.Time              `json:"last_ping"`
	IsAlive     bool                   `json:"is_alive"`
	Rooms       map[string]bool        `json:"rooms"`
	RemoteAddr  string                 `json:"remote_addr"`
	UserAgent   string                 `json:"user_agent"`
}

// Room WebSocket 房间
type Room struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Connections map[string]*Connection `json:"-"`
	Metadata    map[string]interface{} `json:"metadata"`
	CreatedAt   time.Time              `json:"created_at"`
	MaxSize     int                    `json:"max_size"`
	IsPrivate   bool                   `json:"is_private"`
	Owner       string                 `json:"owner"`
}

// Route WebSocket 路由
type Route struct {
	Path        string            `json:"path"`
	Target      string            `json:"target"`
	Methods     []string          `json:"methods"`
	Headers     map[string]string `json:"headers"`
	Timeout     time.Duration     `json:"timeout"`
	RetryCount  int               `json:"retry_count"`
	LoadBalance string            `json:"load_balance"` // round_robin, random, least_conn
	HealthCheck string            `json:"health_check"`
	Enabled     bool              `json:"enabled"`
}

// Message WebSocket 消息
type Message struct {
	ID        string                 `json:"id"`
	Type      string                 `json:"type"`
	From      string                 `json:"from"`
	To        string                 `json:"to"`
	Room      string                 `json:"room"`
	Data      interface{}            `json:"data"`
	Metadata  map[string]interface{} `json:"metadata"`
	Timestamp time.Time              `json:"timestamp"`
}

// Config WebSocket 配置
type Config struct {
	ReadBufferSize    int           `json:"read_buffer_size"`
	WriteBufferSize   int           `json:"write_buffer_size"`
	HandshakeTimeout  time.Duration `json:"handshake_timeout"`
	ReadTimeout       time.Duration `json:"read_timeout"`
	WriteTimeout      time.Duration `json:"write_timeout"`
	PingInterval      time.Duration `json:"ping_interval"`
	PongTimeout       time.Duration `json:"pong_timeout"`
	MaxMessageSize    int64         `json:"max_message_size"`
	MaxConnections    int           `json:"max_connections"`
	EnableCompression bool          `json:"enable_compression"`
	CheckOrigin       bool          `json:"check_origin"`
	AllowedOrigins    []string      `json:"allowed_origins"`
	EnableAuth        bool          `json:"enable_auth"`
	AuthTimeout       time.Duration `json:"auth_timeout"`
	EnableRooms       bool          `json:"enable_rooms"`
	MaxRooms          int           `json:"max_rooms"`
	MaxRoomSize       int           `json:"max_room_size"`
	EnableMetrics     bool          `json:"enable_metrics"`
	MetricsInterval   time.Duration `json:"metrics_interval"`
}

// Stats WebSocket 统计信息
type Stats struct {
	TotalConnections    int64     `json:"total_connections"`
	ActiveConnections   int64     `json:"active_connections"`
	TotalMessages       int64     `json:"total_messages"`
	MessagesSent        int64     `json:"messages_sent"`
	MessagesReceived    int64     `json:"messages_received"`
	TotalRooms          int64     `json:"total_rooms"`
	ActiveRooms         int64     `json:"active_rooms"`
	BytesSent           int64     `json:"bytes_sent"`
	BytesReceived       int64     `json:"bytes_received"`
	ConnectionErrors    int64     `json:"connection_errors"`
	MessageErrors       int64     `json:"message_errors"`
	LastUpdated         time.Time `json:"last_updated"`
	AverageLatency      float64   `json:"average_latency"`
	PeakConnections     int64     `json:"peak_connections"`
	ConnectionsPerSecond float64  `json:"connections_per_second"`
}

// NewWebSocketGateway 创建 WebSocket 网关
func NewWebSocketGateway(config Config, logger logger.Logger) *WebSocketGateway {
	gateway := &WebSocketGateway{
		connections: make(map[string]*Connection),
		rooms:       make(map[string]*Room),
		routes:      make(map[string]*Route),
		logger:      logger,
		config:      config,
		stats:       &Stats{},
		upgrader: websocket.Upgrader{
			ReadBufferSize:   config.ReadBufferSize,
			WriteBufferSize:  config.WriteBufferSize,
			HandshakeTimeout: config.HandshakeTimeout,
			CheckOrigin:      func(r *http.Request) bool { return true }, // 临时设置，后面会被覆盖
		},
	}

	// 设置默认值
	if gateway.config.ReadBufferSize == 0 {
		gateway.config.ReadBufferSize = 4096
	}
	if gateway.config.WriteBufferSize == 0 {
		gateway.config.WriteBufferSize = 4096
	}
	if gateway.config.HandshakeTimeout == 0 {
		gateway.config.HandshakeTimeout = 10 * time.Second
	}
	if gateway.config.PingInterval == 0 {
		gateway.config.PingInterval = 30 * time.Second
	}
	if gateway.config.PongTimeout == 0 {
		gateway.config.PongTimeout = 10 * time.Second
	}
	if gateway.config.MaxMessageSize == 0 {
		gateway.config.MaxMessageSize = 1024 * 1024 // 1MB
	}
	if gateway.config.MaxConnections == 0 {
		gateway.config.MaxConnections = 10000
	}

	// 设置正确的 CheckOrigin 函数
	gateway.upgrader.CheckOrigin = gateway.checkOrigin

	// 启动后台任务
	go gateway.pingHandler()
	go gateway.cleanupHandler()
	if gateway.config.EnableMetrics {
		go gateway.metricsHandler()
	}

	return gateway
}

// HandleUpgrade 处理 WebSocket 升级
func (gw *WebSocketGateway) HandleUpgrade(c *gin.Context) {
	// 检查连接数限制
	if gw.getActiveConnectionCount() >= int64(gw.config.MaxConnections) {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "连接数已达上限",
		})
		return
	}

	// 升级连接
	conn, err := gw.upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		gw.logger.Error("WebSocket 升级失败", "error", err)
		gw.stats.ConnectionErrors++
		return
	}

	// 创建连接对象
	connection := &Connection{
		ID:          uuid.New().String(),
		Conn:        conn,
		Gateway:     gw,
		Send:        make(chan []byte, 256),
		Receive:     make(chan []byte, 256),
		Metadata:    make(map[string]interface{}),
		ConnectedAt: time.Now(),
		LastPing:    time.Now(),
		IsAlive:     true,
		Rooms:       make(map[string]bool),
		RemoteAddr:  c.ClientIP(),
		UserAgent:   c.Request.UserAgent(),
	}

	// 从请求中提取用户信息
	if userID := c.GetHeader("X-User-ID"); userID != "" {
		connection.UserID = userID
	}
	if sessionID := c.GetHeader("X-Session-ID"); sessionID != "" {
		connection.SessionID = sessionID
	}

	// 注册连接
	gw.registerConnection(connection)

	// 启动连接处理
	go connection.readPump()
	go connection.writePump()

	gw.logger.Info("WebSocket 连接建立",
		"connection_id", connection.ID,
		"user_id", connection.UserID,
		"remote_addr", connection.RemoteAddr)
}

// registerConnection 注册连接
func (gw *WebSocketGateway) registerConnection(conn *Connection) {
	gw.mutex.Lock()
	defer gw.mutex.Unlock()

	gw.connections[conn.ID] = conn
	gw.stats.TotalConnections++
	gw.stats.ActiveConnections++

	// 更新峰值连接数
	if gw.stats.ActiveConnections > gw.stats.PeakConnections {
		gw.stats.PeakConnections = gw.stats.ActiveConnections
	}
}

// unregisterConnection 注销连接
func (gw *WebSocketGateway) unregisterConnection(conn *Connection) {
	gw.mutex.Lock()
	defer gw.mutex.Unlock()

	if _, exists := gw.connections[conn.ID]; exists {
		// 从所有房间中移除
		for roomID := range conn.Rooms {
			if room, exists := gw.rooms[roomID]; exists {
				delete(room.Connections, conn.ID)
				if len(room.Connections) == 0 {
					delete(gw.rooms, roomID)
					gw.stats.ActiveRooms--
				}
			}
		}

		// 关闭通道
		close(conn.Send)
		close(conn.Receive)

		// 移除连接
		delete(gw.connections, conn.ID)
		gw.stats.ActiveConnections--

		gw.logger.Info("WebSocket 连接关闭",
			"connection_id", conn.ID,
			"user_id", conn.UserID)
	}
}

// checkOrigin 检查来源
func (gw *WebSocketGateway) checkOrigin(r *http.Request) bool {
	if !gw.config.CheckOrigin {
		return true
	}

	origin := r.Header.Get("Origin")
	if origin == "" {
		return false
	}

	for _, allowedOrigin := range gw.config.AllowedOrigins {
		if origin == allowedOrigin || allowedOrigin == "*" {
			return true
		}
	}

	return false
}

// getActiveConnectionCount 获取活跃连接数
func (gw *WebSocketGateway) getActiveConnectionCount() int64 {
	gw.mutex.RLock()
	defer gw.mutex.RUnlock()
	return int64(len(gw.connections))
}

// GetStats 获取统计信息
func (gw *WebSocketGateway) GetStats() *Stats {
	gw.mutex.RLock()
	defer gw.mutex.RUnlock()

	stats := *gw.stats
	stats.ActiveConnections = int64(len(gw.connections))
	stats.ActiveRooms = int64(len(gw.rooms))
	stats.LastUpdated = time.Now()

	return &stats
}

// GetConnections 获取所有连接
func (gw *WebSocketGateway) GetConnections() []*Connection {
	gw.mutex.RLock()
	defer gw.mutex.RUnlock()

	connections := make([]*Connection, 0, len(gw.connections))
	for _, conn := range gw.connections {
		connections = append(connections, conn)
	}

	return connections
}

// GetConnection 获取指定连接
func (gw *WebSocketGateway) GetConnection(id string) (*Connection, bool) {
	gw.mutex.RLock()
	defer gw.mutex.RUnlock()

	conn, exists := gw.connections[id]
	return conn, exists
}

// BroadcastMessage 广播消息
func (gw *WebSocketGateway) BroadcastMessage(message *Message) error {
	data, err := json.Marshal(message)
	if err != nil {
		return err
	}

	gw.mutex.RLock()
	defer gw.mutex.RUnlock()

	for _, conn := range gw.connections {
		if conn.IsAlive {
			select {
			case conn.Send <- data:
				gw.stats.MessagesSent++
			default:
				// 发送缓冲区已满，跳过
				gw.logger.Warn("消息发送缓冲区已满", "connection_id", conn.ID)
			}
		}
	}

	gw.stats.TotalMessages++
	return nil
}

// SendMessage 发送消息给指定连接
func (gw *WebSocketGateway) SendMessage(connectionID string, message *Message) error {
	conn, exists := gw.GetConnection(connectionID)
	if !exists {
		return fmt.Errorf("连接不存在: %s", connectionID)
	}

	data, err := json.Marshal(message)
	if err != nil {
		return err
	}

	select {
	case conn.Send <- data:
		gw.stats.MessagesSent++
		gw.stats.TotalMessages++
		return nil
	default:
		return fmt.Errorf("消息发送失败，缓冲区已满")
	}
}

// DefaultConfig 默认配置
func DefaultConfig() Config {
	return Config{
		ReadBufferSize:    4096,
		WriteBufferSize:   4096,
		HandshakeTimeout:  10 * time.Second,
		ReadTimeout:       60 * time.Second,
		WriteTimeout:      10 * time.Second,
		PingInterval:      30 * time.Second,
		PongTimeout:       10 * time.Second,
		MaxMessageSize:    1024 * 1024, // 1MB
		MaxConnections:    10000,
		EnableCompression: true,
		CheckOrigin:       true,
		AllowedOrigins:    []string{"*"},
		EnableAuth:        false,
		AuthTimeout:       30 * time.Second,
		EnableRooms:       true,
		MaxRooms:          1000,
		MaxRoomSize:       100,
		EnableMetrics:     true,
		MetricsInterval:   30 * time.Second,
	}
}

// readPump 读取消息泵
func (c *Connection) readPump() {
	defer func() {
		c.Gateway.unregisterConnection(c)
		c.Conn.Close()
	}()

	// 设置读取限制
	c.Conn.SetReadLimit(c.Gateway.config.MaxMessageSize)
	c.Conn.SetReadDeadline(time.Now().Add(c.Gateway.config.PongTimeout))
	c.Conn.SetPongHandler(func(string) error {
		c.LastPing = time.Now()
		c.Conn.SetReadDeadline(time.Now().Add(c.Gateway.config.PongTimeout))
		return nil
	})

	for {
		messageType, data, err := c.Conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				c.Gateway.logger.Error("WebSocket 读取错误", "error", err, "connection_id", c.ID)
				c.Gateway.stats.MessageErrors++
			}
			break
		}

		if messageType == websocket.TextMessage || messageType == websocket.BinaryMessage {
			c.Gateway.stats.MessagesReceived++
			c.Gateway.stats.BytesReceived += int64(len(data))

			// 处理消息
			c.handleMessage(data)
		}
	}
}

// writePump 写入消息泵
func (c *Connection) writePump() {
	ticker := time.NewTicker(c.Gateway.config.PingInterval)
	defer func() {
		ticker.Stop()
		c.Conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.Send:
			c.Conn.SetWriteDeadline(time.Now().Add(c.Gateway.config.WriteTimeout))
			if !ok {
				c.Conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			if err := c.Conn.WriteMessage(websocket.TextMessage, message); err != nil {
				c.Gateway.logger.Error("WebSocket 写入错误", "error", err, "connection_id", c.ID)
				c.Gateway.stats.MessageErrors++
				return
			}

			c.Gateway.stats.BytesSent += int64(len(message))

		case <-ticker.C:
			c.Conn.SetWriteDeadline(time.Now().Add(c.Gateway.config.WriteTimeout))
			if err := c.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				c.Gateway.logger.Error("WebSocket Ping 失败", "error", err, "connection_id", c.ID)
				return
			}
		}
	}
}

// handleMessage 处理消息
func (c *Connection) handleMessage(data []byte) {
	var message Message
	if err := json.Unmarshal(data, &message); err != nil {
		c.Gateway.logger.Error("消息解析失败", "error", err, "connection_id", c.ID)
		c.Gateway.stats.MessageErrors++
		return
	}

	// 设置消息来源
	message.From = c.ID
	message.Timestamp = time.Now()

	// 根据消息类型处理
	switch message.Type {
	case "ping":
		c.handlePing(&message)
	case "join_room":
		c.handleJoinRoom(&message)
	case "leave_room":
		c.handleLeaveRoom(&message)
	case "room_message":
		c.handleRoomMessage(&message)
	case "private_message":
		c.handlePrivateMessage(&message)
	case "broadcast":
		c.handleBroadcast(&message)
	default:
		c.handleCustomMessage(&message)
	}
}

// handlePing 处理 Ping 消息
func (c *Connection) handlePing(message *Message) {
	response := &Message{
		ID:        uuid.New().String(),
		Type:      "pong",
		From:      "server",
		To:        c.ID,
		Data:      message.Data,
		Timestamp: time.Now(),
	}

	data, _ := json.Marshal(response)
	select {
	case c.Send <- data:
	default:
	}
}

// handleJoinRoom 处理加入房间
func (c *Connection) handleJoinRoom(message *Message) {
	roomID, ok := message.Data.(string)
	if !ok {
		c.sendError("无效的房间ID")
		return
	}

	if err := c.Gateway.JoinRoom(c.ID, roomID); err != nil {
		c.sendError(err.Error())
		return
	}

	c.sendSuccess("成功加入房间", map[string]interface{}{
		"room_id": roomID,
	})
}

// handleLeaveRoom 处理离开房间
func (c *Connection) handleLeaveRoom(message *Message) {
	roomID, ok := message.Data.(string)
	if !ok {
		c.sendError("无效的房间ID")
		return
	}

	if err := c.Gateway.LeaveRoom(c.ID, roomID); err != nil {
		c.sendError(err.Error())
		return
	}

	c.sendSuccess("成功离开房间", map[string]interface{}{
		"room_id": roomID,
	})
}

// handleRoomMessage 处理房间消息
func (c *Connection) handleRoomMessage(message *Message) {
	if message.Room == "" {
		c.sendError("房间ID不能为空")
		return
	}

	if !c.Rooms[message.Room] {
		c.sendError("您不在该房间中")
		return
	}

	c.Gateway.SendRoomMessage(message.Room, message)
}

// handlePrivateMessage 处理私聊消息
func (c *Connection) handlePrivateMessage(message *Message) {
	if message.To == "" {
		c.sendError("接收者不能为空")
		return
	}

	c.Gateway.SendMessage(message.To, message)
}

// handleBroadcast 处理广播消息
func (c *Connection) handleBroadcast(message *Message) {
	c.Gateway.BroadcastMessage(message)
}

// handleCustomMessage 处理自定义消息
func (c *Connection) handleCustomMessage(message *Message) {
	// 可以在这里添加自定义消息处理逻辑
	c.Gateway.logger.Debug("收到自定义消息",
		"type", message.Type,
		"from", message.From,
		"connection_id", c.ID)
}

// sendError 发送错误消息
func (c *Connection) sendError(errorMsg string) {
	response := &Message{
		ID:        uuid.New().String(),
		Type:      "error",
		From:      "server",
		To:        c.ID,
		Data:      errorMsg,
		Timestamp: time.Now(),
	}

	data, _ := json.Marshal(response)
	select {
	case c.Send <- data:
	default:
	}
}

// sendSuccess 发送成功消息
func (c *Connection) sendSuccess(msg string, data interface{}) {
	response := &Message{
		ID:   uuid.New().String(),
		Type: "success",
		From: "server",
		To:   c.ID,
		Data: map[string]interface{}{
			"message": msg,
			"data":    data,
		},
		Timestamp: time.Now(),
	}

	responseData, _ := json.Marshal(response)
	select {
	case c.Send <- responseData:
	default:
	}
}

// JoinRoom 加入房间
func (gw *WebSocketGateway) JoinRoom(connectionID, roomID string) error {
	gw.mutex.Lock()
	defer gw.mutex.Unlock()

	conn, exists := gw.connections[connectionID]
	if !exists {
		return fmt.Errorf("连接不存在: %s", connectionID)
	}

	// 获取或创建房间
	room, exists := gw.rooms[roomID]
	if !exists {
		if len(gw.rooms) >= gw.config.MaxRooms {
			return fmt.Errorf("房间数量已达上限")
		}

		room = &Room{
			ID:          roomID,
			Name:        roomID,
			Connections: make(map[string]*Connection),
			Metadata:    make(map[string]interface{}),
			CreatedAt:   time.Now(),
			MaxSize:     gw.config.MaxRoomSize,
			IsPrivate:   false,
		}
		gw.rooms[roomID] = room
		gw.stats.TotalRooms++
		gw.stats.ActiveRooms++
	}

	// 检查房间大小限制
	if len(room.Connections) >= room.MaxSize {
		return fmt.Errorf("房间已满")
	}

	// 加入房间
	room.Connections[connectionID] = conn
	conn.Rooms[roomID] = true

	gw.logger.Info("用户加入房间",
		"connection_id", connectionID,
		"room_id", roomID,
		"room_size", len(room.Connections))

	return nil
}

// LeaveRoom 离开房间
func (gw *WebSocketGateway) LeaveRoom(connectionID, roomID string) error {
	gw.mutex.Lock()
	defer gw.mutex.Unlock()

	conn, exists := gw.connections[connectionID]
	if !exists {
		return fmt.Errorf("连接不存在: %s", connectionID)
	}

	room, exists := gw.rooms[roomID]
	if !exists {
		return fmt.Errorf("房间不存在: %s", roomID)
	}

	// 离开房间
	delete(room.Connections, connectionID)
	delete(conn.Rooms, roomID)

	// 如果房间为空，删除房间
	if len(room.Connections) == 0 {
		delete(gw.rooms, roomID)
		gw.stats.ActiveRooms--
	}

	gw.logger.Info("用户离开房间",
		"connection_id", connectionID,
		"room_id", roomID,
		"room_size", len(room.Connections))

	return nil
}

// SendRoomMessage 发送房间消息
func (gw *WebSocketGateway) SendRoomMessage(roomID string, message *Message) error {
	gw.mutex.RLock()
	defer gw.mutex.RUnlock()

	room, exists := gw.rooms[roomID]
	if !exists {
		return fmt.Errorf("房间不存在: %s", roomID)
	}

	message.Room = roomID
	data, err := json.Marshal(message)
	if err != nil {
		return err
	}

	// 发送给房间内所有连接
	for _, conn := range room.Connections {
		if conn.IsAlive {
			select {
			case conn.Send <- data:
				gw.stats.MessagesSent++
			default:
				gw.logger.Warn("房间消息发送失败，缓冲区已满",
					"connection_id", conn.ID,
					"room_id", roomID)
			}
		}
	}

	gw.stats.TotalMessages++
	return nil
}

// GetRooms 获取所有房间
func (gw *WebSocketGateway) GetRooms() []*Room {
	gw.mutex.RLock()
	defer gw.mutex.RUnlock()

	rooms := make([]*Room, 0, len(gw.rooms))
	for _, room := range gw.rooms {
		rooms = append(rooms, room)
	}

	return rooms
}

// GetRoom 获取指定房间
func (gw *WebSocketGateway) GetRoom(id string) (*Room, bool) {
	gw.mutex.RLock()
	defer gw.mutex.RUnlock()

	room, exists := gw.rooms[id]
	return room, exists
}

// CreateRoom 创建房间
func (gw *WebSocketGateway) CreateRoom(id, name, description string, maxSize int, isPrivate bool, owner string) error {
	gw.mutex.Lock()
	defer gw.mutex.Unlock()

	if _, exists := gw.rooms[id]; exists {
		return fmt.Errorf("房间已存在: %s", id)
	}

	if len(gw.rooms) >= gw.config.MaxRooms {
		return fmt.Errorf("房间数量已达上限")
	}

	room := &Room{
		ID:          id,
		Name:        name,
		Description: description,
		Connections: make(map[string]*Connection),
		Metadata:    make(map[string]interface{}),
		CreatedAt:   time.Now(),
		MaxSize:     maxSize,
		IsPrivate:   isPrivate,
		Owner:       owner,
	}

	if room.MaxSize <= 0 {
		room.MaxSize = gw.config.MaxRoomSize
	}

	gw.rooms[id] = room
	gw.stats.TotalRooms++
	gw.stats.ActiveRooms++

	gw.logger.Info("创建房间",
		"room_id", id,
		"name", name,
		"max_size", maxSize,
		"is_private", isPrivate,
		"owner", owner)

	return nil
}

// DeleteRoom 删除房间
func (gw *WebSocketGateway) DeleteRoom(id string) error {
	gw.mutex.Lock()
	defer gw.mutex.Unlock()

	room, exists := gw.rooms[id]
	if !exists {
		return fmt.Errorf("房间不存在: %s", id)
	}

	// 通知所有连接房间被删除
	message := &Message{
		ID:        uuid.New().String(),
		Type:      "room_deleted",
		From:      "server",
		Room:      id,
		Data:      "房间已被删除",
		Timestamp: time.Now(),
	}

	data, _ := json.Marshal(message)
	for _, conn := range room.Connections {
		select {
		case conn.Send <- data:
		default:
		}
		delete(conn.Rooms, id)
	}

	delete(gw.rooms, id)
	gw.stats.ActiveRooms--

	gw.logger.Info("删除房间", "room_id", id)
	return nil
}

// pingHandler Ping 处理器
func (gw *WebSocketGateway) pingHandler() {
	ticker := time.NewTicker(gw.config.PingInterval)
	defer ticker.Stop()

	for range ticker.C {
		gw.mutex.RLock()
		for _, conn := range gw.connections {
			// 检查连接是否超时
			if time.Since(conn.LastPing) > gw.config.PongTimeout {
				conn.IsAlive = false
				gw.logger.Warn("连接超时", "connection_id", conn.ID)
			}
		}
		gw.mutex.RUnlock()
	}
}

// cleanupHandler 清理处理器
func (gw *WebSocketGateway) cleanupHandler() {
	ticker := time.NewTicker(5 * time.Minute) // 每5分钟清理一次
	defer ticker.Stop()

	for range ticker.C {
		gw.mutex.Lock()

		// 清理死连接
		for id, conn := range gw.connections {
			if !conn.IsAlive {
				gw.logger.Info("清理死连接", "connection_id", id)

				// 从所有房间中移除
				for roomID := range conn.Rooms {
					if room, exists := gw.rooms[roomID]; exists {
						delete(room.Connections, id)
						if len(room.Connections) == 0 {
							delete(gw.rooms, roomID)
							gw.stats.ActiveRooms--
						}
					}
				}

				// 关闭连接
				conn.Conn.Close()
				delete(gw.connections, id)
				gw.stats.ActiveConnections--
			}
		}

		// 清理空房间
		for id, room := range gw.rooms {
			if len(room.Connections) == 0 {
				delete(gw.rooms, id)
				gw.stats.ActiveRooms--
				gw.logger.Info("清理空房间", "room_id", id)
			}
		}

		gw.mutex.Unlock()
	}
}

// metricsHandler 指标处理器
func (gw *WebSocketGateway) metricsHandler() {
	ticker := time.NewTicker(gw.config.MetricsInterval)
	defer ticker.Stop()

	for range ticker.C {
		gw.collectMetrics()
	}
}

// collectMetrics 收集指标
func (gw *WebSocketGateway) collectMetrics() {
	gw.mutex.RLock()
	defer gw.mutex.RUnlock()

	gw.stats.ActiveConnections = int64(len(gw.connections))
	gw.stats.ActiveRooms = int64(len(gw.rooms))
	gw.stats.LastUpdated = time.Now()

	// 计算连接速率（简化实现）
	// 实际项目中可以使用滑动窗口计算更准确的速率
	gw.stats.ConnectionsPerSecond = float64(gw.stats.TotalConnections) / time.Since(time.Now().Add(-time.Hour)).Seconds()

	gw.logger.Debug("WebSocket 指标收集",
		"active_connections", gw.stats.ActiveConnections,
		"active_rooms", gw.stats.ActiveRooms,
		"total_messages", gw.stats.TotalMessages,
		"bytes_sent", gw.stats.BytesSent,
		"bytes_received", gw.stats.BytesReceived)
}

// CloseConnection 关闭连接
func (gw *WebSocketGateway) CloseConnection(connectionID string) error {
	gw.mutex.Lock()
	defer gw.mutex.Unlock()

	conn, exists := gw.connections[connectionID]
	if !exists {
		return fmt.Errorf("连接不存在: %s", connectionID)
	}

	conn.IsAlive = false
	conn.Conn.Close()

	gw.logger.Info("强制关闭连接", "connection_id", connectionID)
	return nil
}

// CloseAllConnections 关闭所有连接
func (gw *WebSocketGateway) CloseAllConnections() {
	gw.mutex.Lock()
	defer gw.mutex.Unlock()

	for _, conn := range gw.connections {
		conn.IsAlive = false
		conn.Conn.Close()
	}

	gw.logger.Info("关闭所有连接", "count", len(gw.connections))
}

// GetConnectionsByUser 根据用户ID获取连接
func (gw *WebSocketGateway) GetConnectionsByUser(userID string) []*Connection {
	gw.mutex.RLock()
	defer gw.mutex.RUnlock()

	var connections []*Connection
	for _, conn := range gw.connections {
		if conn.UserID == userID {
			connections = append(connections, conn)
		}
	}

	return connections
}

// SendMessageToUser 发送消息给指定用户的所有连接
func (gw *WebSocketGateway) SendMessageToUser(userID string, message *Message) error {
	connections := gw.GetConnectionsByUser(userID)
	if len(connections) == 0 {
		return fmt.Errorf("用户不在线: %s", userID)
	}

	data, err := json.Marshal(message)
	if err != nil {
		return err
	}

	for _, conn := range connections {
		if conn.IsAlive {
			select {
			case conn.Send <- data:
				gw.stats.MessagesSent++
			default:
				gw.logger.Warn("用户消息发送失败，缓冲区已满",
					"connection_id", conn.ID,
					"user_id", userID)
			}
		}
	}

	gw.stats.TotalMessages++
	return nil
}

// IsUserOnline 检查用户是否在线
func (gw *WebSocketGateway) IsUserOnline(userID string) bool {
	connections := gw.GetConnectionsByUser(userID)
	for _, conn := range connections {
		if conn.IsAlive {
			return true
		}
	}
	return false
}

// GetOnlineUsers 获取在线用户列表
func (gw *WebSocketGateway) GetOnlineUsers() []string {
	gw.mutex.RLock()
	defer gw.mutex.RUnlock()

	userSet := make(map[string]bool)
	for _, conn := range gw.connections {
		if conn.IsAlive && conn.UserID != "" {
			userSet[conn.UserID] = true
		}
	}

	users := make([]string, 0, len(userSet))
	for userID := range userSet {
		users = append(users, userID)
	}

	return users
}
