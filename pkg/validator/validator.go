package validator

import (
	"encoding/json"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"paas-platform/pkg/logger"
)

// APIValidator API 参数校验器
type APIValidator struct {
	validator *validator.Validate
	logger    logger.Logger
	rules     map[string]*ValidationRule
	schemas   map[string]*JSONSchema
}

// ValidationRule 验证规则
type ValidationRule struct {
	Path        string                 `json:"path"`        // API 路径
	Method      string                 `json:"method"`      // HTTP 方法
	Headers     map[string]string      `json:"headers"`     // 必需的请求头
	QueryParams map[string]ParamRule   `json:"query_params"` // 查询参数规则
	PathParams  map[string]ParamRule   `json:"path_params"`  // 路径参数规则
	Body        *BodyRule              `json:"body"`        // 请求体规则
	Business    []BusinessRule         `json:"business"`    // 业务规则
}

// ParamRule 参数规则
type ParamRule struct {
	Type        string      `json:"type"`         // 参数类型: string, int, float, bool
	Required    bool        `json:"required"`     // 是否必需
	MinLength   int         `json:"min_length"`   // 最小长度
	MaxLength   int         `json:"max_length"`   // 最大长度
	Min         float64     `json:"min"`          // 最小值
	Max         float64     `json:"max"`          // 最大值
	Pattern     string      `json:"pattern"`      // 正则表达式
	Enum        []string    `json:"enum"`         // 枚举值
	Default     interface{} `json:"default"`      // 默认值
	Description string      `json:"description"`  // 参数描述
}

// BodyRule 请求体规则
type BodyRule struct {
	ContentType string     `json:"content_type"` // 内容类型
	Schema      string     `json:"schema"`       // JSON Schema 名称
	MaxSize     int64      `json:"max_size"`     // 最大大小（字节）
	Required    bool       `json:"required"`     // 是否必需
}

// BusinessRule 业务规则
type BusinessRule struct {
	Name        string                 `json:"name"`        // 规则名称
	Description string                 `json:"description"` // 规则描述
	Condition   string                 `json:"condition"`   // 条件表达式
	Message     string                 `json:"message"`     // 错误消息
	Params      map[string]interface{} `json:"params"`      // 规则参数
}

// JSONSchema JSON Schema 定义
type JSONSchema struct {
	Schema      map[string]interface{} `json:"schema"`
	Description string                 `json:"description"`
}

// ValidationError 验证错误
type ValidationError struct {
	Field   string      `json:"field"`
	Value   interface{} `json:"value"`
	Tag     string      `json:"tag"`
	Message string      `json:"message"`
}

// ValidationResult 验证结果
type ValidationResult struct {
	Valid   bool              `json:"valid"`
	Errors  []ValidationError `json:"errors"`
	Message string            `json:"message"`
}

// NewAPIValidator 创建 API 验证器
func NewAPIValidator(logger logger.Logger) *APIValidator {
	v := validator.New()
	
	// 注册自定义验证器
	v.RegisterValidation("phone", validatePhone)
	v.RegisterValidation("email", validateEmail)
	v.RegisterValidation("url", validateURL)
	v.RegisterValidation("uuid", validateUUID)
	v.RegisterValidation("datetime", validateDateTime)
	
	return &APIValidator{
		validator: v,
		logger:    logger,
		rules:     make(map[string]*ValidationRule),
		schemas:   make(map[string]*JSONSchema),
	}
}

// RegisterRule 注册验证规则
func (av *APIValidator) RegisterRule(rule *ValidationRule) error {
	key := fmt.Sprintf("%s:%s", rule.Method, rule.Path)
	av.rules[key] = rule
	
	av.logger.Info("注册API验证规则", 
		"method", rule.Method, 
		"path", rule.Path,
		"key", key)
	
	return nil
}

// RegisterSchema 注册 JSON Schema
func (av *APIValidator) RegisterSchema(name string, schema *JSONSchema) error {
	av.schemas[name] = schema
	
	av.logger.Info("注册JSON Schema", "name", name)
	return nil
}

// ValidateRequest 验证请求
func (av *APIValidator) ValidateRequest(c *gin.Context) *ValidationResult {
	// 获取验证规则
	rule := av.getValidationRule(c.Request.Method, c.Request.URL.Path)
	if rule == nil {
		// 没有验证规则，直接通过
		return &ValidationResult{Valid: true}
	}
	
	result := &ValidationResult{
		Valid:  true,
		Errors: make([]ValidationError, 0),
	}
	
	// 验证请求头
	av.validateHeaders(c, rule, result)
	
	// 验证查询参数
	av.validateQueryParams(c, rule, result)
	
	// 验证路径参数
	av.validatePathParams(c, rule, result)
	
	// 验证请求体
	av.validateBody(c, rule, result)
	
	// 验证业务规则
	av.validateBusinessRules(c, rule, result)
	
	// 设置最终结果
	if len(result.Errors) > 0 {
		result.Valid = false
		result.Message = "请求参数验证失败"
	}
	
	return result
}

// getValidationRule 获取验证规则
func (av *APIValidator) getValidationRule(method, path string) *ValidationRule {
	// 精确匹配
	key := fmt.Sprintf("%s:%s", method, path)
	if rule, exists := av.rules[key]; exists {
		return rule
	}
	
	// 模式匹配
	for _, rule := range av.rules {
		if av.matchPath(rule.Path, path) && rule.Method == method {
			return rule
		}
	}
	
	return nil
}

// matchPath 路径匹配
func (av *APIValidator) matchPath(pattern, path string) bool {
	// 简单的路径匹配实现
	// 支持 /api/v1/apps/{id} 这样的模式
	patternParts := strings.Split(pattern, "/")
	pathParts := strings.Split(path, "/")
	
	if len(patternParts) != len(pathParts) {
		return false
	}
	
	for i, part := range patternParts {
		if strings.HasPrefix(part, "{") && strings.HasSuffix(part, "}") {
			// 路径参数，跳过
			continue
		}
		if part != pathParts[i] {
			return false
		}
	}
	
	return true
}

// validateHeaders 验证请求头
func (av *APIValidator) validateHeaders(c *gin.Context, rule *ValidationRule, result *ValidationResult) {
	for headerName, expectedValue := range rule.Headers {
		actualValue := c.GetHeader(headerName)

		if expectedValue == "required" {
			if actualValue == "" {
				result.Errors = append(result.Errors, ValidationError{
					Field:   fmt.Sprintf("header.%s", headerName),
					Value:   actualValue,
					Tag:     "required",
					Message: fmt.Sprintf("请求头 %s 是必需的", headerName),
				})
			}
		} else if expectedValue != "" && !strings.Contains(actualValue, expectedValue) {
			result.Errors = append(result.Errors, ValidationError{
				Field:   fmt.Sprintf("header.%s", headerName),
				Value:   actualValue,
				Tag:     "invalid",
				Message: fmt.Sprintf("请求头 %s 值无效，期望包含: %s", headerName, expectedValue),
			})
		}
	}
}

// validateQueryParams 验证查询参数
func (av *APIValidator) validateQueryParams(c *gin.Context, rule *ValidationRule, result *ValidationResult) {
	for paramName, paramRule := range rule.QueryParams {
		value := c.Query(paramName)
		
		if err := av.validateParam(paramName, value, paramRule, "query"); err != nil {
			result.Errors = append(result.Errors, *err)
		}
	}
}

// validatePathParams 验证路径参数
func (av *APIValidator) validatePathParams(c *gin.Context, rule *ValidationRule, result *ValidationResult) {
	for paramName, paramRule := range rule.PathParams {
		value := c.Param(paramName)
		
		if err := av.validateParam(paramName, value, paramRule, "path"); err != nil {
			result.Errors = append(result.Errors, *err)
		}
	}
}

// validateParam 验证单个参数
func (av *APIValidator) validateParam(name, value string, rule ParamRule, location string) *ValidationError {
	// 检查必需参数
	if rule.Required && value == "" {
		return &ValidationError{
			Field:   fmt.Sprintf("%s.%s", location, name),
			Value:   value,
			Tag:     "required",
			Message: fmt.Sprintf("%s参数 %s 是必需的", location, name),
		}
	}
	
	// 如果参数为空且不是必需的，跳过验证
	if value == "" {
		return nil
	}
	
	// 类型验证
	if err := av.validateParamType(name, value, rule, location); err != nil {
		return err
	}
	
	// 长度验证
	if err := av.validateParamLength(name, value, rule, location); err != nil {
		return err
	}
	
	// 范围验证
	if err := av.validateParamRange(name, value, rule, location); err != nil {
		return err
	}
	
	// 正则验证
	if err := av.validateParamPattern(name, value, rule, location); err != nil {
		return err
	}
	
	// 枚举验证
	if err := av.validateParamEnum(name, value, rule, location); err != nil {
		return err
	}
	
	return nil
}

// validateParamType 验证参数类型
func (av *APIValidator) validateParamType(name, value string, rule ParamRule, location string) *ValidationError {
	switch rule.Type {
	case "int":
		if _, err := strconv.Atoi(value); err != nil {
			return &ValidationError{
				Field:   fmt.Sprintf("%s.%s", location, name),
				Value:   value,
				Tag:     "type",
				Message: fmt.Sprintf("%s参数 %s 必须是整数", location, name),
			}
		}
	case "float":
		if _, err := strconv.ParseFloat(value, 64); err != nil {
			return &ValidationError{
				Field:   fmt.Sprintf("%s.%s", location, name),
				Value:   value,
				Tag:     "type",
				Message: fmt.Sprintf("%s参数 %s 必须是浮点数", location, name),
			}
		}
	case "bool":
		if _, err := strconv.ParseBool(value); err != nil {
			return &ValidationError{
				Field:   fmt.Sprintf("%s.%s", location, name),
				Value:   value,
				Tag:     "type",
				Message: fmt.Sprintf("%s参数 %s 必须是布尔值", location, name),
			}
		}
	}
	
	return nil
}

// validateParamLength 验证参数长度
func (av *APIValidator) validateParamLength(name, value string, rule ParamRule, location string) *ValidationError {
	if rule.MinLength > 0 && len(value) < rule.MinLength {
		return &ValidationError{
			Field:   fmt.Sprintf("%s.%s", location, name),
			Value:   value,
			Tag:     "min_length",
			Message: fmt.Sprintf("%s参数 %s 长度不能少于 %d", location, name, rule.MinLength),
		}
	}
	
	if rule.MaxLength > 0 && len(value) > rule.MaxLength {
		return &ValidationError{
			Field:   fmt.Sprintf("%s.%s", location, name),
			Value:   value,
			Tag:     "max_length",
			Message: fmt.Sprintf("%s参数 %s 长度不能超过 %d", location, name, rule.MaxLength),
		}
	}
	
	return nil
}

// validateParamRange 验证参数范围
func (av *APIValidator) validateParamRange(name, value string, rule ParamRule, location string) *ValidationError {
	if rule.Type == "int" || rule.Type == "float" {
		var numValue float64
		var err error
		
		if rule.Type == "int" {
			intVal, e := strconv.Atoi(value)
			numValue = float64(intVal)
			err = e
		} else {
			numValue, err = strconv.ParseFloat(value, 64)
		}
		
		if err != nil {
			return nil // 类型验证已经处理
		}
		
		if rule.Min != 0 && numValue < rule.Min {
			return &ValidationError{
				Field:   fmt.Sprintf("%s.%s", location, name),
				Value:   value,
				Tag:     "min",
				Message: fmt.Sprintf("%s参数 %s 不能小于 %g", location, name, rule.Min),
			}
		}
		
		if rule.Max != 0 && numValue > rule.Max {
			return &ValidationError{
				Field:   fmt.Sprintf("%s.%s", location, name),
				Value:   value,
				Tag:     "max",
				Message: fmt.Sprintf("%s参数 %s 不能大于 %g", location, name, rule.Max),
			}
		}
	}
	
	return nil
}

// validateParamPattern 验证正则表达式
func (av *APIValidator) validateParamPattern(name, value string, rule ParamRule, location string) *ValidationError {
	if rule.Pattern != "" {
		matched, err := regexp.MatchString(rule.Pattern, value)
		if err != nil {
			av.logger.Error("正则表达式错误", "pattern", rule.Pattern, "error", err)
			return nil
		}
		
		if !matched {
			return &ValidationError{
				Field:   fmt.Sprintf("%s.%s", location, name),
				Value:   value,
				Tag:     "pattern",
				Message: fmt.Sprintf("%s参数 %s 格式不正确", location, name),
			}
		}
	}
	
	return nil
}

// validateParamEnum 验证枚举值
func (av *APIValidator) validateParamEnum(name, value string, rule ParamRule, location string) *ValidationError {
	if len(rule.Enum) > 0 && value != "" { // 空值跳过枚举验证
		for _, enumValue := range rule.Enum {
			if value == enumValue {
				return nil
			}
		}

		return &ValidationError{
			Field:   fmt.Sprintf("%s.%s", location, name),
			Value:   value,
			Tag:     "enum",
			Message: fmt.Sprintf("%s参数 %s 必须是以下值之一: %s", location, name, strings.Join(rule.Enum, ", ")),
		}
	}

	return nil
}

// validateBody 验证请求体
func (av *APIValidator) validateBody(c *gin.Context, rule *ValidationRule, result *ValidationResult) {
	if rule.Body == nil {
		return
	}

	// 检查内容类型
	contentType := c.GetHeader("Content-Type")
	if rule.Body.ContentType != "" && !strings.Contains(contentType, rule.Body.ContentType) {
		result.Errors = append(result.Errors, ValidationError{
			Field:   "body.content_type",
			Value:   contentType,
			Tag:     "content_type",
			Message: fmt.Sprintf("请求体内容类型必须是 %s", rule.Body.ContentType),
		})
		return
	}

	// 获取请求体
	bodyBytes, err := c.GetRawData()
	if err != nil {
		result.Errors = append(result.Errors, ValidationError{
			Field:   "body",
			Value:   nil,
			Tag:     "read_error",
			Message: "无法读取请求体",
		})
		return
	}

	// 检查是否必需
	if rule.Body.Required && len(bodyBytes) == 0 {
		result.Errors = append(result.Errors, ValidationError{
			Field:   "body",
			Value:   nil,
			Tag:     "required",
			Message: "请求体是必需的",
		})
		return
	}

	// 检查大小限制
	if rule.Body.MaxSize > 0 && int64(len(bodyBytes)) > rule.Body.MaxSize {
		result.Errors = append(result.Errors, ValidationError{
			Field:   "body.size",
			Value:   len(bodyBytes),
			Tag:     "max_size",
			Message: fmt.Sprintf("请求体大小不能超过 %d 字节", rule.Body.MaxSize),
		})
		return
	}

	// JSON Schema 验证
	if rule.Body.Schema != "" {
		av.validateJSONSchema(bodyBytes, rule.Body.Schema, result)
	}
}

// validateJSONSchema 验证 JSON Schema
func (av *APIValidator) validateJSONSchema(bodyBytes []byte, schemaName string, result *ValidationResult) {
	schema, exists := av.schemas[schemaName]
	if !exists {
		av.logger.Warn("JSON Schema 不存在", "schema", schemaName)
		return
	}

	// 解析 JSON
	var data interface{}
	if err := json.Unmarshal(bodyBytes, &data); err != nil {
		result.Errors = append(result.Errors, ValidationError{
			Field:   "body.json",
			Value:   string(bodyBytes),
			Tag:     "json_format",
			Message: "请求体不是有效的 JSON 格式",
		})
		return
	}

	// 简单的 Schema 验证实现
	// 在实际项目中，建议使用专业的 JSON Schema 验证库
	av.validateJSONData(data, schema.Schema, "body", result)
}

// validateJSONData 验证 JSON 数据
func (av *APIValidator) validateJSONData(data interface{}, schema map[string]interface{}, prefix string, result *ValidationResult) {
	// 获取 schema 类型
	schemaType, ok := schema["type"].(string)
	if !ok {
		return
	}

	// 验证类型
	if !av.validateJSONType(data, schemaType) {
		result.Errors = append(result.Errors, ValidationError{
			Field:   prefix,
			Value:   data,
			Tag:     "type",
			Message: fmt.Sprintf("字段类型必须是 %s", schemaType),
		})
		return
	}

	// 验证必需字段
	if schemaType == "object" {
		required, ok := schema["required"].([]interface{})
		if ok {
			dataMap, ok := data.(map[string]interface{})
			if ok {
				for _, req := range required {
					fieldName, ok := req.(string)
					if ok {
						if _, exists := dataMap[fieldName]; !exists {
							result.Errors = append(result.Errors, ValidationError{
								Field:   fmt.Sprintf("%s.%s", prefix, fieldName),
								Value:   nil,
								Tag:     "required",
								Message: fmt.Sprintf("字段 %s 是必需的", fieldName),
							})
						}
					}
				}
			}
		}

		// 验证对象属性
		properties, ok := schema["properties"].(map[string]interface{})
		if ok {
			dataMap, ok := data.(map[string]interface{})
			if ok {
				for fieldName, fieldSchema := range properties {
					if fieldValue, exists := dataMap[fieldName]; exists {
						fieldSchemaMap, ok := fieldSchema.(map[string]interface{})
						if ok {
							av.validateJSONData(fieldValue, fieldSchemaMap, fmt.Sprintf("%s.%s", prefix, fieldName), result)
						}
					}
				}
			}
		}
	}
}

// validateJSONType 验证 JSON 类型
func (av *APIValidator) validateJSONType(data interface{}, expectedType string) bool {
	switch expectedType {
	case "string":
		_, ok := data.(string)
		return ok
	case "number":
		_, ok1 := data.(float64)
		_, ok2 := data.(int)
		return ok1 || ok2
	case "integer":
		// JSON 中的数字通常被解析为 float64，需要检查是否为整数
		if f, ok := data.(float64); ok {
			return f == float64(int(f))
		}
		_, ok := data.(int)
		return ok
	case "boolean":
		_, ok := data.(bool)
		return ok
	case "array":
		_, ok := data.([]interface{})
		return ok
	case "object":
		_, ok := data.(map[string]interface{})
		return ok
	case "null":
		return data == nil
	}

	return false
}

// validateBusinessRules 验证业务规则
func (av *APIValidator) validateBusinessRules(c *gin.Context, rule *ValidationRule, result *ValidationResult) {
	for _, businessRule := range rule.Business {
		if !av.evaluateBusinessRule(c, businessRule) {
			result.Errors = append(result.Errors, ValidationError{
				Field:   "business_rule",
				Value:   businessRule.Name,
				Tag:     "business",
				Message: businessRule.Message,
			})
		}
	}
}

// evaluateBusinessRule 评估业务规则
func (av *APIValidator) evaluateBusinessRule(c *gin.Context, rule BusinessRule) bool {
	// 简单的业务规则评估实现
	// 在实际项目中，可以使用表达式引擎如 govaluate

	switch rule.Name {
	case "tenant_access":
		// 租户访问权限检查
		userTenantID := c.GetString("tenant_id")
		pathTenantID := c.Param("tenant_id")
		return userTenantID == pathTenantID

	case "resource_owner":
		// 资源所有者检查
		userID := c.GetString("user_id")
		resourceOwnerID := c.Param("user_id")
		return userID == resourceOwnerID

	case "admin_only":
		// 管理员权限检查
		roles := c.GetStringSlice("roles")
		for _, role := range roles {
			if role == "admin" {
				return true
			}
		}
		return false

	default:
		av.logger.Warn("未知的业务规则", "rule", rule.Name)
		return true
	}
}

// 自定义验证器函数

// validatePhone 验证手机号
func validatePhone(fl validator.FieldLevel) bool {
	phone := fl.Field().String()
	matched, _ := regexp.MatchString(`^1[3-9]\d{9}$`, phone)
	return matched
}

// validateEmail 验证邮箱
func validateEmail(fl validator.FieldLevel) bool {
	email := fl.Field().String()
	matched, _ := regexp.MatchString(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`, email)
	return matched
}

// validateURL 验证 URL
func validateURL(fl validator.FieldLevel) bool {
	url := fl.Field().String()
	matched, _ := regexp.MatchString(`^https?://[^\s/$.?#].[^\s]*$`, url)
	return matched
}

// validateUUID 验证 UUID
func validateUUID(fl validator.FieldLevel) bool {
	uuid := fl.Field().String()
	matched, _ := regexp.MatchString(`^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$`, uuid)
	return matched
}

// validateDateTime 验证日期时间
func validateDateTime(fl validator.FieldLevel) bool {
	datetime := fl.Field().String()
	_, err := time.Parse(time.RFC3339, datetime)
	return err == nil
}
