package validator

import (
	"fmt"
	"io/ioutil"
	"path/filepath"

	"gopkg.in/yaml.v3"
	"paas-platform/pkg/logger"
)

// ValidationRulesConfig 验证规则配置
type ValidationRulesConfig struct {
	Validation ValidationSettings           `yaml:"validation"`
	Rules      []*ValidationRule            `yaml:"rules"`
	Schemas    map[string]*JSONSchemaConfig `yaml:"schemas"`
}

// ValidationSettings 验证设置
type ValidationSettings struct {
	Enabled        bool     `yaml:"enabled"`
	StrictMode     bool     `yaml:"strict_mode"`
	LogValidation  bool     `yaml:"log_validation"`
	CacheResults   bool     `yaml:"cache_results"`
	MaxCacheSize   int      `yaml:"max_cache_size"`
	CacheTTL       int      `yaml:"cache_ttl"`
	SkipPaths      []string `yaml:"skip_paths"`
}

// JSONSchemaConfig JSON Schema 配置
type JSONSchemaConfig struct {
	Description string                 `yaml:"description"`
	Schema      map[string]interface{} `yaml:"schema"`
}

// RuleLoader 规则加载器
type RuleLoader struct {
	logger logger.Logger
}

// NewRuleLoader 创建规则加载器
func NewRuleLoader(logger logger.Logger) *RuleLoader {
	return &RuleLoader{
		logger: logger,
	}
}

// LoadFromFile 从文件加载验证规则
func (rl *RuleLoader) LoadFromFile(filePath string) (*ValidationRulesConfig, error) {
	rl.logger.Info("开始加载验证规则文件", "file", filePath)
	
	// 读取文件
	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		rl.logger.Error("读取验证规则文件失败", "file", filePath, "error", err)
		return nil, fmt.Errorf("读取验证规则文件失败: %w", err)
	}
	
	// 解析 YAML
	var config ValidationRulesConfig
	if err := yaml.Unmarshal(data, &config); err != nil {
		rl.logger.Error("解析验证规则文件失败", "file", filePath, "error", err)
		return nil, fmt.Errorf("解析验证规则文件失败: %w", err)
	}
	
	// 验证配置
	if err := rl.validateConfig(&config); err != nil {
		rl.logger.Error("验证规则配置无效", "file", filePath, "error", err)
		return nil, fmt.Errorf("验证规则配置无效: %w", err)
	}
	
	rl.logger.Info("验证规则文件加载成功", 
		"file", filePath,
		"rules_count", len(config.Rules),
		"schemas_count", len(config.Schemas))
	
	return &config, nil
}

// LoadFromDirectory 从目录加载验证规则
func (rl *RuleLoader) LoadFromDirectory(dirPath string) (*ValidationRulesConfig, error) {
	rl.logger.Info("开始加载验证规则目录", "dir", dirPath)
	
	// 读取目录中的所有 YAML 文件
	files, err := filepath.Glob(filepath.Join(dirPath, "*.yaml"))
	if err != nil {
		return nil, fmt.Errorf("读取验证规则目录失败: %w", err)
	}
	
	yamlFiles, err := filepath.Glob(filepath.Join(dirPath, "*.yml"))
	if err != nil {
		return nil, fmt.Errorf("读取验证规则目录失败: %w", err)
	}
	
	files = append(files, yamlFiles...)
	
	if len(files) == 0 {
		rl.logger.Warn("验证规则目录中没有找到 YAML 文件", "dir", dirPath)
		return &ValidationRulesConfig{}, nil
	}
	
	// 合并所有文件的配置
	mergedConfig := &ValidationRulesConfig{
		Rules:   make([]*ValidationRule, 0),
		Schemas: make(map[string]*JSONSchemaConfig),
	}
	
	for _, file := range files {
		config, err := rl.LoadFromFile(file)
		if err != nil {
			rl.logger.Error("加载验证规则文件失败", "file", file, "error", err)
			continue
		}
		
		// 合并规则
		mergedConfig.Rules = append(mergedConfig.Rules, config.Rules...)
		
		// 合并 Schema
		for name, schema := range config.Schemas {
			if _, exists := mergedConfig.Schemas[name]; exists {
				rl.logger.Warn("JSON Schema 名称冲突，将被覆盖", "name", name, "file", file)
			}
			mergedConfig.Schemas[name] = schema
		}
		
		// 使用第一个文件的验证设置
		if mergedConfig.Validation.Enabled == false && config.Validation.Enabled {
			mergedConfig.Validation = config.Validation
		}
	}
	
	rl.logger.Info("验证规则目录加载完成",
		"dir", dirPath,
		"files_count", len(files),
		"rules_count", len(mergedConfig.Rules),
		"schemas_count", len(mergedConfig.Schemas))
	
	return mergedConfig, nil
}

// validateConfig 验证配置
func (rl *RuleLoader) validateConfig(config *ValidationRulesConfig) error {
	// 验证规则
	for i, rule := range config.Rules {
		if err := rl.validateRule(rule, i); err != nil {
			return err
		}
	}
	
	// 验证 Schema
	for name, schema := range config.Schemas {
		if err := rl.validateSchema(name, schema); err != nil {
			return err
		}
	}
	
	return nil
}

// validateRule 验证单个规则
func (rl *RuleLoader) validateRule(rule *ValidationRule, index int) error {
	if rule.Path == "" {
		return fmt.Errorf("规则 %d: path 不能为空", index)
	}
	
	if rule.Method == "" {
		return fmt.Errorf("规则 %d: method 不能为空", index)
	}
	
	// 验证 HTTP 方法
	validMethods := map[string]bool{
		"GET": true, "POST": true, "PUT": true, "DELETE": true,
		"PATCH": true, "HEAD": true, "OPTIONS": true,
	}
	
	if !validMethods[rule.Method] {
		return fmt.Errorf("规则 %d: 无效的 HTTP 方法 %s", index, rule.Method)
	}
	
	// 验证参数规则
	for paramName, paramRule := range rule.QueryParams {
		if err := rl.validateParamRule(paramName, paramRule, fmt.Sprintf("规则 %d 查询参数", index)); err != nil {
			return err
		}
	}
	
	for paramName, paramRule := range rule.PathParams {
		if err := rl.validateParamRule(paramName, paramRule, fmt.Sprintf("规则 %d 路径参数", index)); err != nil {
			return err
		}
	}
	
	// 验证请求体规则
	if rule.Body != nil {
		if rule.Body.MaxSize < 0 {
			return fmt.Errorf("规则 %d: body.max_size 不能为负数", index)
		}
	}
	
	return nil
}

// validateParamRule 验证参数规则
func (rl *RuleLoader) validateParamRule(paramName string, rule ParamRule, context string) error {
	// 验证参数类型
	validTypes := map[string]bool{
		"string": true, "int": true, "float": true, "bool": true,
	}
	
	if rule.Type != "" && !validTypes[rule.Type] {
		return fmt.Errorf("%s %s: 无效的参数类型 %s", context, paramName, rule.Type)
	}
	
	// 验证长度限制
	if rule.MinLength < 0 {
		return fmt.Errorf("%s %s: min_length 不能为负数", context, paramName)
	}
	
	if rule.MaxLength < 0 {
		return fmt.Errorf("%s %s: max_length 不能为负数", context, paramName)
	}
	
	if rule.MinLength > 0 && rule.MaxLength > 0 && rule.MinLength > rule.MaxLength {
		return fmt.Errorf("%s %s: min_length 不能大于 max_length", context, paramName)
	}
	
	// 验证数值范围
	if rule.Min != 0 && rule.Max != 0 && rule.Min > rule.Max {
		return fmt.Errorf("%s %s: min 不能大于 max", context, paramName)
	}
	
	return nil
}

// validateSchema 验证 Schema
func (rl *RuleLoader) validateSchema(name string, schema *JSONSchemaConfig) error {
	if schema.Schema == nil {
		return fmt.Errorf("Schema %s: schema 不能为空", name)
	}
	
	// 验证 Schema 必须有 type 字段
	if _, exists := schema.Schema["type"]; !exists {
		return fmt.Errorf("Schema %s: 缺少 type 字段", name)
	}
	
	return nil
}

// ConvertToJSONSchemas 转换为 JSON Schema 格式
func (rl *RuleLoader) ConvertToJSONSchemas(schemas map[string]*JSONSchemaConfig) map[string]*JSONSchema {
	result := make(map[string]*JSONSchema)
	
	for name, config := range schemas {
		result[name] = &JSONSchema{
			Schema:      config.Schema,
			Description: config.Description,
		}
	}
	
	return result
}

// GetDefaultRulesPath 获取默认规则文件路径
func (rl *RuleLoader) GetDefaultRulesPath() string {
	return "configs/validation-rules.yaml"
}

// GetDefaultRulesDirectory 获取默认规则目录路径
func (rl *RuleLoader) GetDefaultRulesDirectory() string {
	return "configs/validation-rules"
}

// SaveToFile 保存验证规则到文件
func (rl *RuleLoader) SaveToFile(config *ValidationRulesConfig, filePath string) error {
	rl.logger.Info("保存验证规则到文件", "file", filePath)
	
	// 转换为 YAML
	data, err := yaml.Marshal(config)
	if err != nil {
		rl.logger.Error("序列化验证规则失败", "error", err)
		return fmt.Errorf("序列化验证规则失败: %w", err)
	}
	
	// 写入文件
	if err := ioutil.WriteFile(filePath, data, 0644); err != nil {
		rl.logger.Error("写入验证规则文件失败", "file", filePath, "error", err)
		return fmt.Errorf("写入验证规则文件失败: %w", err)
	}
	
	rl.logger.Info("验证规则文件保存成功", "file", filePath)
	return nil
}

// CreateSampleConfig 创建示例配置
func (rl *RuleLoader) CreateSampleConfig() *ValidationRulesConfig {
	return &ValidationRulesConfig{
		Validation: ValidationSettings{
			Enabled:       true,
			StrictMode:    false,
			LogValidation: true,
			CacheResults:  false,
			MaxCacheSize:  1000,
			CacheTTL:      300,
			SkipPaths: []string{
				"/health",
				"/ready",
				"/metrics",
			},
		},
		Rules: []*ValidationRule{
			{
				Path:   "/api/v1/example",
				Method: "POST",
				Headers: map[string]string{
					"Content-Type": "application/json",
				},
				QueryParams: map[string]ParamRule{
					"page": {
						Type:     "int",
						Required: false,
						Min:      1,
						Max:      1000,
						Default:  1,
					},
				},
				Body: &BodyRule{
					ContentType: "application/json",
					Required:    true,
					MaxSize:     1024,
					Schema:      "example_request",
				},
			},
		},
		Schemas: map[string]*JSONSchemaConfig{
			"example_request": {
				Description: "示例请求",
				Schema: map[string]interface{}{
					"type": "object",
					"required": []string{"name"},
					"properties": map[string]interface{}{
						"name": map[string]interface{}{
							"type":      "string",
							"minLength": 1,
							"maxLength": 100,
						},
					},
				},
			},
		},
	}
}
