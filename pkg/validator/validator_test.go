package validator

import (
	"bytes"
	"encoding/json"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"paas-platform/pkg/logger"
)

func TestAPIValidator_ValidateRequest(t *testing.T) {
	// 创建测试用的验证器
	testLogger := logger.NewTestLogger()
	validator := NewAPIValidator(testLogger)

	// 注册测试规则
	rule := &ValidationRule{
		Path:   "/api/v1/test",
		Method: "POST",
		Headers: map[string]string{
			"Content-Type": "required",
		},
		QueryParams: map[string]ParamRule{
			"page": {
				Type:     "int",
				Required: false,
				Min:      1,
				Max:      100,
				Default:  1,
			},
			"size": {
				Type:     "int",
				Required: false,
				Min:      1,
				Max:      50,
				Default:  10,
			},
		},
		PathParams: map[string]ParamRule{
			"id": {
				Type:     "string",
				Required: true,
				Pattern:  "^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$",
			},
		},
		Body: &BodyRule{
			ContentType: "application/json",
			Required:    true,
			MaxSize:     1024,
			Schema:      "test_request",
		},
	}

	err := validator.RegisterRule(rule)
	require.NoError(t, err)

	// 注册测试 Schema
	schema := &JSONSchema{
		Description: "测试请求",
		Schema: map[string]interface{}{
			"type": "object",
			"required": []interface{}{"name"},
			"properties": map[string]interface{}{
				"name": map[string]interface{}{
					"type":      "string",
					"minLength": 1,
					"maxLength": 100,
				},
				"age": map[string]interface{}{
					"type":    "integer",
					"minimum": 0,
					"maximum": 150,
				},
			},
		},
	}

	err = validator.RegisterSchema("test_request", schema)
	require.NoError(t, err)

	tests := []struct {
		name           string
		method         string
		path           string
		headers        map[string]string
		queryParams    map[string]string
		pathParams     map[string]string
		body           interface{}
		expectedValid  bool
		expectedErrors int
	}{
		{
			name:   "有效请求",
			method: "POST",
			path:   "/api/v1/test",
			headers: map[string]string{
				"Content-Type": "application/json",
			},
			queryParams: map[string]string{
				"page": "1",
				"size": "10",
			},
			pathParams: map[string]string{
				"id": "123e4567-e89b-12d3-a456-************",
			},
			body: map[string]interface{}{
				"name": "测试用户",
				"age":  25,
			},
			expectedValid:  true,
			expectedErrors: 0,
		},
		{
			name:   "缺少必需头部",
			method: "POST",
			path:   "/api/v1/test",
			headers: map[string]string{
				// 缺少 Content-Type
			},
			queryParams: map[string]string{
				"page": "1",
			},
			pathParams: map[string]string{
				"id": "123e4567-e89b-12d3-a456-************",
			},
			body: map[string]interface{}{
				"name": "测试用户",
			},
			expectedValid:  false,
			expectedErrors: 2, // 头部验证 + 请求体内容类型验证
		},
		{
			name:   "无效查询参数",
			method: "POST",
			path:   "/api/v1/test",
			headers: map[string]string{
				"Content-Type": "application/json",
			},
			queryParams: map[string]string{
				"page": "0", // 小于最小值
				"size": "100", // 大于最大值
			},
			pathParams: map[string]string{
				"id": "123e4567-e89b-12d3-a456-************",
			},
			body: map[string]interface{}{
				"name": "测试用户",
			},
			expectedValid:  false,
			expectedErrors: 2,
		},
		{
			name:   "无效路径参数",
			method: "POST",
			path:   "/api/v1/test",
			headers: map[string]string{
				"Content-Type": "application/json",
			},
			queryParams: map[string]string{
				"page": "1",
			},
			pathParams: map[string]string{
				"id": "invalid-uuid", // 无效的 UUID 格式
			},
			body: map[string]interface{}{
				"name": "测试用户",
			},
			expectedValid:  false,
			expectedErrors: 1,
		},
		{
			name:   "无效请求体",
			method: "POST",
			path:   "/api/v1/test",
			headers: map[string]string{
				"Content-Type": "application/json",
			},
			queryParams: map[string]string{
				"page": "1",
			},
			pathParams: map[string]string{
				"id": "123e4567-e89b-12d3-a456-************",
			},
			body: map[string]interface{}{
				// 缺少必需字段 name
				"age": 25,
			},
			expectedValid:  false,
			expectedErrors: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建测试请求
			var bodyBytes []byte
			if tt.body != nil {
				bodyBytes, _ = json.Marshal(tt.body)
			}

			req := httptest.NewRequest(tt.method, tt.path, bytes.NewBuffer(bodyBytes))
			
			// 设置头部
			for key, value := range tt.headers {
				req.Header.Set(key, value)
			}

			// 设置查询参数
			q := req.URL.Query()
			for key, value := range tt.queryParams {
				q.Add(key, value)
			}
			req.URL.RawQuery = q.Encode()

			// 创建 Gin 上下文
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			// 设置路径参数
			for key, value := range tt.pathParams {
				c.Params = append(c.Params, gin.Param{Key: key, Value: value})
			}

			// 执行验证
			result := validator.ValidateRequest(c)

			// 验证结果
			assert.Equal(t, tt.expectedValid, result.Valid, "验证结果不匹配")
			assert.Equal(t, tt.expectedErrors, len(result.Errors), "错误数量不匹配")

			if !result.Valid {
				t.Logf("验证错误: %+v", result.Errors)
			}
		})
	}
}

func TestAPIValidator_ValidateParamType(t *testing.T) {
	testLogger := logger.NewTestLogger()
	validator := NewAPIValidator(testLogger)

	tests := []struct {
		name      string
		value     string
		paramType string
		expectErr bool
	}{
		{"有效整数", "123", "int", false},
		{"无效整数", "abc", "int", true},
		{"有效浮点数", "123.45", "float", false},
		{"无效浮点数", "abc", "float", true},
		{"有效布尔值", "true", "bool", false},
		{"无效布尔值", "maybe", "bool", true},
		{"字符串类型", "任何值", "string", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rule := ParamRule{Type: tt.paramType}
			err := validator.validateParamType("test", tt.value, rule, "query")
			
			if tt.expectErr {
				assert.NotNil(t, err, "期望验证失败")
			} else {
				assert.Nil(t, err, "期望验证成功")
			}
		})
	}
}

func TestAPIValidator_ValidateParamRange(t *testing.T) {
	testLogger := logger.NewTestLogger()
	validator := NewAPIValidator(testLogger)

	tests := []struct {
		name      string
		value     string
		rule      ParamRule
		expectErr bool
	}{
		{
			name:  "整数在范围内",
			value: "50",
			rule:  ParamRule{Type: "int", Min: 1, Max: 100},
			expectErr: false,
		},
		{
			name:  "整数小于最小值",
			value: "0",
			rule:  ParamRule{Type: "int", Min: 1, Max: 100},
			expectErr: true,
		},
		{
			name:  "整数大于最大值",
			value: "101",
			rule:  ParamRule{Type: "int", Min: 1, Max: 100},
			expectErr: true,
		},
		{
			name:  "浮点数在范围内",
			value: "50.5",
			rule:  ParamRule{Type: "float", Min: 1.0, Max: 100.0},
			expectErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validator.validateParamRange("test", tt.value, tt.rule, "query")
			
			if tt.expectErr {
				assert.NotNil(t, err, "期望验证失败")
			} else {
				assert.Nil(t, err, "期望验证成功")
			}
		})
	}
}

func TestAPIValidator_ValidateParamEnum(t *testing.T) {
	testLogger := logger.NewTestLogger()
	validator := NewAPIValidator(testLogger)

	rule := ParamRule{
		Enum: []string{"active", "inactive", "pending"},
	}

	tests := []struct {
		name      string
		value     string
		expectErr bool
	}{
		{"有效枚举值", "active", false},
		{"无效枚举值", "unknown", true},
		{"空值", "", false}, // 空值应该通过枚举验证
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validator.validateParamEnum("test", tt.value, rule, "query")
			
			if tt.expectErr {
				assert.NotNil(t, err, "期望验证失败")
			} else {
				assert.Nil(t, err, "期望验证成功")
			}
		})
	}
}
