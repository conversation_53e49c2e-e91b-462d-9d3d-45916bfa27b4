package retry

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"paas-platform/pkg/logger"
)

func TestRetrier_Execute_Success(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	config.Name = "test-retrier"
	
	retrier := NewRetrier(config, logger)

	// 测试成功执行
	err := retrier.Execute(context.Background(), func() error {
		return nil
	})
	require.NoError(t, err)

	// 检查统计信息
	stats := retrier.GetStats()
	assert.Equal(t, 1, stats.TotalAttempts)
	assert.Equal(t, 1, stats.SuccessAttempts)
	assert.Equal(t, 0, stats.FailureAttempts)
}

func TestRetrier_Execute_RetryAndSuccess(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	config.Name = "test-retrier-retry"
	config.MaxAttempts = 3
	config.InitialInterval = 10 * time.Millisecond
	
	retrier := NewRetrier(config, logger)

	attempt := 0
	err := retrier.Execute(context.Background(), func() error {
		attempt++
		if attempt < 3 {
			return errors.New("temporary error")
		}
		return nil
	})

	require.NoError(t, err)
	assert.Equal(t, 3, attempt)

	// 检查统计信息
	stats := retrier.GetStats()
	assert.Equal(t, 3, stats.TotalAttempts)
	assert.Equal(t, 1, stats.SuccessAttempts)
	assert.Equal(t, 2, stats.FailureAttempts)
}

func TestRetrier_Execute_MaxAttemptsReached(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	config.Name = "test-retrier-max"
	config.MaxAttempts = 3
	config.InitialInterval = 10 * time.Millisecond
	
	retrier := NewRetrier(config, logger)

	attempt := 0
	err := retrier.Execute(context.Background(), func() error {
		attempt++
		return errors.New("persistent error")
	})

	require.Error(t, err)
	assert.Contains(t, err.Error(), "重试失败，已达到最大重试次数")
	assert.Equal(t, 3, attempt)

	// 检查统计信息
	stats := retrier.GetStats()
	assert.Equal(t, 3, stats.TotalAttempts)
	assert.Equal(t, 0, stats.SuccessAttempts)
	assert.Equal(t, 3, stats.FailureAttempts)
}

func TestRetrier_ExecuteWithResult(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	config.Name = "test-retrier-result"
	config.MaxAttempts = 3
	config.InitialInterval = 10 * time.Millisecond
	
	retrier := NewRetrier(config, logger)

	attempt := 0
	result, err := retrier.ExecuteWithResult(context.Background(), func() (interface{}, error) {
		attempt++
		if attempt < 2 {
			return nil, errors.New("temporary error")
		}
		return "success result", nil
	})

	require.NoError(t, err)
	assert.Equal(t, "success result", result)
	assert.Equal(t, 2, attempt)
}

func TestRetrier_FixedStrategy(t *testing.T) {
	logger := logger.NewTestLogger()
	config := FixedConfig()
	config.Name = "test-retrier-fixed"
	config.MaxAttempts = 3
	config.InitialInterval = 50 * time.Millisecond
	
	retrier := NewRetrier(config, logger)

	start := time.Now()
	attempt := 0
	
	retrier.Execute(context.Background(), func() error {
		attempt++
		if attempt < 3 {
			return errors.New("temporary error")
		}
		return nil
	})

	duration := time.Since(start)
	
	// 固定策略应该有大约 100ms 的延迟（2次重试 * 50ms）
	assert.True(t, duration >= 100*time.Millisecond)
	assert.True(t, duration < 200*time.Millisecond) // 给一些容差
}

func TestRetrier_LinearStrategy(t *testing.T) {
	logger := logger.NewTestLogger()
	config := LinearConfig()
	config.Name = "test-retrier-linear"
	config.MaxAttempts = 3
	config.InitialInterval = 20 * time.Millisecond
	
	retrier := NewRetrier(config, logger)

	start := time.Now()
	attempt := 0
	
	retrier.Execute(context.Background(), func() error {
		attempt++
		if attempt < 3 {
			return errors.New("temporary error")
		}
		return nil
	})

	duration := time.Since(start)
	
	// 线性策略应该有 20ms + 40ms = 60ms 的延迟
	assert.True(t, duration >= 50*time.Millisecond) // 给一些容差
	assert.True(t, duration < 150*time.Millisecond)
}

func TestRetrier_ExponentialStrategy(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultConfig() // 默认是指数退避
	config.Name = "test-retrier-exponential"
	config.MaxAttempts = 4
	config.InitialInterval = 10 * time.Millisecond
	config.Multiplier = 2.0
	config.Jitter = false // 禁用抖动以便测试
	
	retrier := NewRetrier(config, logger)

	start := time.Now()
	attempt := 0
	
	retrier.Execute(context.Background(), func() error {
		attempt++
		if attempt < 4 {
			return errors.New("temporary error")
		}
		return nil
	})

	duration := time.Since(start)
	
	// 指数退避：10ms + 20ms + 40ms = 70ms
	assert.True(t, duration >= 70*time.Millisecond)
	assert.True(t, duration < 140*time.Millisecond)
}

func TestRetrier_CustomStrategy(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	config.Name = "test-retrier-custom"
	config.MaxAttempts = 3
	config.Strategy = StrategyCustom
	config.CustomStrategy = func(attempt int, config Config) time.Duration {
		// 自定义策略：每次延迟都是 30ms
		return 30 * time.Millisecond
	}
	
	retrier := NewRetrier(config, logger)

	start := time.Now()
	attempt := 0
	
	retrier.Execute(context.Background(), func() error {
		attempt++
		if attempt < 3 {
			return errors.New("temporary error")
		}
		return nil
	})

	duration := time.Since(start)
	
	// 自定义策略：30ms + 30ms = 60ms
	assert.True(t, duration >= 50*time.Millisecond) // 给一些容差
	assert.True(t, duration < 150*time.Millisecond)
}

func TestRetrier_ContextTimeout(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	config.Name = "test-retrier-timeout"
	config.MaxAttempts = 5
	config.InitialInterval = 100 * time.Millisecond
	
	retrier := NewRetrier(config, logger)

	// 设置较短的超时时间
	ctx, cancel := context.WithTimeout(context.Background(), 150*time.Millisecond)
	defer cancel()

	err := retrier.Execute(ctx, func() error {
		return errors.New("persistent error")
	})

	require.Error(t, err)
	assert.True(t, errors.Is(err, context.DeadlineExceeded))
}

func TestRetrier_ContextCancellation(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	config.Name = "test-retrier-cancel"
	config.MaxAttempts = 5
	config.InitialInterval = 100 * time.Millisecond
	
	retrier := NewRetrier(config, logger)

	ctx, cancel := context.WithCancel(context.Background())
	
	// 在第一次重试后取消上下文
	go func() {
		time.Sleep(50 * time.Millisecond)
		cancel()
	}()

	err := retrier.Execute(ctx, func() error {
		return errors.New("persistent error")
	})

	require.Error(t, err)
	assert.True(t, errors.Is(err, context.Canceled))
}

func TestRetrier_PerAttemptTimeout(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	config.Name = "test-retrier-per-attempt"
	config.MaxAttempts = 2
	config.InitialInterval = 10 * time.Millisecond
	config.PerAttemptTimeout = 30 * time.Millisecond

	retrier := NewRetrier(config, logger)

	// 简化测试：只测试配置是否正确设置
	assert.Equal(t, 30*time.Millisecond, config.PerAttemptTimeout)
	assert.Equal(t, 2, config.MaxAttempts)

	// 执行一个简单的成功测试
	err := retrier.Execute(context.Background(), func() error {
		return nil
	})
	require.NoError(t, err)
}

func TestRetrier_CustomRetryCondition(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	config.Name = "test-retrier-condition"
	config.MaxAttempts = 5
	config.InitialInterval = 10 * time.Millisecond
	config.RetryIf = func(err error, attempt int) bool {
		// 只重试包含 "retryable" 的错误
		return err != nil && err.Error() == "retryable error"
	}
	
	retrier := NewRetrier(config, logger)

	// 测试可重试错误
	attempt := 0
	err := retrier.Execute(context.Background(), func() error {
		attempt++
		if attempt < 3 {
			return errors.New("retryable error")
		}
		return nil
	})
	require.NoError(t, err)
	assert.Equal(t, 3, attempt)

	// 测试不可重试错误
	attempt = 0
	err = retrier.Execute(context.Background(), func() error {
		attempt++
		return errors.New("non-retryable error")
	})
	require.Error(t, err)
	assert.Equal(t, 1, attempt) // 不应该重试
}

func TestRetrier_RetryableErrors(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	config.Name = "test-retrier-retryable"
	config.MaxAttempts = 3
	config.InitialInterval = 10 * time.Millisecond
	config.RetryableErrors = []string{"network error", "timeout error"}
	
	retrier := NewRetrier(config, logger)

	// 测试可重试错误
	attempt := 0
	err := retrier.Execute(context.Background(), func() error {
		attempt++
		if attempt < 2 {
			return errors.New("network error")
		}
		return nil
	})
	require.NoError(t, err)
	assert.Equal(t, 2, attempt)

	// 测试不可重试错误
	attempt = 0
	err = retrier.Execute(context.Background(), func() error {
		attempt++
		return errors.New("validation error")
	})
	require.Error(t, err)
	assert.Equal(t, 1, attempt) // 不应该重试
}

func TestRetrier_NonRetryableErrors(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	config.Name = "test-retrier-non-retryable"
	config.MaxAttempts = 3
	config.InitialInterval = 10 * time.Millisecond
	config.NonRetryableErrors = []string{"validation error", "auth error"}
	
	retrier := NewRetrier(config, logger)

	// 测试不可重试错误
	attempt := 0
	err := retrier.Execute(context.Background(), func() error {
		attempt++
		return errors.New("validation error")
	})
	require.Error(t, err)
	assert.Equal(t, 1, attempt) // 不应该重试

	// 测试可重试错误
	attempt = 0
	err = retrier.Execute(context.Background(), func() error {
		attempt++
		if attempt < 2 {
			return errors.New("network error")
		}
		return nil
	})
	require.NoError(t, err)
	assert.Equal(t, 2, attempt)
}

func TestRetrier_OnRetryCallback(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	config.Name = "test-retrier-callback"
	config.MaxAttempts = 3
	config.InitialInterval = 10 * time.Millisecond
	
	var callbackCalls []int
	config.OnRetry = func(attempt int, err error, nextDelay time.Duration) {
		callbackCalls = append(callbackCalls, attempt)
	}
	
	retrier := NewRetrier(config, logger)

	attempt := 0
	retrier.Execute(context.Background(), func() error {
		attempt++
		if attempt < 3 {
			return errors.New("temporary error")
		}
		return nil
	})

	// 应该有2次回调（第1次和第2次失败后）
	assert.Equal(t, []int{1, 2}, callbackCalls)
}

func TestRetrier_Stats(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	config.Name = "test-retrier-stats"
	config.MaxAttempts = 4
	config.InitialInterval = 10 * time.Millisecond
	
	retrier := NewRetrier(config, logger)

	// 执行一些成功和失败的重试
	retrier.Execute(context.Background(), func() error {
		return nil // 立即成功
	})

	attempt := 0
	retrier.Execute(context.Background(), func() error {
		attempt++
		if attempt < 3 {
			return errors.New("temporary error")
		}
		return nil // 第3次成功
	})

	retrier.Execute(context.Background(), func() error {
		return errors.New("persistent error") // 始终失败
	})

	// 检查统计信息
	stats := retrier.GetStats()
	assert.Equal(t, "test-retrier-stats", stats.Name)
	assert.Equal(t, 8, stats.TotalAttempts)  // 1 + 3 + 4
	assert.Equal(t, 2, stats.SuccessAttempts) // 2次成功的执行
	assert.Equal(t, 6, stats.FailureAttempts) // 6次失败的尝试
	assert.True(t, stats.TotalDuration > 0)
	assert.True(t, stats.AverageDelay > 0)
}

func TestRetrier_Reset(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	config.Name = "test-retrier-reset"
	
	retrier := NewRetrier(config, logger)

	// 执行一些操作
	retrier.Execute(context.Background(), func() error {
		return errors.New("error")
	})

	// 检查统计信息
	stats := retrier.GetStats()
	assert.True(t, stats.TotalAttempts > 0)

	// 重置
	retrier.Reset()

	// 检查统计信息已重置
	stats = retrier.GetStats()
	assert.Equal(t, 0, stats.TotalAttempts)
	assert.Equal(t, 0, stats.SuccessAttempts)
	assert.Equal(t, 0, stats.FailureAttempts)
	assert.Equal(t, time.Duration(0), stats.TotalDuration)
}

func TestRetrier_Panic(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	config.Name = "test-retrier-panic"
	config.MaxAttempts = 2
	config.InitialInterval = 10 * time.Millisecond
	
	retrier := NewRetrier(config, logger)

	// 测试 panic 处理
	err := retrier.Execute(context.Background(), func() error {
		panic("test panic")
	})

	require.Error(t, err)
	assert.Contains(t, err.Error(), "panic")
}
