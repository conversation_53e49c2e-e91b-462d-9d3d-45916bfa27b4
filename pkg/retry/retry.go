package retry

import (
	"context"
	"errors"
	"fmt"
	"math"
	"math/rand"
	"time"

	"paas-platform/pkg/logger"
)

// Retrier 重试器接口
type Retrier interface {
	Execute(ctx context.Context, fn func() error) error
	ExecuteWithResult(ctx context.Context, fn func() (interface{}, error)) (interface{}, error)
	GetStats() *Stats
	Reset()
}

// Strategy 重试策略
type Strategy string

const (
	StrategyFixed       Strategy = "fixed"        // 固定间隔
	StrategyLinear      Strategy = "linear"       // 线性递增
	StrategyExponential Strategy = "exponential"  // 指数退避
	StrategyRandom      Strategy = "random"       // 随机间隔
	StrategyCustom      Strategy = "custom"       // 自定义策略
)

// Config 重试配置
type Config struct {
	Name            string                `json:"name"`              // 重试器名称
	MaxAttempts     int                   `json:"max_attempts"`      // 最大重试次数
	InitialInterval time.Duration         `json:"initial_interval"`  // 初始间隔
	MaxInterval     time.Duration         `json:"max_interval"`      // 最大间隔
	Multiplier      float64               `json:"multiplier"`        // 乘数（指数退避）
	RandomFactor    float64               `json:"random_factor"`     // 随机因子
	Strategy        Strategy              `json:"strategy"`          // 重试策略
	RetryIf         RetryCondition        `json:"-"`                 // 重试条件函数
	OnRetry         OnRetryCallback       `json:"-"`                 // 重试回调函数
	CustomStrategy  CustomStrategyFunc    `json:"-"`                 // 自定义策略函数
	Timeout         time.Duration         `json:"timeout"`           // 总超时时间
	PerAttemptTimeout time.Duration       `json:"per_attempt_timeout"` // 单次尝试超时
	Jitter          bool                  `json:"jitter"`            // 是否启用抖动
	StopOnSuccess   bool                  `json:"stop_on_success"`   // 成功后停止
	RetryableErrors []string              `json:"retryable_errors"`  // 可重试的错误类型
	NonRetryableErrors []string           `json:"non_retryable_errors"` // 不可重试的错误类型
}

// RetryCondition 重试条件函数类型
type RetryCondition func(err error, attempt int) bool

// OnRetryCallback 重试回调函数类型
type OnRetryCallback func(attempt int, err error, nextDelay time.Duration)

// CustomStrategyFunc 自定义策略函数类型
type CustomStrategyFunc func(attempt int, config Config) time.Duration

// Stats 重试统计信息
type Stats struct {
	Name            string        `json:"name"`             // 重试器名称
	TotalAttempts   int           `json:"total_attempts"`   // 总尝试次数
	SuccessAttempts int           `json:"success_attempts"` // 成功次数
	FailureAttempts int           `json:"failure_attempts"` // 失败次数
	TotalDuration   time.Duration `json:"total_duration"`   // 总耗时
	AverageDelay    time.Duration `json:"average_delay"`    // 平均延迟
	LastError       string        `json:"last_error"`       // 最后错误
	LastSuccess     time.Time     `json:"last_success"`     // 最后成功时间
	LastFailure     time.Time     `json:"last_failure"`     // 最后失败时间
}

// retrier 重试器实现
type retrier struct {
	config Config
	stats  Stats
	logger logger.Logger
}

// NewRetrier 创建重试器
func NewRetrier(config Config, logger logger.Logger) Retrier {
	r := &retrier{
		config: config,
		logger: logger,
		stats: Stats{
			Name: config.Name,
		},
	}

	// 设置默认值
	if r.config.MaxAttempts <= 0 {
		r.config.MaxAttempts = 3
	}
	if r.config.InitialInterval <= 0 {
		r.config.InitialInterval = 1 * time.Second
	}
	if r.config.MaxInterval <= 0 {
		r.config.MaxInterval = 30 * time.Second
	}
	if r.config.Multiplier <= 0 {
		r.config.Multiplier = 2.0
	}
	if r.config.RandomFactor < 0 || r.config.RandomFactor > 1 {
		r.config.RandomFactor = 0.1
	}
	if r.config.Strategy == "" {
		r.config.Strategy = StrategyExponential
	}
	if r.config.RetryIf == nil {
		r.config.RetryIf = r.defaultRetryCondition
	}

	return r
}

// Execute 执行函数（无返回值）
func (r *retrier) Execute(ctx context.Context, fn func() error) error {
	_, err := r.ExecuteWithResult(ctx, func() (interface{}, error) {
		return nil, fn()
	})
	return err
}

// ExecuteWithResult 执行函数（有返回值）
func (r *retrier) ExecuteWithResult(ctx context.Context, fn func() (interface{}, error)) (interface{}, error) {
	start := time.Now()
	var lastErr error
	var result interface{}

	// 设置总超时
	if r.config.Timeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, r.config.Timeout)
		defer cancel()
	}

	for attempt := 1; attempt <= r.config.MaxAttempts; attempt++ {
		r.stats.TotalAttempts++

		// 检查上下文是否已取消
		select {
		case <-ctx.Done():
			r.stats.FailureAttempts++
			r.stats.LastError = ctx.Err().Error()
			r.stats.LastFailure = time.Now()
			return result, ctx.Err()
		default:
		}

		// 执行函数
		attemptStart := time.Now()
		attemptCtx := ctx
		
		// 设置单次尝试超时
		if r.config.PerAttemptTimeout > 0 {
			var cancel context.CancelFunc
			attemptCtx, cancel = context.WithTimeout(ctx, r.config.PerAttemptTimeout)
			defer cancel()
		}

		result, lastErr = r.executeAttempt(attemptCtx, fn)
		attemptDuration := time.Since(attemptStart)

		if lastErr == nil {
			// 成功
			r.stats.SuccessAttempts++
			r.stats.TotalDuration += time.Since(start)
			r.stats.LastSuccess = time.Now()
			
			r.logger.Debug("重试成功",
				"name", r.config.Name,
				"attempt", attempt,
				"duration", attemptDuration)
			
			return result, nil
		}

		// 失败处理
		r.stats.FailureAttempts++
		r.stats.LastError = lastErr.Error()
		r.stats.LastFailure = time.Now()

		// 检查是否应该重试
		if attempt >= r.config.MaxAttempts || !r.config.RetryIf(lastErr, attempt) {
			break
		}

		// 计算延迟时间
		delay := r.calculateDelay(attempt)
		
		// 执行重试回调
		if r.config.OnRetry != nil {
			r.config.OnRetry(attempt, lastErr, delay)
		}

		r.logger.Warn("重试失败，准备下次重试",
			"name", r.config.Name,
			"attempt", attempt,
			"error", lastErr.Error(),
			"next_delay", delay,
			"duration", attemptDuration)

		// 等待延迟时间
		select {
		case <-ctx.Done():
			r.stats.TotalDuration += time.Since(start)
			return result, ctx.Err()
		case <-time.After(delay):
		}
	}

	r.stats.TotalDuration += time.Since(start)
	
	r.logger.Error("重试最终失败",
		"name", r.config.Name,
		"max_attempts", r.config.MaxAttempts,
		"total_duration", r.stats.TotalDuration,
		"last_error", lastErr.Error())

	return result, fmt.Errorf("重试失败，已达到最大重试次数 %d: %w", r.config.MaxAttempts, lastErr)
}

// executeAttempt 执行单次尝试
func (r *retrier) executeAttempt(ctx context.Context, fn func() (interface{}, error)) (interface{}, error) {
	done := make(chan struct{})
	var result interface{}
	var err error

	go func() {
		defer close(done)
		defer func() {
			if r := recover(); r != nil {
				err = fmt.Errorf("panic: %v", r)
			}
		}()
		result, err = fn()
	}()

	select {
	case <-ctx.Done():
		return result, ctx.Err()
	case <-done:
		return result, err
	}
}

// calculateDelay 计算延迟时间
func (r *retrier) calculateDelay(attempt int) time.Duration {
	var delay time.Duration

	switch r.config.Strategy {
	case StrategyFixed:
		delay = r.config.InitialInterval
	case StrategyLinear:
		delay = time.Duration(attempt) * r.config.InitialInterval
	case StrategyExponential:
		delay = time.Duration(float64(r.config.InitialInterval) * math.Pow(r.config.Multiplier, float64(attempt-1)))
	case StrategyRandom:
		delay = time.Duration(rand.Int63n(int64(r.config.MaxInterval)))
	case StrategyCustom:
		if r.config.CustomStrategy != nil {
			delay = r.config.CustomStrategy(attempt, r.config)
		} else {
			delay = r.config.InitialInterval
		}
	default:
		delay = r.config.InitialInterval
	}

	// 应用最大间隔限制
	if delay > r.config.MaxInterval {
		delay = r.config.MaxInterval
	}

	// 应用抖动
	if r.config.Jitter && r.config.RandomFactor > 0 {
		jitter := time.Duration(float64(delay) * r.config.RandomFactor * (rand.Float64()*2 - 1))
		delay += jitter
		if delay < 0 {
			delay = r.config.InitialInterval
		}
	}

	return delay
}

// defaultRetryCondition 默认重试条件
func (r *retrier) defaultRetryCondition(err error, attempt int) bool {
	if err == nil {
		return false
	}

	// 检查不可重试的错误
	for _, nonRetryableError := range r.config.NonRetryableErrors {
		if errors.Is(err, errors.New(nonRetryableError)) || 
		   fmt.Sprintf("%v", err) == nonRetryableError {
			return false
		}
	}

	// 检查可重试的错误
	if len(r.config.RetryableErrors) > 0 {
		for _, retryableError := range r.config.RetryableErrors {
			if errors.Is(err, errors.New(retryableError)) || 
			   fmt.Sprintf("%v", err) == retryableError {
				return true
			}
		}
		return false
	}

	// 默认情况下，除了上下文错误外都可以重试
	return !errors.Is(err, context.Canceled) && !errors.Is(err, context.DeadlineExceeded)
}

// GetStats 获取统计信息
func (r *retrier) GetStats() *Stats {
	stats := r.stats
	if stats.TotalAttempts > 0 {
		stats.AverageDelay = stats.TotalDuration / time.Duration(stats.TotalAttempts)
	}
	return &stats
}

// Reset 重置统计信息
func (r *retrier) Reset() {
	r.stats = Stats{
		Name: r.config.Name,
	}
}

// DefaultConfig 默认配置
func DefaultConfig() Config {
	return Config{
		Name:              "default",
		MaxAttempts:       3,
		InitialInterval:   1 * time.Second,
		MaxInterval:       30 * time.Second,
		Multiplier:        2.0,
		RandomFactor:      0.1,
		Strategy:          StrategyExponential,
		Jitter:            true,
		StopOnSuccess:     true,
		PerAttemptTimeout: 10 * time.Second,
	}
}

// LinearConfig 线性重试配置
func LinearConfig() Config {
	config := DefaultConfig()
	config.Strategy = StrategyLinear
	config.Multiplier = 1.0
	return config
}

// FixedConfig 固定间隔重试配置
func FixedConfig() Config {
	config := DefaultConfig()
	config.Strategy = StrategyFixed
	config.Jitter = false
	return config
}

// FastRetryConfig 快速重试配置
func FastRetryConfig() Config {
	config := DefaultConfig()
	config.MaxAttempts = 5
	config.InitialInterval = 100 * time.Millisecond
	config.MaxInterval = 2 * time.Second
	config.Multiplier = 1.5
	return config
}

// SlowRetryConfig 慢速重试配置
func SlowRetryConfig() Config {
	config := DefaultConfig()
	config.MaxAttempts = 3
	config.InitialInterval = 5 * time.Second
	config.MaxInterval = 60 * time.Second
	config.Multiplier = 3.0
	return config
}
