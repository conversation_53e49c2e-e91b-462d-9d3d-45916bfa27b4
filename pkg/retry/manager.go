package retry

import (
	"context"
	"sync"
	"time"

	"paas-platform/pkg/logger"
)

// Manager 重试管理器
type Manager struct {
	retriers map[string]Retrier
	mutex    sync.RWMutex
	logger   logger.Logger
	config   ManagerConfig
}

// ManagerConfig 管理器配置
type ManagerConfig struct {
	DefaultConfig       Config        `json:"default_config"`        // 默认重试配置
	EnableMetrics       bool          `json:"enable_metrics"`        // 启用指标收集
	MetricsInterval     time.Duration `json:"metrics_interval"`      // 指标收集间隔
	EnableHealthCheck   bool          `json:"enable_health_check"`   // 启用健康检查
	HealthCheckInterval time.Duration `json:"health_check_interval"` // 健康检查间隔
	AutoCleanup         bool          `json:"auto_cleanup"`          // 自动清理
	CleanupInterval     time.Duration `json:"cleanup_interval"`      // 清理间隔
	MaxRetriers         int           `json:"max_retriers"`          // 最大重试器数量
}

// RetrierInfo 重试器信息
type RetrierInfo struct {
	Name       string    `json:"name"`        // 名称
	Stats      *Stats    `json:"stats"`       // 统计信息
	Config     Config    `json:"config"`      // 配置
	CreatedAt  time.Time `json:"created_at"`  // 创建时间
	LastUsed   time.Time `json:"last_used"`   // 最后使用时间
	UsageCount int64     `json:"usage_count"` // 使用次数
}

// HealthStatus 健康状态
type HealthStatus struct {
	Healthy        bool                     `json:"healthy"`         // 是否健康
	TotalRetriers  int                      `json:"total_retriers"`  // 总重试器数
	ActiveRetriers int                      `json:"active_retriers"` // 活跃重试器数
	Retriers       map[string]*RetrierInfo  `json:"retriers"`        // 重试器信息
	CheckTime      time.Time                `json:"check_time"`      // 检查时间
}

// NewManager 创建重试管理器
func NewManager(config ManagerConfig, logger logger.Logger) *Manager {
	m := &Manager{
		retriers: make(map[string]Retrier),
		logger:   logger,
		config:   config,
	}

	// 设置默认值
	if m.config.MetricsInterval <= 0 {
		m.config.MetricsInterval = 30 * time.Second
	}
	if m.config.HealthCheckInterval <= 0 {
		m.config.HealthCheckInterval = 60 * time.Second
	}
	if m.config.CleanupInterval <= 0 {
		m.config.CleanupInterval = 300 * time.Second
	}
	if m.config.MaxRetriers <= 0 {
		m.config.MaxRetriers = 1000
	}

	// 启动后台任务
	if m.config.EnableMetrics {
		go m.metricsCollector()
	}
	if m.config.EnableHealthCheck {
		go m.healthChecker()
	}
	if m.config.AutoCleanup {
		go m.cleaner()
	}

	return m
}

// GetOrCreate 获取或创建重试器
func (m *Manager) GetOrCreate(name string, config *Config) Retrier {
	m.mutex.RLock()
	if retrier, exists := m.retriers[name]; exists {
		m.mutex.RUnlock()
		return retrier
	}
	m.mutex.RUnlock()

	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 双重检查
	if retrier, exists := m.retriers[name]; exists {
		return retrier
	}

	// 检查重试器数量限制
	if len(m.retriers) >= m.config.MaxRetriers {
		m.logger.Warn("重试器数量已达上限", "max", m.config.MaxRetriers, "current", len(m.retriers))
		m.cleanupInactive()
	}

	// 使用提供的配置或默认配置
	var retrierConfig Config
	if config != nil {
		retrierConfig = *config
	} else {
		retrierConfig = m.config.DefaultConfig
	}
	retrierConfig.Name = name

	// 创建重试器
	retrier := NewRetrier(retrierConfig, m.logger)
	m.retriers[name] = retrier

	m.logger.Info("创建重试器", "name", name, "config", retrierConfig)
	return retrier
}

// Get 获取重试器
func (m *Manager) Get(name string) (Retrier, bool) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	retrier, exists := m.retriers[name]
	return retrier, exists
}

// Remove 移除重试器
func (m *Manager) Remove(name string) bool {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if _, exists := m.retriers[name]; exists {
		delete(m.retriers, name)
		m.logger.Info("移除重试器", "name", name)
		return true
	}
	return false
}

// List 列出所有重试器
func (m *Manager) List() []string {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	names := make([]string, 0, len(m.retriers))
	for name := range m.retriers {
		names = append(names, name)
	}
	return names
}

// GetStats 获取所有重试器统计信息
func (m *Manager) GetStats() map[string]*Stats {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	stats := make(map[string]*Stats)
	for name, retrier := range m.retriers {
		stats[name] = retrier.GetStats()
	}
	return stats
}

// GetHealthStatus 获取健康状态
func (m *Manager) GetHealthStatus() *HealthStatus {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	status := &HealthStatus{
		Healthy:        true,
		TotalRetriers:  len(m.retriers),
		ActiveRetriers: 0,
		Retriers:       make(map[string]*RetrierInfo),
		CheckTime:      time.Now(),
	}

	for name, retrier := range m.retriers {
		stats := retrier.GetStats()
		info := &RetrierInfo{
			Name:      name,
			Stats:     stats,
			CreatedAt: time.Now(), // 简化实现
			LastUsed:  time.Now(), // 简化实现
		}

		if stats.TotalAttempts > 0 {
			status.ActiveRetriers++
		}

		status.Retriers[name] = info
	}

	return status
}

// Execute 执行函数（通过指定重试器）
func (m *Manager) Execute(ctx context.Context, retrierName string, fn func() error) error {
	retrier := m.GetOrCreate(retrierName, nil)
	return retrier.Execute(ctx, fn)
}

// ExecuteWithResult 执行函数（有返回值）
func (m *Manager) ExecuteWithResult(ctx context.Context, retrierName string, fn func() (interface{}, error)) (interface{}, error) {
	retrier := m.GetOrCreate(retrierName, nil)
	return retrier.ExecuteWithResult(ctx, fn)
}

// ExecuteWithConfig 使用指定配置执行函数
func (m *Manager) ExecuteWithConfig(ctx context.Context, retrierName string, config Config, fn func() error) error {
	retrier := m.GetOrCreate(retrierName, &config)
	return retrier.Execute(ctx, fn)
}

// ExecuteWithConfigAndResult 使用指定配置执行函数（有返回值）
func (m *Manager) ExecuteWithConfigAndResult(ctx context.Context, retrierName string, config Config, fn func() (interface{}, error)) (interface{}, error) {
	retrier := m.GetOrCreate(retrierName, &config)
	return retrier.ExecuteWithResult(ctx, fn)
}

// ResetAll 重置所有重试器
func (m *Manager) ResetAll() {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	for name, retrier := range m.retriers {
		retrier.Reset()
		m.logger.Info("重置重试器", "name", name)
	}
}

// metricsCollector 指标收集器
func (m *Manager) metricsCollector() {
	ticker := time.NewTicker(m.config.MetricsInterval)
	defer ticker.Stop()

	for range ticker.C {
		m.collectMetrics()
	}
}

// collectMetrics 收集指标
func (m *Manager) collectMetrics() {
	stats := m.GetStats()
	
	totalRetriers := len(stats)
	activeRetriers := 0
	totalAttempts := 0
	totalSuccesses := 0
	totalFailures := 0

	for _, stat := range stats {
		if stat.TotalAttempts > 0 {
			activeRetriers++
		}
		totalAttempts += stat.TotalAttempts
		totalSuccesses += stat.SuccessAttempts
		totalFailures += stat.FailureAttempts
	}

	m.logger.Debug("重试器指标收集",
		"total_retriers", totalRetriers,
		"active_retriers", activeRetriers,
		"total_attempts", totalAttempts,
		"total_successes", totalSuccesses,
		"total_failures", totalFailures)
}

// healthChecker 健康检查器
func (m *Manager) healthChecker() {
	ticker := time.NewTicker(m.config.HealthCheckInterval)
	defer ticker.Stop()

	for range ticker.C {
		m.performHealthCheck()
	}
}

// performHealthCheck 执行健康检查
func (m *Manager) performHealthCheck() {
	status := m.GetHealthStatus()
	
	m.logger.Debug("重试器健康检查",
		"total_retriers", status.TotalRetriers,
		"active_retriers", status.ActiveRetriers,
		"healthy", status.Healthy)
}

// cleaner 清理器
func (m *Manager) cleaner() {
	ticker := time.NewTicker(m.config.CleanupInterval)
	defer ticker.Stop()

	for range ticker.C {
		m.performCleanup()
	}
}

// performCleanup 执行清理
func (m *Manager) performCleanup() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 简化实现：这里可以添加更复杂的清理逻辑
	m.logger.Debug("执行重试器清理", "total", len(m.retriers))
}

// cleanupInactive 清理不活跃的重试器
func (m *Manager) cleanupInactive() {
	// 简化实现：实际应该根据使用时间等因素清理
	if len(m.retriers) > 0 {
		for name := range m.retriers {
			delete(m.retriers, name)
			m.logger.Info("清理不活跃重试器", "name", name)
			break
		}
	}
}

// DefaultManagerConfig 默认管理器配置
func DefaultManagerConfig() ManagerConfig {
	return ManagerConfig{
		DefaultConfig:       DefaultConfig(),
		EnableMetrics:       true,
		MetricsInterval:     30 * time.Second,
		EnableHealthCheck:   true,
		HealthCheckInterval: 60 * time.Second,
		AutoCleanup:         true,
		CleanupInterval:     300 * time.Second,
		MaxRetriers:         1000,
	}
}

// DevelopmentManagerConfig 开发环境管理器配置
func DevelopmentManagerConfig() ManagerConfig {
	config := DefaultManagerConfig()
	config.DefaultConfig = FastRetryConfig()
	config.MetricsInterval = 10 * time.Second
	config.HealthCheckInterval = 30 * time.Second
	return config
}

// ProductionManagerConfig 生产环境管理器配置
func ProductionManagerConfig() ManagerConfig {
	config := DefaultManagerConfig()
	config.DefaultConfig = SlowRetryConfig()
	config.MetricsInterval = 60 * time.Second
	config.HealthCheckInterval = 120 * time.Second
	return config
}
