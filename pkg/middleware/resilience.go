package middleware

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"paas-platform/pkg/circuitbreaker"
	"paas-platform/pkg/logger"
	"paas-platform/pkg/retry"
)

// ResilienceMiddleware 弹性中间件（断路器+重试）
type ResilienceMiddleware struct {
	cbManager     *circuitbreaker.Manager
	retryManager  *retry.Manager
	logger        logger.Logger
	config        ResilienceConfig
}

// ResilienceConfig 弹性配置
type ResilienceConfig struct {
	Enabled              bool                      `json:"enabled"`                // 是否启用
	EnableCircuitBreaker bool                      `json:"enable_circuit_breaker"` // 启用断路器
	EnableRetry          bool                      `json:"enable_retry"`           // 启用重试
	DefaultCBConfig      circuitbreaker.Config     `json:"default_cb_config"`      // 默认断路器配置
	DefaultRetryConfig   retry.Config              `json:"default_retry_config"`   // 默认重试配置
	ServiceConfigs       map[string]*ServiceConfig `json:"service_configs"`        // 服务特定配置
	PathConfigs          map[string]*PathConfig    `json:"path_configs"`           // 路径特定配置
	SkipPaths            []string                  `json:"skip_paths"`             // 跳过的路径
	OnlyPaths            []string                  `json:"only_paths"`             // 仅处理的路径
	FallbackResponse     *FallbackResponse         `json:"fallback_response"`      // 降级响应
	AddHeaders           bool                      `json:"add_headers"`            // 添加响应头
	LogRequests          bool                      `json:"log_requests"`           // 记录请求日志
	LogFailures          bool                      `json:"log_failures"`           // 记录失败日志
}

// ServiceConfig 服务配置
type ServiceConfig struct {
	CircuitBreakerConfig *circuitbreaker.Config `json:"circuit_breaker_config"` // 断路器配置
	RetryConfig          *retry.Config          `json:"retry_config"`           // 重试配置
	FallbackResponse     *FallbackResponse      `json:"fallback_response"`      // 降级响应
}

// PathConfig 路径配置
type PathConfig struct {
	CircuitBreakerConfig *circuitbreaker.Config `json:"circuit_breaker_config"` // 断路器配置
	RetryConfig          *retry.Config          `json:"retry_config"`           // 重试配置
	FallbackResponse     *FallbackResponse      `json:"fallback_response"`      // 降级响应
}

// FallbackResponse 降级响应
type FallbackResponse struct {
	StatusCode int                    `json:"status_code"` // 状态码
	Headers    map[string]string      `json:"headers"`     // 响应头
	Body       interface{}            `json:"body"`        // 响应体
	Message    string                 `json:"message"`     // 消息
}

// NewResilienceMiddleware 创建弹性中间件
func NewResilienceMiddleware(
	cbManager *circuitbreaker.Manager,
	retryManager *retry.Manager,
	logger logger.Logger,
	config ResilienceConfig,
) *ResilienceMiddleware {
	return &ResilienceMiddleware{
		cbManager:    cbManager,
		retryManager: retryManager,
		logger:       logger,
		config:       config,
	}
}

// Handler 返回中间件处理函数
func (rm *ResilienceMiddleware) Handler() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查是否启用
		if !rm.config.Enabled {
			c.Next()
			return
		}

		// 检查是否跳过
		if rm.shouldSkip(c.Request.URL.Path) {
			c.Next()
			return
		}

		// 检查是否仅处理指定路径
		if !rm.shouldProcess(c.Request.URL.Path) {
			c.Next()
			return
		}

		// 获取服务名称
		serviceName := rm.getServiceName(c)
		
		// 获取配置
		cbConfig, retryConfig, fallbackResp := rm.getConfigs(c, serviceName)

		// 执行弹性处理
		rm.executeWithResilience(c, serviceName, cbConfig, retryConfig, fallbackResp)
	}
}

// executeWithResilience 执行弹性处理
func (rm *ResilienceMiddleware) executeWithResilience(
	c *gin.Context,
	serviceName string,
	cbConfig *circuitbreaker.Config,
	retryConfig *retry.Config,
	fallbackResp *FallbackResponse,
) {
	ctx := c.Request.Context()
	startTime := time.Now()

	// 定义执行函数
	executeFunc := func() (interface{}, error) {
		return rm.executeRequest(c)
	}

	// 定义降级函数
	fallbackFunc := func() (interface{}, error) {
		return rm.executeFallback(c, fallbackResp)
	}

	var result interface{}
	var err error

	// 根据配置选择执行策略
	if rm.config.EnableCircuitBreaker && rm.config.EnableRetry {
		// 断路器 + 重试
		result, err = rm.executeWithCircuitBreakerAndRetry(ctx, serviceName, cbConfig, retryConfig, executeFunc, fallbackFunc)
	} else if rm.config.EnableCircuitBreaker {
		// 仅断路器
		result, err = rm.executeWithCircuitBreaker(ctx, serviceName, cbConfig, executeFunc, fallbackFunc)
	} else if rm.config.EnableRetry {
		// 仅重试
		result, err = rm.executeWithRetry(ctx, serviceName, retryConfig, executeFunc)
	} else {
		// 直接执行
		result, err = executeFunc()
	}

	duration := time.Since(startTime)

	// 处理结果
	rm.handleResult(c, result, err, duration, serviceName)
}

// executeWithCircuitBreakerAndRetry 使用断路器和重试执行
func (rm *ResilienceMiddleware) executeWithCircuitBreakerAndRetry(
	ctx context.Context,
	serviceName string,
	cbConfig *circuitbreaker.Config,
	retryConfig *retry.Config,
	executeFunc func() (interface{}, error),
	fallbackFunc func() (interface{}, error),
) (interface{}, error) {
	// 在断路器内执行重试
	return rm.cbManager.ExecuteWithConfig(ctx, serviceName, *cbConfig, func() (interface{}, error) {
		return rm.retryManager.ExecuteWithConfigAndResult(ctx, serviceName, *retryConfig, executeFunc)
	})
}

// executeWithCircuitBreaker 使用断路器执行
func (rm *ResilienceMiddleware) executeWithCircuitBreaker(
	ctx context.Context,
	serviceName string,
	cbConfig *circuitbreaker.Config,
	executeFunc func() (interface{}, error),
	fallbackFunc func() (interface{}, error),
) (interface{}, error) {
	if fallbackFunc != nil {
		return rm.cbManager.ExecuteWithFallback(ctx, serviceName, executeFunc, fallbackFunc)
	}
	return rm.cbManager.ExecuteWithConfig(ctx, serviceName, *cbConfig, executeFunc)
}

// executeWithRetry 使用重试执行
func (rm *ResilienceMiddleware) executeWithRetry(
	ctx context.Context,
	serviceName string,
	retryConfig *retry.Config,
	executeFunc func() (interface{}, error),
) (interface{}, error) {
	return rm.retryManager.ExecuteWithConfigAndResult(ctx, serviceName, *retryConfig, executeFunc)
}

// executeRequest 执行请求
func (rm *ResilienceMiddleware) executeRequest(c *gin.Context) (interface{}, error) {
	// 创建一个新的响应写入器来捕获响应
	writer := &responseWriter{
		ResponseWriter: c.Writer,
		statusCode:     http.StatusOK,
		body:          make([]byte, 0),
	}
	c.Writer = writer

	// 执行下一个中间件
	c.Next()

	// 检查是否有错误
	if len(c.Errors) > 0 {
		return nil, c.Errors.Last()
	}

	// 检查状态码
	if writer.statusCode >= 400 {
		return nil, fmt.Errorf("HTTP %d", writer.statusCode)
	}

	return writer.body, nil
}

// executeFallback 执行降级
func (rm *ResilienceMiddleware) executeFallback(c *gin.Context, fallbackResp *FallbackResponse) (interface{}, error) {
	if fallbackResp == nil {
		fallbackResp = rm.config.FallbackResponse
	}

	if fallbackResp == nil {
		// 默认降级响应
		fallbackResp = &FallbackResponse{
			StatusCode: http.StatusServiceUnavailable,
			Message:    "服务暂时不可用",
		}
	}

	// 设置状态码
	c.Status(fallbackResp.StatusCode)

	// 设置响应头
	for key, value := range fallbackResp.Headers {
		c.Header(key, value)
	}

	// 设置响应体
	var body interface{}
	if fallbackResp.Body != nil {
		body = fallbackResp.Body
	} else {
		body = gin.H{
			"error": gin.H{
				"code":    "SERVICE_UNAVAILABLE",
				"message": fallbackResp.Message,
			},
			"timestamp": time.Now().Format(time.RFC3339),
		}
	}

	c.JSON(fallbackResp.StatusCode, body)
	c.Abort()

	return body, nil
}

// handleResult 处理结果
func (rm *ResilienceMiddleware) handleResult(c *gin.Context, result interface{}, err error, duration time.Duration, serviceName string) {
	if err != nil {
		// 记录失败日志
		if rm.config.LogFailures {
			rm.logger.Error("弹性处理失败",
				"service", serviceName,
				"path", c.Request.URL.Path,
				"method", c.Request.Method,
				"error", err.Error(),
				"duration", duration)
		}

		// 如果还没有响应，返回错误响应
		if !c.Writer.Written() {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": gin.H{
					"code":    "RESILIENCE_ERROR",
					"message": "请求处理失败",
					"details": err.Error(),
				},
				"timestamp": time.Now().Format(time.RFC3339),
			})
		}
	} else {
		// 记录成功日志
		if rm.config.LogRequests {
			rm.logger.Debug("弹性处理成功",
				"service", serviceName,
				"path", c.Request.URL.Path,
				"method", c.Request.Method,
				"duration", duration)
		}
	}

	// 添加响应头
	if rm.config.AddHeaders {
		c.Header("X-Resilience-Service", serviceName)
		c.Header("X-Resilience-Duration", duration.String())
		c.Header("X-Resilience-CB-Enabled", strconv.FormatBool(rm.config.EnableCircuitBreaker))
		c.Header("X-Resilience-Retry-Enabled", strconv.FormatBool(rm.config.EnableRetry))
		
		if err != nil {
			c.Header("X-Resilience-Error", "true")
		}
	}
}

// getServiceName 获取服务名称
func (rm *ResilienceMiddleware) getServiceName(c *gin.Context) string {
	// 尝试从请求头获取
	if serviceName := c.GetHeader("X-Service-Name"); serviceName != "" {
		return serviceName
	}

	// 尝试从路径提取
	path := c.Request.URL.Path
	parts := strings.Split(strings.Trim(path, "/"), "/")
	if len(parts) >= 2 {
		return parts[1] // 假设格式为 /api/service-name/...
	}

	// 默认服务名称
	return "default"
}

// getConfigs 获取配置
func (rm *ResilienceMiddleware) getConfigs(c *gin.Context, serviceName string) (*circuitbreaker.Config, *retry.Config, *FallbackResponse) {
	path := c.Request.URL.Path

	// 检查路径特定配置
	if pathConfig, exists := rm.config.PathConfigs[path]; exists {
		cbConfig := pathConfig.CircuitBreakerConfig
		if cbConfig == nil {
			cbConfig = &rm.config.DefaultCBConfig
		}
		
		retryConfig := pathConfig.RetryConfig
		if retryConfig == nil {
			retryConfig = &rm.config.DefaultRetryConfig
		}
		
		return cbConfig, retryConfig, pathConfig.FallbackResponse
	}

	// 检查服务特定配置
	if serviceConfig, exists := rm.config.ServiceConfigs[serviceName]; exists {
		cbConfig := serviceConfig.CircuitBreakerConfig
		if cbConfig == nil {
			cbConfig = &rm.config.DefaultCBConfig
		}
		
		retryConfig := serviceConfig.RetryConfig
		if retryConfig == nil {
			retryConfig = &rm.config.DefaultRetryConfig
		}
		
		return cbConfig, retryConfig, serviceConfig.FallbackResponse
	}

	// 使用默认配置
	return &rm.config.DefaultCBConfig, &rm.config.DefaultRetryConfig, rm.config.FallbackResponse
}

// shouldSkip 检查是否跳过
func (rm *ResilienceMiddleware) shouldSkip(path string) bool {
	for _, skipPath := range rm.config.SkipPaths {
		if rm.matchPath(skipPath, path) {
			return true
		}
	}
	return false
}

// shouldProcess 检查是否处理
func (rm *ResilienceMiddleware) shouldProcess(path string) bool {
	if len(rm.config.OnlyPaths) == 0 {
		return true
	}

	for _, onlyPath := range rm.config.OnlyPaths {
		if rm.matchPath(onlyPath, path) {
			return true
		}
	}
	return false
}

// matchPath 路径匹配
func (rm *ResilienceMiddleware) matchPath(pattern, path string) bool {
	if pattern == "*" {
		return true
	}
	if strings.HasSuffix(pattern, "*") {
		prefix := pattern[:len(pattern)-1]
		return strings.HasPrefix(path, prefix)
	}
	return pattern == path
}

// responseWriter 响应写入器
type responseWriter struct {
	gin.ResponseWriter
	statusCode int
	body       []byte
}

func (w *responseWriter) WriteHeader(statusCode int) {
	w.statusCode = statusCode
	w.ResponseWriter.WriteHeader(statusCode)
}

func (w *responseWriter) Write(data []byte) (int, error) {
	w.body = append(w.body, data...)
	return w.ResponseWriter.Write(data)
}

// DefaultResilienceConfig 默认弹性配置
func DefaultResilienceConfig() ResilienceConfig {
	return ResilienceConfig{
		Enabled:              true,
		EnableCircuitBreaker: true,
		EnableRetry:          true,
		DefaultCBConfig:      circuitbreaker.Config{
			MaxRequests:         1,
			Interval:            60 * time.Second,
			Timeout:             60 * time.Second,
			FailureThreshold:    5,
			SuccessThreshold:    1,
			MinRequestThreshold: 10,
			FailureRate:         0.5,
		},
		DefaultRetryConfig: retry.DefaultConfig(),
		ServiceConfigs:     make(map[string]*ServiceConfig),
		PathConfigs:        make(map[string]*PathConfig),
		SkipPaths: []string{
			"/health",
			"/ready",
			"/metrics",
			"/swagger/*",
			"/docs/*",
		},
		FallbackResponse: &FallbackResponse{
			StatusCode: http.StatusServiceUnavailable,
			Message:    "服务暂时不可用，请稍后重试",
		},
		AddHeaders:  true,
		LogRequests: true,
		LogFailures: true,
	}
}
