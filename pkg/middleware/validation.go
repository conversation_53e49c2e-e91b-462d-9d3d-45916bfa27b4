package middleware

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"paas-platform/pkg/logger"
	"paas-platform/pkg/validator"
)

// ValidationMiddleware 验证中间件
type ValidationMiddleware struct {
	validator *validator.APIValidator
	logger    logger.Logger
	config    ValidationConfig
}

// ValidationConfig 验证配置
type ValidationConfig struct {
	Enabled         bool     `json:"enabled"`          // 是否启用验证
	SkipPaths       []string `json:"skip_paths"`       // 跳过验证的路径
	StrictMode      bool     `json:"strict_mode"`      // 严格模式
	LogValidation   bool     `json:"log_validation"`   // 记录验证日志
	CacheResults    bool     `json:"cache_results"`    // 缓存验证结果
	MaxCacheSize    int      `json:"max_cache_size"`   // 最大缓存大小
	CacheTTL        int      `json:"cache_ttl"`        // 缓存TTL（秒）
}

// ValidationResponse 验证响应
type ValidationResponse struct {
	Code      string                    `json:"code"`
	Message   string                    `json:"message"`
	Details   []validator.ValidationError `json:"details,omitempty"`
	Timestamp string                    `json:"timestamp"`
	RequestID string                    `json:"request_id,omitempty"`
}

// NewValidationMiddleware 创建验证中间件
func NewValidationMiddleware(apiValidator *validator.APIValidator, logger logger.Logger, config ValidationConfig) *ValidationMiddleware {
	return &ValidationMiddleware{
		validator: apiValidator,
		logger:    logger,
		config:    config,
	}
}

// Handler 返回验证中间件处理函数
func (vm *ValidationMiddleware) Handler() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查是否启用验证
		if !vm.config.Enabled {
			c.Next()
			return
		}
		
		// 检查是否跳过验证
		if vm.shouldSkipValidation(c.Request.URL.Path) {
			c.Next()
			return
		}
		
		// 执行验证
		result := vm.validator.ValidateRequest(c)
		
		// 记录验证日志
		if vm.config.LogValidation {
			vm.logValidation(c, result)
		}
		
		// 处理验证结果
		if !result.Valid {
			vm.handleValidationError(c, result)
			return
		}
		
		c.Next()
	}
}

// shouldSkipValidation 检查是否跳过验证
func (vm *ValidationMiddleware) shouldSkipValidation(path string) bool {
	for _, skipPath := range vm.config.SkipPaths {
		if vm.matchPath(skipPath, path) {
			return true
		}
	}
	return false
}

// matchPath 路径匹配
func (vm *ValidationMiddleware) matchPath(pattern, path string) bool {
	// 支持通配符匹配
	if pattern == "*" {
		return true
	}
	
	// 支持前缀匹配
	if pattern[len(pattern)-1] == '*' {
		prefix := pattern[:len(pattern)-1]
		return len(path) >= len(prefix) && path[:len(prefix)] == prefix
	}
	
	// 精确匹配
	return pattern == path
}

// logValidation 记录验证日志
func (vm *ValidationMiddleware) logValidation(c *gin.Context, result *validator.ValidationResult) {
	if result.Valid {
		vm.logger.Debug("API参数验证通过",
			"method", c.Request.Method,
			"path", c.Request.URL.Path,
			"client_ip", c.ClientIP(),
		)
	} else {
		vm.logger.Warn("API参数验证失败",
			"method", c.Request.Method,
			"path", c.Request.URL.Path,
			"client_ip", c.ClientIP(),
			"errors", len(result.Errors),
			"message", result.Message,
		)
		
		// 详细错误日志
		for _, err := range result.Errors {
			vm.logger.Debug("验证错误详情",
				"field", err.Field,
				"value", err.Value,
				"tag", err.Tag,
				"message", err.Message,
			)
		}
	}
}

// handleValidationError 处理验证错误
func (vm *ValidationMiddleware) handleValidationError(c *gin.Context, result *validator.ValidationResult) {
	requestID := c.GetString("request_id")
	
	response := ValidationResponse{
		Code:      "VALIDATION_FAILED",
		Message:   result.Message,
		Details:   result.Errors,
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: requestID,
	}
	
	// 严格模式下返回详细错误信息
	if !vm.config.StrictMode {
		response.Details = nil
	}
	
	c.JSON(http.StatusBadRequest, response)
	c.Abort()
}

// LoadValidationRules 加载验证规则
func (vm *ValidationMiddleware) LoadValidationRules(rules []*validator.ValidationRule) error {
	for _, rule := range rules {
		if err := vm.validator.RegisterRule(rule); err != nil {
			vm.logger.Error("注册验证规则失败", "error", err, "path", rule.Path, "method", rule.Method)
			return err
		}
	}
	
	vm.logger.Info("加载验证规则完成", "count", len(rules))
	return nil
}

// LoadJSONSchemas 加载 JSON Schema
func (vm *ValidationMiddleware) LoadJSONSchemas(schemas map[string]*validator.JSONSchema) error {
	for name, schema := range schemas {
		if err := vm.validator.RegisterSchema(name, schema); err != nil {
			vm.logger.Error("注册JSON Schema失败", "error", err, "name", name)
			return err
		}
	}
	
	vm.logger.Info("加载JSON Schema完成", "count", len(schemas))
	return nil
}

// GetValidationStats 获取验证统计信息
func (vm *ValidationMiddleware) GetValidationStats() map[string]interface{} {
	return map[string]interface{}{
		"enabled":      vm.config.Enabled,
		"strict_mode":  vm.config.StrictMode,
		"skip_paths":   vm.config.SkipPaths,
		"cache_enabled": vm.config.CacheResults,
	}
}

// UpdateConfig 更新配置
func (vm *ValidationMiddleware) UpdateConfig(config ValidationConfig) {
	vm.config = config
	vm.logger.Info("验证中间件配置已更新", "enabled", config.Enabled, "strict_mode", config.StrictMode)
}

// ValidateRequestManually 手动验证请求（用于测试）
func (vm *ValidationMiddleware) ValidateRequestManually(c *gin.Context) *validator.ValidationResult {
	return vm.validator.ValidateRequest(c)
}

// DefaultValidationConfig 默认验证配置
func DefaultValidationConfig() ValidationConfig {
	return ValidationConfig{
		Enabled:      true,
		StrictMode:   false,
		LogValidation: true,
		CacheResults: false,
		MaxCacheSize: 1000,
		CacheTTL:     300, // 5分钟
		SkipPaths: []string{
			"/health",
			"/ready",
			"/metrics",
			"/swagger/*",
			"/api/v1/auth/login",
			"/api/v1/auth/register",
		},
	}
}

// StrictValidationConfig 严格验证配置
func StrictValidationConfig() ValidationConfig {
	config := DefaultValidationConfig()
	config.StrictMode = true
	config.LogValidation = true
	return config
}

// DevelopmentValidationConfig 开发环境验证配置
func DevelopmentValidationConfig() ValidationConfig {
	config := DefaultValidationConfig()
	config.Enabled = false // 开发环境可以禁用验证
	config.StrictMode = false
	return config
}

// ProductionValidationConfig 生产环境验证配置
func ProductionValidationConfig() ValidationConfig {
	config := DefaultValidationConfig()
	config.Enabled = true
	config.StrictMode = true
	config.LogValidation = true
	config.CacheResults = true
	return config
}
