package middleware

import (
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"paas-platform/pkg/logger"
	"paas-platform/pkg/version"
)

// VersionMiddleware 版本控制中间件
type VersionMiddleware struct {
	manager *version.VersionManager
	router  *version.VersionRouter
	logger  logger.Logger
	config  VersionMiddlewareConfig
}

// VersionMiddlewareConfig 版本中间件配置
type VersionMiddlewareConfig struct {
	Enabled              bool     `json:"enabled"`               // 是否启用版本控制
	StrictVersioning     bool     `json:"strict_versioning"`     // 严格版本控制
	RequireVersion       bool     `json:"require_version"`       // 要求指定版本
	DefaultVersion       string   `json:"default_version"`       // 默认版本
	SupportedVersions    []string `json:"supported_versions"`    // 支持的版本列表
	DeprecatedVersions   []string `json:"deprecated_versions"`   // 已废弃的版本列表
	SkipPaths           []string `json:"skip_paths"`            // 跳过版本控制的路径
	EnableVersionHeader  bool     `json:"enable_version_header"` // 启用版本响应头
	EnableMetrics        bool     `json:"enable_metrics"`        // 启用指标收集
	EnableCompatibility  bool     `json:"enable_compatibility"`  // 启用兼容性处理
	CacheEnabled         bool     `json:"cache_enabled"`         // 启用缓存
	CacheTTL             time.Duration `json:"cache_ttl"`        // 缓存TTL
}

// NewVersionMiddleware 创建版本控制中间件
func NewVersionMiddleware(
	manager *version.VersionManager,
	router *version.VersionRouter,
	logger logger.Logger,
	config VersionMiddlewareConfig,
) *VersionMiddleware {
	return &VersionMiddleware{
		manager: manager,
		router:  router,
		logger:  logger,
		config:  config,
	}
}

// Handler 返回版本控制中间件处理函数
func (vm *VersionMiddleware) Handler() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查是否启用版本控制
		if !vm.config.Enabled {
			c.Next()
			return
		}

		// 检查是否跳过版本控制
		if vm.shouldSkipVersioning(c.Request.URL.Path) {
			c.Next()
			return
		}

		// 构建版本请求
		versionReq := vm.buildVersionRequest(c)

		// 执行版本路由
		routeResult, err := vm.router.RouteRequest(versionReq)
		if err != nil {
			vm.handleVersionError(c, err)
			return
		}

		// 检查版本状态
		if err := vm.checkVersionStatus(routeResult.TargetVersion); err != nil {
			vm.handleVersionError(c, err)
			return
		}

		// 设置版本信息到上下文
		vm.setVersionContext(c, routeResult)

		// 添加版本响应头
		if vm.config.EnableVersionHeader {
			vm.addVersionHeaders(c, routeResult)
		}

		// 记录版本使用指标
		if vm.config.EnableMetrics {
			vm.recordVersionMetrics(routeResult, versionReq)
		}

		// 继续处理请求
		c.Next()
	}
}

// buildVersionRequest 构建版本请求
func (vm *VersionMiddleware) buildVersionRequest(c *gin.Context) *version.VersionRequest {
	// 提取请求头
	headers := make(map[string]string)
	for key, values := range c.Request.Header {
		if len(values) > 0 {
			headers[key] = values[0]
		}
	}

	// 提取查询参数
	queryParams := make(map[string]string)
	for key, values := range c.Request.URL.Query() {
		if len(values) > 0 {
			queryParams[key] = values[0]
		}
	}

	return &version.VersionRequest{
		Method:      c.Request.Method,
		Path:        c.Request.URL.Path,
		Host:        c.Request.Host,
		Headers:     headers,
		QueryParams: queryParams,
		RemoteAddr:  c.ClientIP(),
		UserAgent:   c.Request.UserAgent(),
		Timestamp:   time.Now(),
	}
}

// shouldSkipVersioning 检查是否跳过版本控制
func (vm *VersionMiddleware) shouldSkipVersioning(path string) bool {
	for _, skipPath := range vm.config.SkipPaths {
		if vm.matchPath(skipPath, path) {
			return true
		}
	}
	return false
}

// matchPath 路径匹配
func (vm *VersionMiddleware) matchPath(pattern, path string) bool {
	// 支持通配符匹配
	if pattern == "*" {
		return true
	}

	// 支持前缀匹配
	if strings.HasSuffix(pattern, "*") {
		prefix := pattern[:len(pattern)-1]
		return strings.HasPrefix(path, prefix)
	}

	// 精确匹配
	return pattern == path
}

// checkVersionStatus 检查版本状态
func (vm *VersionMiddleware) checkVersionStatus(targetVersion string) error {
	// 检查版本是否存在
	if !vm.manager.VersionExists(targetVersion) {
		return &VersionError{
			Code:    "VERSION_NOT_FOUND",
			Message: "请求的版本不存在",
			Version: targetVersion,
		}
	}

	// 检查版本是否已废弃
	for _, deprecated := range vm.config.DeprecatedVersions {
		if deprecated == targetVersion {
			vm.logger.Warn("使用已废弃的版本", "version", targetVersion)
			// 废弃版本仍然可以使用，只是记录警告
			break
		}
	}

	// 检查版本是否在支持列表中
	if len(vm.config.SupportedVersions) > 0 {
		supported := false
		for _, supportedVersion := range vm.config.SupportedVersions {
			if supportedVersion == targetVersion {
				supported = true
				break
			}
		}
		if !supported {
			return &VersionError{
				Code:    "VERSION_NOT_SUPPORTED",
				Message: "请求的版本不受支持",
				Version: targetVersion,
			}
		}
	}

	return nil
}

// setVersionContext 设置版本信息到上下文
func (vm *VersionMiddleware) setVersionContext(c *gin.Context, result *version.VersionRouteResult) {
	c.Set("api_version", result.TargetVersion)
	c.Set("version_route_result", result)
	c.Set("target_service", result.ServiceName)
	c.Set("target_url", result.TargetURL)

	// 设置转换信息
	if len(result.Transformations) > 0 {
		c.Set("version_transformations", result.Transformations)
	}
}

// addVersionHeaders 添加版本响应头
func (vm *VersionMiddleware) addVersionHeaders(c *gin.Context, result *version.VersionRouteResult) {
	c.Header("X-API-Version", result.TargetVersion)
	c.Header("X-Version-Strategy", string(result.RouteStrategy))
	
	if result.Applied {
		c.Header("X-Version-Applied", "true")
	}

	// 添加废弃警告头
	for _, deprecated := range vm.config.DeprecatedVersions {
		if deprecated == result.TargetVersion {
			c.Header("X-API-Deprecated", "true")
			c.Header("X-API-Deprecation-Warning", "此版本已废弃，请升级到最新版本")
			break
		}
	}

	// 添加兼容性信息
	if len(result.Transformations) > 0 {
		c.Header("X-Version-Compatibility", "applied")
	}
}

// recordVersionMetrics 记录版本使用指标
func (vm *VersionMiddleware) recordVersionMetrics(result *version.VersionRouteResult, req *version.VersionRequest) {
	// 这里可以集成到指标收集系统
	vm.logger.Debug("版本使用指标",
		"version", result.TargetVersion,
		"path", req.Path,
		"method", req.Method,
		"user_agent", req.UserAgent,
		"remote_addr", req.RemoteAddr)
}

// handleVersionError 处理版本错误
func (vm *VersionMiddleware) handleVersionError(c *gin.Context, err error) {
	vm.logger.Error("版本控制错误", "error", err, "path", c.Request.URL.Path)

	if versionErr, ok := err.(*VersionError); ok {
		c.JSON(versionErr.HTTPStatus(), gin.H{
			"error": gin.H{
				"code":    versionErr.Code,
				"message": versionErr.Message,
				"version": versionErr.Version,
			},
			"timestamp": time.Now().Format(time.RFC3339),
		})
	} else {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": gin.H{
				"code":    "VERSION_ERROR",
				"message": err.Error(),
			},
			"timestamp": time.Now().Format(time.RFC3339),
		})
	}

	c.Abort()
}

// VersionError 版本错误
type VersionError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Version string `json:"version"`
}

// Error 实现 error 接口
func (ve *VersionError) Error() string {
	return ve.Message
}

// HTTPStatus 返回HTTP状态码
func (ve *VersionError) HTTPStatus() int {
	switch ve.Code {
	case "VERSION_NOT_FOUND":
		return http.StatusNotFound
	case "VERSION_NOT_SUPPORTED":
		return http.StatusNotAcceptable
	case "VERSION_DEPRECATED":
		return http.StatusGone
	case "VERSION_REQUIRED":
		return http.StatusBadRequest
	default:
		return http.StatusBadRequest
	}
}

// GetVersionFromContext 从上下文获取版本信息
func GetVersionFromContext(c *gin.Context) string {
	if version, exists := c.Get("api_version"); exists {
		if versionStr, ok := version.(string); ok {
			return versionStr
		}
	}
	return ""
}

// GetVersionRouteResult 从上下文获取版本路由结果
func GetVersionRouteResult(c *gin.Context) *version.VersionRouteResult {
	if result, exists := c.Get("version_route_result"); exists {
		if routeResult, ok := result.(*version.VersionRouteResult); ok {
			return routeResult
		}
	}
	return nil
}

// DefaultVersionMiddlewareConfig 默认版本中间件配置
func DefaultVersionMiddlewareConfig() VersionMiddlewareConfig {
	return VersionMiddlewareConfig{
		Enabled:             true,
		StrictVersioning:    false,
		RequireVersion:      false,
		DefaultVersion:      "v1",
		EnableVersionHeader: true,
		EnableMetrics:       true,
		EnableCompatibility: true,
		CacheEnabled:        true,
		CacheTTL:           5 * time.Minute,
		SkipPaths: []string{
			"/health",
			"/ready",
			"/metrics",
			"/swagger/*",
			"/docs/*",
		},
	}
}

// DevelopmentVersionMiddlewareConfig 开发环境版本中间件配置
func DevelopmentVersionMiddlewareConfig() VersionMiddlewareConfig {
	config := DefaultVersionMiddlewareConfig()
	config.StrictVersioning = false
	config.RequireVersion = false
	return config
}

// ProductionVersionMiddlewareConfig 生产环境版本中间件配置
func ProductionVersionMiddlewareConfig() VersionMiddlewareConfig {
	config := DefaultVersionMiddlewareConfig()
	config.StrictVersioning = true
	config.RequireVersion = true
	return config
}
