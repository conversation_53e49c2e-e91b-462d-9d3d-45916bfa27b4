package middleware

import (
	"bytes"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"paas-platform/pkg/logger"
	"paas-platform/pkg/transformer"
)

// TransformMiddleware 转换中间件
type TransformMiddleware struct {
	requestTransformer  *transformer.RequestTransformer
	responseTransformer *transformer.ResponseTransformer
	logger              logger.Logger
	config              TransformConfig
}

// TransformConfig 转换配置
type TransformConfig struct {
	Enabled              bool     `json:"enabled"`               // 是否启用转换
	RequestTransform     bool     `json:"request_transform"`     // 是否启用请求转换
	ResponseTransform    bool     `json:"response_transform"`    // 是否启用响应转换
	LogTransforms        bool     `json:"log_transforms"`        // 是否记录转换日志
	SkipPaths           []string `json:"skip_paths"`            // 跳过转换的路径
	MaxBodySize         int64    `json:"max_body_size"`         // 最大请求体大小
	BufferResponse      bool     `json:"buffer_response"`       // 是否缓冲响应
}

// NewTransformMiddleware 创建转换中间件
func NewTransformMiddleware(
	requestTransformer *transformer.RequestTransformer,
	responseTransformer *transformer.ResponseTransformer,
	logger logger.Logger,
	config TransformConfig,
) *TransformMiddleware {
	return &TransformMiddleware{
		requestTransformer:  requestTransformer,
		responseTransformer: responseTransformer,
		logger:              logger,
		config:              config,
	}
}

// Handler 返回转换中间件处理函数
func (tm *TransformMiddleware) Handler() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查是否启用转换
		if !tm.config.Enabled {
			c.Next()
			return
		}
		
		// 检查是否跳过转换
		if tm.shouldSkipTransform(c.Request.URL.Path) {
			c.Next()
			return
		}
		
		// 请求转换
		if tm.config.RequestTransform {
			if err := tm.transformRequest(c); err != nil {
				tm.logger.Error("请求转换失败", "error", err, "path", c.Request.URL.Path)
				c.JSON(http.StatusInternalServerError, gin.H{
					"error": "请求转换失败",
					"details": err.Error(),
				})
				return
			}
		}
		
		// 响应转换
		if tm.config.ResponseTransform && tm.config.BufferResponse {
			tm.transformResponseWithBuffer(c)
		} else {
			c.Next()
		}
	}
}

// transformRequest 转换请求
func (tm *TransformMiddleware) transformRequest(c *gin.Context) error {
	// 检查请求体大小
	if c.Request.ContentLength > tm.config.MaxBodySize {
		return fmt.Errorf("请求体大小超过限制: %d > %d", c.Request.ContentLength, tm.config.MaxBodySize)
	}
	
	// 执行请求转换
	result := tm.requestTransformer.TransformRequest(c)
	if !result.Success {
		return result.Error
	}
	
	// 记录转换日志
	if tm.config.LogTransforms && result.Modified {
		tm.logger.Info("请求转换完成",
			"method", c.Request.Method,
			"path", c.Request.URL.Path,
			"details", result.Details)
	}
	
	return nil
}

// transformResponseWithBuffer 使用缓冲区转换响应
func (tm *TransformMiddleware) transformResponseWithBuffer(c *gin.Context) {
	// 创建自定义响应写入器
	responseWriter := transformer.NewResponseWriter(c.Writer)
	c.Writer = responseWriter
	
	// 继续处理请求
	c.Next()
	
	// 获取响应数据
	responseData := responseWriter.GetBody()
	statusCode := responseWriter.GetStatusCode()
	
	// 执行响应转换
	result := tm.responseTransformer.TransformResponse(c, responseData, statusCode)
	
	// 记录转换日志
	if tm.config.LogTransforms && result.Modified {
		tm.logger.Info("响应转换完成",
			"method", c.Request.Method,
			"path", c.Request.URL.Path,
			"status_code", statusCode,
			"details", result.Details)
	}
	
	if !result.Success {
		tm.logger.Error("响应转换失败", "error", result.Error, "path", c.Request.URL.Path)
	}
}

// shouldSkipTransform 检查是否跳过转换
func (tm *TransformMiddleware) shouldSkipTransform(path string) bool {
	for _, skipPath := range tm.config.SkipPaths {
		if tm.matchPath(skipPath, path) {
			return true
		}
	}
	return false
}

// matchPath 路径匹配
func (tm *TransformMiddleware) matchPath(pattern, path string) bool {
	// 支持通配符匹配
	if pattern == "*" {
		return true
	}
	
	// 支持前缀匹配
	if pattern[len(pattern)-1] == '*' {
		prefix := pattern[:len(pattern)-1]
		return len(path) >= len(prefix) && path[:len(prefix)] == prefix
	}
	
	// 精确匹配
	return pattern == path
}

// LoadTransformRules 加载转换规则
func (tm *TransformMiddleware) LoadTransformRules(rules []*transformer.TransformRule) error {
	for _, rule := range rules {
		// 注册请求转换规则
		if rule.RequestRules != nil {
			if err := tm.requestTransformer.RegisterRule(rule); err != nil {
				tm.logger.Error("注册请求转换规则失败", "error", err, "path", rule.Path, "method", rule.Method)
				return err
			}
		}
		
		// 注册响应转换规则
		if rule.ResponseRules != nil {
			if err := tm.responseTransformer.RegisterRule(rule); err != nil {
				tm.logger.Error("注册响应转换规则失败", "error", err, "path", rule.Path, "method", rule.Method)
				return err
			}
		}
	}
	
	tm.logger.Info("加载转换规则完成", "count", len(rules))
	return nil
}

// GetTransformStats 获取转换统计信息
func (tm *TransformMiddleware) GetTransformStats() map[string]interface{} {
	return map[string]interface{}{
		"enabled":            tm.config.Enabled,
		"request_transform":  tm.config.RequestTransform,
		"response_transform": tm.config.ResponseTransform,
		"buffer_response":    tm.config.BufferResponse,
		"skip_paths":         tm.config.SkipPaths,
		"max_body_size":      tm.config.MaxBodySize,
	}
}

// UpdateConfig 更新配置
func (tm *TransformMiddleware) UpdateConfig(config TransformConfig) {
	tm.config = config
	tm.logger.Info("转换中间件配置已更新",
		"enabled", config.Enabled,
		"request_transform", config.RequestTransform,
		"response_transform", config.ResponseTransform)
}

// DefaultTransformConfig 默认转换配置
func DefaultTransformConfig() TransformConfig {
	return TransformConfig{
		Enabled:           true,
		RequestTransform:  true,
		ResponseTransform: true,
		LogTransforms:     true,
		BufferResponse:    true,
		MaxBodySize:       10 * 1024 * 1024, // 10MB
		SkipPaths: []string{
			"/health",
			"/ready",
			"/metrics",
			"/swagger/*",
		},
	}
}

// DevelopmentTransformConfig 开发环境转换配置
func DevelopmentTransformConfig() TransformConfig {
	config := DefaultTransformConfig()
	config.LogTransforms = true
	config.BufferResponse = true
	return config
}

// ProductionTransformConfig 生产环境转换配置
func ProductionTransformConfig() TransformConfig {
	config := DefaultTransformConfig()
	config.LogTransforms = false // 生产环境减少日志
	config.BufferResponse = true
	return config
}

// ResponseBufferWriter 响应缓冲写入器
type ResponseBufferWriter struct {
	gin.ResponseWriter
	body       *bytes.Buffer
	statusCode int
}

// NewResponseBufferWriter 创建响应缓冲写入器
func NewResponseBufferWriter(w gin.ResponseWriter) *ResponseBufferWriter {
	return &ResponseBufferWriter{
		ResponseWriter: w,
		body:          bytes.NewBuffer(nil),
		statusCode:    http.StatusOK,
	}
}

// Write 写入数据
func (rbw *ResponseBufferWriter) Write(data []byte) (int, error) {
	rbw.body.Write(data)
	return len(data), nil
}

// WriteHeader 写入状态码
func (rbw *ResponseBufferWriter) WriteHeader(statusCode int) {
	rbw.statusCode = statusCode
}

// GetBody 获取响应体
func (rbw *ResponseBufferWriter) GetBody() []byte {
	return rbw.body.Bytes()
}

// GetStatusCode 获取状态码
func (rbw *ResponseBufferWriter) GetStatusCode() int {
	return rbw.statusCode
}

// Flush 刷新到原始写入器
func (rbw *ResponseBufferWriter) Flush() {
	rbw.ResponseWriter.WriteHeader(rbw.statusCode)
	rbw.ResponseWriter.Write(rbw.body.Bytes())
}
