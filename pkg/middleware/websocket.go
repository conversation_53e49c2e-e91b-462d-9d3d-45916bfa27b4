package middleware

import (
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"paas-platform/pkg/logger"
	"paas-platform/pkg/websocket"
)

// WebSocketMiddleware WebSocket 中间件
type WebSocketMiddleware struct {
	gateway *websocket.WebSocketGateway
	logger  logger.Logger
	config  WebSocketMiddlewareConfig
}

// WebSocketMiddlewareConfig WebSocket 中间件配置
type WebSocketMiddlewareConfig struct {
	Enabled         bool     `json:"enabled"`          // 是否启用
	Path            string   `json:"path"`             // WebSocket 路径
	SkipPaths       []string `json:"skip_paths"`       // 跳过的路径
	OnlyPaths       []string `json:"only_paths"`       // 仅处理的路径
	RequireAuth     bool     `json:"require_auth"`     // 是否需要认证
	AuthHeader      string   `json:"auth_header"`      // 认证头名称
	UserIDHeader    string   `json:"user_id_header"`   // 用户ID头名称
	SessionIDHeader string   `json:"session_id_header"` // 会话ID头名称
	LogConnections  bool     `json:"log_connections"`  // 记录连接日志
	LogMessages     bool     `json:"log_messages"`     // 记录消息日志
	AddHeaders      bool     `json:"add_headers"`      // 添加响应头
	CorsEnabled     bool     `json:"cors_enabled"`     // 启用CORS
	CorsOrigins     []string `json:"cors_origins"`     // CORS允许的来源
}

// NewWebSocketMiddleware 创建 WebSocket 中间件
func NewWebSocketMiddleware(
	gateway *websocket.WebSocketGateway,
	logger logger.Logger,
	config WebSocketMiddlewareConfig,
) *WebSocketMiddleware {
	return &WebSocketMiddleware{
		gateway: gateway,
		logger:  logger,
		config:  config,
	}
}

// Handler 返回中间件处理函数
func (wsm *WebSocketMiddleware) Handler() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查是否启用
		if !wsm.config.Enabled {
			c.Next()
			return
		}

		// 检查是否为 WebSocket 请求
		if !wsm.isWebSocketRequest(c) {
			c.Next()
			return
		}

		// 检查路径匹配
		if !wsm.shouldHandle(c.Request.URL.Path) {
			c.Next()
			return
		}

		// 处理 CORS
		if wsm.config.CorsEnabled {
			wsm.handleCORS(c)
			if c.Request.Method == "OPTIONS" {
				c.AbortWithStatus(http.StatusOK)
				return
			}
		}

		// 认证检查
		if wsm.config.RequireAuth {
			if !wsm.checkAuth(c) {
				c.JSON(http.StatusUnauthorized, gin.H{
					"error": "认证失败",
				})
				c.Abort()
				return
			}
		}

		// 记录连接日志
		if wsm.config.LogConnections {
			wsm.logger.Info("WebSocket 连接请求",
				"path", c.Request.URL.Path,
				"remote_addr", c.ClientIP(),
				"user_agent", c.Request.UserAgent(),
				"user_id", c.GetHeader(wsm.config.UserIDHeader),
				"session_id", c.GetHeader(wsm.config.SessionIDHeader))
		}

		// 添加响应头
		if wsm.config.AddHeaders {
			c.Header("X-WebSocket-Gateway", "enabled")
			c.Header("X-WebSocket-Version", "1.0")
			c.Header("X-WebSocket-Timestamp", time.Now().Format(time.RFC3339))
		}

		// 处理 WebSocket 升级
		wsm.gateway.HandleUpgrade(c)
		c.Abort()
	}
}

// isWebSocketRequest 检查是否为 WebSocket 请求
func (wsm *WebSocketMiddleware) isWebSocketRequest(c *gin.Context) bool {
	return strings.ToLower(c.GetHeader("Connection")) == "upgrade" &&
		strings.ToLower(c.GetHeader("Upgrade")) == "websocket"
}

// shouldHandle 检查是否应该处理
func (wsm *WebSocketMiddleware) shouldHandle(path string) bool {
	// 检查跳过路径
	for _, skipPath := range wsm.config.SkipPaths {
		if wsm.matchPath(skipPath, path) {
			return false
		}
	}

	// 检查仅处理路径
	if len(wsm.config.OnlyPaths) > 0 {
		for _, onlyPath := range wsm.config.OnlyPaths {
			if wsm.matchPath(onlyPath, path) {
				return true
			}
		}
		return false
	}

	// 检查默认路径
	if wsm.config.Path != "" {
		return wsm.matchPath(wsm.config.Path, path)
	}

	return true
}

// matchPath 路径匹配
func (wsm *WebSocketMiddleware) matchPath(pattern, path string) bool {
	if pattern == "*" {
		return true
	}
	if strings.HasSuffix(pattern, "*") {
		prefix := pattern[:len(pattern)-1]
		return strings.HasPrefix(path, prefix)
	}
	return pattern == path
}

// handleCORS 处理 CORS
func (wsm *WebSocketMiddleware) handleCORS(c *gin.Context) {
	origin := c.GetHeader("Origin")
	
	// 检查允许的来源
	allowed := false
	for _, allowedOrigin := range wsm.config.CorsOrigins {
		if allowedOrigin == "*" || allowedOrigin == origin {
			allowed = true
			break
		}
	}

	if allowed {
		c.Header("Access-Control-Allow-Origin", origin)
		c.Header("Access-Control-Allow-Credentials", "true")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, X-User-ID, X-Session-ID")
		c.Header("Access-Control-Expose-Headers", "X-WebSocket-Gateway, X-WebSocket-Version, X-WebSocket-Timestamp")
	}
}

// checkAuth 检查认证
func (wsm *WebSocketMiddleware) checkAuth(c *gin.Context) bool {
	if wsm.config.AuthHeader == "" {
		return true
	}

	authValue := c.GetHeader(wsm.config.AuthHeader)
	if authValue == "" {
		return false
	}

	// 这里可以添加具体的认证逻辑
	// 例如验证 JWT token、API key 等
	// 简化实现，只检查是否存在认证头
	return true
}

// GetGateway 获取 WebSocket 网关
func (wsm *WebSocketMiddleware) GetGateway() *websocket.WebSocketGateway {
	return wsm.gateway
}

// GetStats 获取统计信息
func (wsm *WebSocketMiddleware) GetStats() *websocket.Stats {
	return wsm.gateway.GetStats()
}

// BroadcastMessage 广播消息
func (wsm *WebSocketMiddleware) BroadcastMessage(message *websocket.Message) error {
	return wsm.gateway.BroadcastMessage(message)
}

// SendMessageToUser 发送消息给用户
func (wsm *WebSocketMiddleware) SendMessageToUser(userID string, message *websocket.Message) error {
	return wsm.gateway.SendMessageToUser(userID, message)
}

// SendRoomMessage 发送房间消息
func (wsm *WebSocketMiddleware) SendRoomMessage(roomID string, message *websocket.Message) error {
	return wsm.gateway.SendRoomMessage(roomID, message)
}

// CreateRoom 创建房间
func (wsm *WebSocketMiddleware) CreateRoom(id, name, description string, maxSize int, isPrivate bool, owner string) error {
	return wsm.gateway.CreateRoom(id, name, description, maxSize, isPrivate, owner)
}

// DeleteRoom 删除房间
func (wsm *WebSocketMiddleware) DeleteRoom(id string) error {
	return wsm.gateway.DeleteRoom(id)
}

// GetOnlineUsers 获取在线用户
func (wsm *WebSocketMiddleware) GetOnlineUsers() []string {
	return wsm.gateway.GetOnlineUsers()
}

// IsUserOnline 检查用户是否在线
func (wsm *WebSocketMiddleware) IsUserOnline(userID string) bool {
	return wsm.gateway.IsUserOnline(userID)
}

// DefaultWebSocketMiddlewareConfig 默认配置
func DefaultWebSocketMiddlewareConfig() WebSocketMiddlewareConfig {
	return WebSocketMiddlewareConfig{
		Enabled:         true,
		Path:            "/ws",
		RequireAuth:     false,
		AuthHeader:      "Authorization",
		UserIDHeader:    "X-User-ID",
		SessionIDHeader: "X-Session-ID",
		LogConnections:  true,
		LogMessages:     false,
		AddHeaders:      true,
		CorsEnabled:     true,
		CorsOrigins:     []string{"*"},
		SkipPaths: []string{
			"/health",
			"/ready",
			"/metrics",
		},
	}
}

// DevelopmentWebSocketMiddlewareConfig 开发环境配置
func DevelopmentWebSocketMiddlewareConfig() WebSocketMiddlewareConfig {
	config := DefaultWebSocketMiddlewareConfig()
	config.RequireAuth = false
	config.LogConnections = true
	config.LogMessages = true
	config.CorsOrigins = []string{"*"}
	return config
}

// ProductionWebSocketMiddlewareConfig 生产环境配置
func ProductionWebSocketMiddlewareConfig() WebSocketMiddlewareConfig {
	config := DefaultWebSocketMiddlewareConfig()
	config.RequireAuth = true
	config.LogConnections = true
	config.LogMessages = false
	config.CorsOrigins = []string{} // 生产环境需要明确指定允许的来源
	return config
}

// WebSocketHandler WebSocket 处理器接口
type WebSocketHandler interface {
	HandleMessage(conn *websocket.Connection, message *websocket.Message) error
	HandleConnect(conn *websocket.Connection) error
	HandleDisconnect(conn *websocket.Connection) error
}

// DefaultWebSocketHandler 默认 WebSocket 处理器
type DefaultWebSocketHandler struct {
	logger logger.Logger
}

// NewDefaultWebSocketHandler 创建默认处理器
func NewDefaultWebSocketHandler(logger logger.Logger) *DefaultWebSocketHandler {
	return &DefaultWebSocketHandler{
		logger: logger,
	}
}

// HandleMessage 处理消息
func (h *DefaultWebSocketHandler) HandleMessage(conn *websocket.Connection, message *websocket.Message) error {
	h.logger.Debug("处理 WebSocket 消息",
		"connection_id", conn.ID,
		"message_type", message.Type,
		"from", message.From,
		"to", message.To)
	return nil
}

// HandleConnect 处理连接
func (h *DefaultWebSocketHandler) HandleConnect(conn *websocket.Connection) error {
	h.logger.Info("WebSocket 连接建立",
		"connection_id", conn.ID,
		"user_id", conn.UserID,
		"remote_addr", conn.RemoteAddr)
	return nil
}

// HandleDisconnect 处理断开连接
func (h *DefaultWebSocketHandler) HandleDisconnect(conn *websocket.Connection) error {
	h.logger.Info("WebSocket 连接断开",
		"connection_id", conn.ID,
		"user_id", conn.UserID)
	return nil
}
