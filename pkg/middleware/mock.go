package middleware

import (
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"paas-platform/pkg/logger"
	"paas-platform/pkg/mock"
)

// MockMiddleware Mock 服务中间件
type MockMiddleware struct {
	manager *mock.MockManager
	logger  logger.Logger
	config  MockMiddlewareConfig
}

// MockMiddlewareConfig Mock 中间件配置
type MockMiddlewareConfig struct {
	Enabled         bool     `json:"enabled"`          // 是否启用 Mock
	Priority        int      `json:"priority"`         // 中间件优先级
	SkipPaths       []string `json:"skip_paths"`       // 跳过 Mock 的路径
	OnlyPaths       []string `json:"only_paths"`       // 仅对指定路径启用 Mock
	MockHeader      string   `json:"mock_header"`      // Mock 控制头
	MockHeaderValue string   `json:"mock_header_value"` // Mock 控制头值
	ForceMode       bool     `json:"force_mode"`       // 强制模式（忽略后端服务）
	PassThrough     bool     `json:"pass_through"`     // 透传模式（Mock 失败时继续）
	LogRequests     bool     `json:"log_requests"`     // 记录请求日志
	LogResponses    bool     `json:"log_responses"`    // 记录响应日志
	AddHeaders      bool     `json:"add_headers"`      // 添加 Mock 响应头
	CacheEnabled    bool     `json:"cache_enabled"`    // 启用缓存
	CacheTTL        time.Duration `json:"cache_ttl"`   // 缓存TTL
}

// NewMockMiddleware 创建 Mock 中间件
func NewMockMiddleware(
	manager *mock.MockManager,
	logger logger.Logger,
	config MockMiddlewareConfig,
) *MockMiddleware {
	return &MockMiddleware{
		manager: manager,
		logger:  logger,
		config:  config,
	}
}

// Handler 返回 Mock 中间件处理函数
func (mm *MockMiddleware) Handler() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查是否启用 Mock
		if !mm.config.Enabled {
			c.Next()
			return
		}

		// 检查是否跳过 Mock
		if mm.shouldSkipMock(c.Request.URL.Path) {
			c.Next()
			return
		}

		// 检查是否仅对指定路径启用 Mock
		if !mm.shouldEnableMock(c.Request.URL.Path) {
			c.Next()
			return
		}

		// 检查 Mock 控制头
		if !mm.shouldUseMock(c) {
			c.Next()
			return
		}

		// 构建 Mock 请求
		mockRequest, err := mm.buildMockRequest(c)
		if err != nil {
			mm.logger.Error("构建 Mock 请求失败", "error", err)
			if mm.config.PassThrough {
				c.Next()
				return
			}
			mm.handleMockError(c, err)
			return
		}

		// 处理 Mock 请求
		result, err := mm.manager.ProcessMockRequest(mockRequest)
		if err != nil {
			mm.logger.Error("处理 Mock 请求失败", "error", err)
			if mm.config.PassThrough {
				c.Next()
				return
			}
			mm.handleMockError(c, err)
			return
		}

		// 检查是否匹配到 Mock 规则
		if !result.Matched {
			if mm.config.ForceMode {
				// 强制模式下返回默认 Mock 响应
				mm.handleNoMatchFound(c)
				return
			}
			// 继续处理请求
			c.Next()
			return
		}

		// 应用 Mock 响应
		mm.applyMockResponse(c, result)

		// 记录 Mock 使用日志
		if mm.config.LogRequests {
			mm.logMockUsage(mockRequest, result)
		}

		// 中止请求处理链
		c.Abort()
	}
}

// buildMockRequest 构建 Mock 请求
func (mm *MockMiddleware) buildMockRequest(c *gin.Context) (*mock.MockRequest, error) {
	// 读取请求体
	var body []byte
	if c.Request.Body != nil {
		var err error
		body, err = io.ReadAll(c.Request.Body)
		if err != nil {
			return nil, err
		}
		// 重置请求体以供后续使用
		c.Request.Body = io.NopCloser(strings.NewReader(string(body)))
	}

	// 提取请求头
	headers := make(map[string]string)
	for key, values := range c.Request.Header {
		if len(values) > 0 {
			headers[key] = values[0]
		}
	}

	// 提取查询参数
	queryParams := make(map[string]string)
	for key, values := range c.Request.URL.Query() {
		if len(values) > 0 {
			queryParams[key] = values[0]
		}
	}

	// 生成请求ID
	requestID := c.GetHeader("X-Request-ID")
	if requestID == "" {
		requestID = generateRequestID()
	}

	return &mock.MockRequest{
		Method:      c.Request.Method,
		Path:        c.Request.URL.Path,
		Headers:     headers,
		QueryParams: queryParams,
		Body:        body,
		RemoteAddr:  c.ClientIP(),
		UserAgent:   c.Request.UserAgent(),
		Timestamp:   time.Now(),
		RequestID:   requestID,
	}, nil
}

// shouldSkipMock 检查是否跳过 Mock
func (mm *MockMiddleware) shouldSkipMock(path string) bool {
	for _, skipPath := range mm.config.SkipPaths {
		if mm.matchPath(skipPath, path) {
			return true
		}
	}
	return false
}

// shouldEnableMock 检查是否启用 Mock
func (mm *MockMiddleware) shouldEnableMock(path string) bool {
	// 如果没有指定仅启用路径，则对所有路径启用
	if len(mm.config.OnlyPaths) == 0 {
		return true
	}

	for _, onlyPath := range mm.config.OnlyPaths {
		if mm.matchPath(onlyPath, path) {
			return true
		}
	}
	return false
}

// shouldUseMock 检查是否使用 Mock
func (mm *MockMiddleware) shouldUseMock(c *gin.Context) bool {
	// 如果没有配置 Mock 控制头，则始终使用 Mock
	if mm.config.MockHeader == "" {
		return true
	}

	headerValue := c.GetHeader(mm.config.MockHeader)
	if mm.config.MockHeaderValue == "" {
		// 如果没有指定头值，则只要存在头就使用 Mock
		return headerValue != ""
	}

	// 检查头值是否匹配
	return headerValue == mm.config.MockHeaderValue
}

// matchPath 路径匹配
func (mm *MockMiddleware) matchPath(pattern, path string) bool {
	// 支持通配符匹配
	if pattern == "*" {
		return true
	}

	// 支持前缀匹配
	if strings.HasSuffix(pattern, "*") {
		prefix := pattern[:len(pattern)-1]
		return strings.HasPrefix(path, prefix)
	}

	// 精确匹配
	return pattern == path
}

// applyMockResponse 应用 Mock 响应
func (mm *MockMiddleware) applyMockResponse(c *gin.Context, result *mock.MockResult) {
	response := result.Response

	// 设置状态码
	c.Status(response.StatusCode)

	// 设置响应头
	for key, value := range response.Headers {
		c.Header(key, value)
	}

	// 添加 Mock 标识头
	if mm.config.AddHeaders {
		c.Header("X-Mock-Matched", "true")
		c.Header("X-Mock-Rule-ID", result.Rule.ID)
		c.Header("X-Mock-Rule-Name", result.Rule.Name)
		c.Header("X-Mock-Source", string(response.Source))
		c.Header("X-Mock-Process-Time", result.ProcessTime.String())

		if result.Scenario != nil {
			c.Header("X-Mock-Scenario", result.Scenario.Name)
		}
	}

	// 写入响应体
	c.Data(response.StatusCode, response.Headers["Content-Type"], response.Body)
}

// handleMockError 处理 Mock 错误
func (mm *MockMiddleware) handleMockError(c *gin.Context, err error) {
	mm.logger.Error("Mock 处理错误", "error", err, "path", c.Request.URL.Path)

	c.JSON(http.StatusInternalServerError, gin.H{
		"error": gin.H{
			"code":    "MOCK_ERROR",
			"message": "Mock 服务处理失败",
			"details": err.Error(),
		},
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// handleNoMatchFound 处理未找到匹配规则
func (mm *MockMiddleware) handleNoMatchFound(c *gin.Context) {
	mm.logger.Warn("未找到匹配的 Mock 规则", "path", c.Request.URL.Path, "method", c.Request.Method)

	// 返回默认 Mock 响应
	c.JSON(http.StatusNotFound, gin.H{
		"error": gin.H{
			"code":    "MOCK_NOT_FOUND",
			"message": "未找到匹配的 Mock 规则",
			"path":    c.Request.URL.Path,
			"method":  c.Request.Method,
		},
		"timestamp": time.Now().Format(time.RFC3339),
	})

	if mm.config.AddHeaders {
		c.Header("X-Mock-Matched", "false")
		c.Header("X-Mock-Error", "no-rule-found")
	}
}

// logMockUsage 记录 Mock 使用日志
func (mm *MockMiddleware) logMockUsage(request *mock.MockRequest, result *mock.MockResult) {
	logData := map[string]interface{}{
		"method":       request.Method,
		"path":         request.Path,
		"remote_addr":  request.RemoteAddr,
		"user_agent":   request.UserAgent,
		"matched":      result.Matched,
		"process_time": result.ProcessTime.String(),
	}

	if result.Rule != nil {
		logData["rule_id"] = result.Rule.ID
		logData["rule_name"] = result.Rule.Name
	}

	if result.Scenario != nil {
		logData["scenario"] = result.Scenario.Name
	}

	if result.Response != nil {
		logData["status_code"] = result.Response.StatusCode
		logData["response_size"] = result.Response.Size
		logData["response_source"] = result.Response.Source
	}

	mm.logger.Info("Mock 请求处理", logData)
}

// generateRequestID 生成请求ID
func generateRequestID() string {
	return time.Now().Format("20060102150405") + "-" + randomString(8)
}

// randomString 生成随机字符串
func randomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	result := make([]byte, length)
	for i := range result {
		result[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(result)
}

// DefaultMockMiddlewareConfig 默认 Mock 中间件配置
func DefaultMockMiddlewareConfig() MockMiddlewareConfig {
	return MockMiddlewareConfig{
		Enabled:         true,
		Priority:        100,
		MockHeader:      "X-Mock-Enable",
		MockHeaderValue: "true",
		ForceMode:       false,
		PassThrough:     true,
		LogRequests:     true,
		LogResponses:    false,
		AddHeaders:      true,
		CacheEnabled:    false,
		CacheTTL:        5 * time.Minute,
		SkipPaths: []string{
			"/health",
			"/ready",
			"/metrics",
			"/swagger/*",
			"/docs/*",
		},
	}
}

// DevelopmentMockMiddlewareConfig 开发环境 Mock 中间件配置
func DevelopmentMockMiddlewareConfig() MockMiddlewareConfig {
	config := DefaultMockMiddlewareConfig()
	config.ForceMode = false
	config.PassThrough = true
	config.LogRequests = true
	config.LogResponses = true
	return config
}

// ProductionMockMiddlewareConfig 生产环境 Mock 中间件配置
func ProductionMockMiddlewareConfig() MockMiddlewareConfig {
	config := DefaultMockMiddlewareConfig()
	config.Enabled = false // 生产环境默认禁用 Mock
	config.ForceMode = false
	config.PassThrough = true
	config.LogRequests = false
	config.LogResponses = false
	return config
}
