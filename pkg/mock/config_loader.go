package mock

import (
	"encoding/json"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"strings"

	"paas-platform/pkg/logger"
)

// ConfigLoader Mock 配置加载器
type ConfigLoader struct {
	logger    logger.Logger
	configDir string
}

// NewConfigLoader 创建配置加载器
func NewConfigLoader(configDir string, logger logger.Logger) *ConfigLoader {
	return &ConfigLoader{
		logger:    logger,
		configDir: configDir,
	}
}

// LoadMockRules 从目录加载 Mock 规则
func (cl *ConfigLoader) LoadMockRules() ([]*MockRule, error) {
	if cl.configDir == "" {
		return nil, fmt.Errorf("Mock 配置目录未设置")
	}
	
	if _, err := os.Stat(cl.configDir); os.IsNotExist(err) {
		cl.logger.Warn("Mock 配置目录不存在", "dir", cl.configDir)
		return []*MockRule{}, nil
	}
	
	cl.logger.Info("开始加载 Mock 规则", "dir", cl.configDir)
	
	var allRules []*MockRule
	
	err := filepath.WalkDir(cl.configDir, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}
		
		if d.IsDir() || !strings.HasSuffix(path, ".json") {
			return nil
		}
		
		rules, err := cl.loadRulesFromFile(path)
		if err != nil {
			cl.logger.Error("加载 Mock 规则文件失败", "error", err, "file", path)
			return err
		}
		
		allRules = append(allRules, rules...)
		return nil
	})
	
	if err != nil {
		return nil, fmt.Errorf("加载 Mock 规则失败: %w", err)
	}
	
	cl.logger.Info("Mock 规则加载完成", "count", len(allRules))
	return allRules, nil
}

// loadRulesFromFile 从文件加载规则
func (cl *ConfigLoader) loadRulesFromFile(filePath string) ([]*MockRule, error) {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取 Mock 规则文件失败 %s: %w", filePath, err)
	}
	
	// 尝试解析为单个规则
	var singleRule MockRule
	if err := json.Unmarshal(data, &singleRule); err == nil && singleRule.ID != "" {
		if err := cl.validateRule(&singleRule); err != nil {
			return nil, fmt.Errorf("规则验证失败 %s: %w", filePath, err)
		}
		cl.logger.Debug("加载单个 Mock 规则", "file", filePath, "id", singleRule.ID)
		return []*MockRule{&singleRule}, nil
	}
	
	// 尝试解析为规则数组
	var rules []*MockRule
	if err := json.Unmarshal(data, &rules); err != nil {
		return nil, fmt.Errorf("解析 Mock 规则文件失败 %s: %w", filePath, err)
	}
	
	// 验证所有规则
	for i, rule := range rules {
		if err := cl.validateRule(rule); err != nil {
			return nil, fmt.Errorf("规则 %d 验证失败 %s: %w", i, filePath, err)
		}
	}
	
	cl.logger.Debug("加载 Mock 规则数组", "file", filePath, "count", len(rules))
	return rules, nil
}

// validateRule 验证规则
func (cl *ConfigLoader) validateRule(rule *MockRule) error {
	if rule.ID == "" {
		return fmt.Errorf("规则 ID 不能为空")
	}
	
	if rule.Path == "" {
		return fmt.Errorf("规则路径不能为空")
	}
	
	if rule.Method == "" {
		return fmt.Errorf("规则方法不能为空")
	}
	
	// 验证路径格式
	if !strings.HasPrefix(rule.Path, "/") {
		return fmt.Errorf("路径必须以 / 开头")
	}
	
	// 验证 HTTP 方法
	validMethods := []string{"GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", "*"}
	validMethod := false
	for _, method := range validMethods {
		if rule.Method == method {
			validMethod = true
			break
		}
	}
	if !validMethod {
		return fmt.Errorf("无效的 HTTP 方法: %s", rule.Method)
	}
	
	// 验证状态码
	if rule.Response.StatusCode < 100 || rule.Response.StatusCode > 599 {
		return fmt.Errorf("无效的 HTTP 状态码: %d", rule.Response.StatusCode)
	}
	
	// 验证响应体类型
	if rule.Response.BodyType != "" {
		validBodyTypes := []ResponseBodyType{
			ResponseBodyTypeJSON, ResponseBodyTypeXML, ResponseBodyTypeText,
			ResponseBodyTypeHTML, ResponseBodyTypeBinary, ResponseBodyTypeTemplate,
			ResponseBodyTypeSchema, ResponseBodyTypeFile, ResponseBodyTypeProxy,
		}
		validBodyType := false
		for _, bodyType := range validBodyTypes {
			if rule.Response.BodyType == bodyType {
				validBodyType = true
				break
			}
		}
		if !validBodyType {
			return fmt.Errorf("无效的响应体类型: %s", rule.Response.BodyType)
		}
	}
	
	// 验证条件
	for i, condition := range rule.Conditions {
		if err := cl.validateCondition(&condition); err != nil {
			return fmt.Errorf("条件 %d 验证失败: %w", i, err)
		}
	}
	
	// 验证场景
	for i, scenario := range rule.Scenarios {
		if err := cl.validateScenario(&scenario); err != nil {
			return fmt.Errorf("场景 %d 验证失败: %w", i, err)
		}
	}
	
	// 验证延迟配置
	if rule.Delay != nil {
		if err := cl.validateDelay(rule.Delay); err != nil {
			return fmt.Errorf("延迟配置验证失败: %w", err)
		}
	}
	
	// 验证代理配置
	if rule.Response.Proxy != nil {
		if err := cl.validateProxy(rule.Response.Proxy); err != nil {
			return fmt.Errorf("代理配置验证失败: %w", err)
		}
	}
	
	return nil
}

// validateCondition 验证条件
func (cl *ConfigLoader) validateCondition(condition *MockCondition) error {
	if condition.Field == "" && condition.Type != ConditionTypeRandom {
		return fmt.Errorf("条件字段不能为空")
	}
	
	// 验证条件类型
	validTypes := []ConditionType{
		ConditionTypeHeader, ConditionTypeQuery, ConditionTypeBody,
		ConditionTypePath, ConditionTypeMethod, ConditionTypeUserAgent,
		ConditionTypeIP, ConditionTypeTime, ConditionTypeRandom,
	}
	validType := false
	for _, t := range validTypes {
		if condition.Type == t {
			validType = true
			break
		}
	}
	if !validType {
		return fmt.Errorf("无效的条件类型: %s", condition.Type)
	}
	
	// 验证操作符
	validOperators := []Operator{
		OperatorEquals, OperatorNotEquals, OperatorContains, OperatorNotContains,
		OperatorStartsWith, OperatorEndsWith, OperatorRegex, OperatorIn, OperatorNotIn,
		OperatorGreater, OperatorLess, OperatorExists, OperatorNotExists,
	}
	validOperator := false
	for _, op := range validOperators {
		if condition.Operator == op {
			validOperator = true
			break
		}
	}
	if !validOperator {
		return fmt.Errorf("无效的操作符: %s", condition.Operator)
	}
	
	return nil
}

// validateScenario 验证场景
func (cl *ConfigLoader) validateScenario(scenario *MockScenario) error {
	if scenario.Name == "" {
		return fmt.Errorf("场景名称不能为空")
	}
	
	if scenario.Probability < 0 || scenario.Probability > 1 {
		return fmt.Errorf("场景概率必须在 0-1 之间")
	}
	
	if scenario.Weight < 0 {
		return fmt.Errorf("场景权重不能为负数")
	}
	
	// 验证场景条件
	for i, condition := range scenario.Conditions {
		if err := cl.validateCondition(&condition); err != nil {
			return fmt.Errorf("场景条件 %d 验证失败: %w", i, err)
		}
	}
	
	// 验证场景响应
	if scenario.Response.StatusCode < 100 || scenario.Response.StatusCode > 599 {
		return fmt.Errorf("无效的场景响应状态码: %d", scenario.Response.StatusCode)
	}
	
	return nil
}

// validateDelay 验证延迟配置
func (cl *ConfigLoader) validateDelay(delay *DelayConfig) error {
	validTypes := []DelayType{DelayTypeFixed, DelayTypeRandom, DelayTypeNormal}
	validType := false
	for _, t := range validTypes {
		if delay.Type == t {
			validType = true
			break
		}
	}
	if !validType {
		return fmt.Errorf("无效的延迟类型: %s", delay.Type)
	}
	
	switch delay.Type {
	case DelayTypeFixed:
		if delay.Fixed < 0 {
			return fmt.Errorf("固定延迟不能为负数")
		}
	case DelayTypeRandom:
		if delay.Min < 0 || delay.Max < 0 {
			return fmt.Errorf("随机延迟范围不能为负数")
		}
		if delay.Max < delay.Min {
			return fmt.Errorf("随机延迟最大值不能小于最小值")
		}
	case DelayTypeNormal:
		if delay.Mean < 0 {
			return fmt.Errorf("正态分布延迟均值不能为负数")
		}
		if delay.StdDev < 0 {
			return fmt.Errorf("正态分布延迟标准差不能为负数")
		}
	}
	
	return nil
}

// validateProxy 验证代理配置
func (cl *ConfigLoader) validateProxy(proxy *ProxyConfig) error {
	if proxy.URL == "" {
		return fmt.Errorf("代理 URL 不能为空")
	}
	
	if !strings.HasPrefix(proxy.URL, "http://") && !strings.HasPrefix(proxy.URL, "https://") {
		return fmt.Errorf("代理 URL 必须以 http:// 或 https:// 开头")
	}
	
	if proxy.Timeout < 0 {
		return fmt.Errorf("代理超时时间不能为负数")
	}
	
	return nil
}

// LoadAndApplyRules 加载并应用规则到 Mock 管理器
func (cl *ConfigLoader) LoadAndApplyRules(manager *MockManager) error {
	rules, err := cl.LoadMockRules()
	if err != nil {
		return err
	}
	
	successCount := 0
	errorCount := 0
	
	for _, rule := range rules {
		if err := manager.RegisterMock(rule); err != nil {
			cl.logger.Error("注册 Mock 规则失败", "error", err, "id", rule.ID, "name", rule.Name)
			errorCount++
		} else {
			successCount++
		}
	}
	
	cl.logger.Info("Mock 规则应用完成",
		"success", successCount,
		"error", errorCount,
		"total", len(rules))
	
	if errorCount > 0 {
		return fmt.Errorf("部分 Mock 规则注册失败: 成功 %d, 失败 %d", successCount, errorCount)
	}
	
	return nil
}

// SaveRule 保存规则到文件
func (cl *ConfigLoader) SaveRule(rule *MockRule, filename string) error {
	if cl.configDir == "" {
		return fmt.Errorf("Mock 配置目录未设置")
	}
	
	// 确保目录存在
	if err := os.MkdirAll(cl.configDir, 0755); err != nil {
		return fmt.Errorf("创建 Mock 配置目录失败: %w", err)
	}
	
	filePath := filepath.Join(cl.configDir, filename)
	data, err := json.MarshalIndent(rule, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化 Mock 规则失败: %w", err)
	}
	
	if err := os.WriteFile(filePath, data, 0644); err != nil {
		return fmt.Errorf("写入 Mock 规则文件失败: %w", err)
	}
	
	cl.logger.Info("保存 Mock 规则到文件", "file", filePath, "id", rule.ID, "name", rule.Name)
	return nil
}

// SaveRules 保存规则数组到文件
func (cl *ConfigLoader) SaveRules(rules []*MockRule, filename string) error {
	if cl.configDir == "" {
		return fmt.Errorf("Mock 配置目录未设置")
	}
	
	// 确保目录存在
	if err := os.MkdirAll(cl.configDir, 0755); err != nil {
		return fmt.Errorf("创建 Mock 配置目录失败: %w", err)
	}
	
	filePath := filepath.Join(cl.configDir, filename)
	data, err := json.MarshalIndent(rules, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化 Mock 规则失败: %w", err)
	}
	
	if err := os.WriteFile(filePath, data, 0644); err != nil {
		return fmt.Errorf("写入 Mock 规则文件失败: %w", err)
	}
	
	cl.logger.Info("保存 Mock 规则数组到文件", "file", filePath, "count", len(rules))
	return nil
}
