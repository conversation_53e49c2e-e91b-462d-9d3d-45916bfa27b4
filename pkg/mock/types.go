package mock

import (
	"time"
)

// MockRequest Mock 请求
type MockRequest struct {
	Method      string            `json:"method"`       // HTTP 方法
	Path        string            `json:"path"`         // 请求路径
	Headers     map[string]string `json:"headers"`      // 请求头
	QueryParams map[string]string `json:"query_params"` // 查询参数
	Body        []byte            `json:"body"`         // 请求体
	RemoteAddr  string            `json:"remote_addr"`  // 客户端地址
	UserAgent   string            `json:"user_agent"`   // 用户代理
	Timestamp   time.Time         `json:"timestamp"`    // 请求时间
	RequestID   string            `json:"request_id"`   // 请求ID
}

// MockResult Mock 结果
type MockResult struct {
	Matched     bool              `json:"matched"`      // 是否匹配
	Rule        *MockRule         `json:"rule"`         // 匹配的规则
	Scenario    *MockScenario     `json:"scenario"`     // 匹配的场景
	Response    *GeneratedResponse `json:"response"`    // 生成的响应
	Delay       time.Duration     `json:"delay"`        // 实际延迟
	ProcessTime time.Duration     `json:"process_time"` // 处理时间
	Error       error             `json:"error"`        // 错误信息
	Metadata    map[string]interface{} `json:"metadata"` // 元数据
}

// GeneratedResponse 生成的响应
type GeneratedResponse struct {
	StatusCode int                    `json:"status_code"` // HTTP 状态码
	Headers    map[string]string      `json:"headers"`     // 响应头
	Body       []byte                 `json:"body"`        // 响应体
	BodyType   ResponseBodyType       `json:"body_type"`   // 响应体类型
	Size       int                    `json:"size"`        // 响应大小
	Generated  bool                   `json:"generated"`   // 是否为生成的响应
	Source     ResponseSource         `json:"source"`      // 响应来源
	Metadata   map[string]interface{} `json:"metadata"`    // 响应元数据
}

// ResponseSource 响应来源
type ResponseSource string
const (
	ResponseSourceStatic   ResponseSource = "static"   // 静态响应
	ResponseSourceTemplate ResponseSource = "template" // 模板响应
	ResponseSourceSchema   ResponseSource = "schema"   // 结构化响应
	ResponseSourceFile     ResponseSource = "file"     // 文件响应
	ResponseSourceProxy    ResponseSource = "proxy"    // 代理响应
	ResponseSourceDynamic  ResponseSource = "dynamic"  // 动态响应
	ResponseSourceFaker    ResponseSource = "faker"    // 生成器响应
)

// MockStats Mock 统计信息
type MockStats struct {
	RuleID         string            `json:"rule_id"`         // 规则ID
	RuleName       string            `json:"rule_name"`       // 规则名称
	RequestCount   int64             `json:"request_count"`   // 请求数量
	MatchCount     int64             `json:"match_count"`     // 匹配数量
	ErrorCount     int64             `json:"error_count"`     // 错误数量
	AvgDelay       float64           `json:"avg_delay"`       // 平均延迟
	AvgProcessTime float64           `json:"avg_process_time"` // 平均处理时间
	LastUsed       time.Time         `json:"last_used"`       // 最后使用时间
	ScenarioStats  map[string]int64  `json:"scenario_stats"`  // 场景统计
	StatusCodes    map[int]int64     `json:"status_codes"`    // 状态码统计
	Metadata       map[string]string `json:"metadata"`        // 统计元数据
}

// MockEvent Mock 事件
type MockEvent struct {
	ID        string                 `json:"id"`         // 事件ID
	Type      MockEventType          `json:"type"`       // 事件类型
	RuleID    string                 `json:"rule_id"`    // 规则ID
	Timestamp time.Time              `json:"timestamp"`  // 事件时间
	Request   *MockRequest           `json:"request"`    // 请求信息
	Response  *GeneratedResponse     `json:"response"`   // 响应信息
	Duration  time.Duration          `json:"duration"`   // 处理时长
	Error     string                 `json:"error"`      // 错误信息
	Metadata  map[string]interface{} `json:"metadata"`   // 事件元数据
}

// MockEventType Mock 事件类型
type MockEventType string
const (
	MockEventTypeRequest   MockEventType = "request"   // 请求事件
	MockEventTypeMatch     MockEventType = "match"     // 匹配事件
	MockEventTypeResponse  MockEventType = "response"  // 响应事件
	MockEventTypeError     MockEventType = "error"     // 错误事件
	MockEventTypeTimeout   MockEventType = "timeout"   // 超时事件
	MockEventTypeProxy     MockEventType = "proxy"     // 代理事件
)

// MockTemplate Mock 模板
type MockTemplate struct {
	ID          string            `json:"id"`           // 模板ID
	Name        string            `json:"name"`         // 模板名称
	Description string            `json:"description"`  // 模板描述
	Category    string            `json:"category"`     // 模板分类
	Template    string            `json:"template"`     // 模板内容
	Variables   []TemplateVariable `json:"variables"`   // 模板变量
	Examples    []TemplateExample  `json:"examples"`    // 模板示例
	Tags        []string          `json:"tags"`         // 模板标签
	CreatedAt   time.Time         `json:"created_at"`   // 创建时间
	UpdatedAt   time.Time         `json:"updated_at"`   // 更新时间
	CreatedBy   string            `json:"created_by"`   // 创建者
}

// TemplateVariable 模板变量
type TemplateVariable struct {
	Name         string      `json:"name"`         // 变量名
	Type         string      `json:"type"`         // 变量类型
	Description  string      `json:"description"`  // 变量描述
	DefaultValue interface{} `json:"default_value"` // 默认值
	Required     bool        `json:"required"`     // 是否必需
	Options      []string    `json:"options"`      // 可选值
	Pattern      string      `json:"pattern"`      // 验证模式
}

// TemplateExample 模板示例
type TemplateExample struct {
	Name        string                 `json:"name"`        // 示例名称
	Description string                 `json:"description"` // 示例描述
	Variables   map[string]interface{} `json:"variables"`   // 变量值
	Output      string                 `json:"output"`      // 输出结果
}

// MockCollection Mock 集合
type MockCollection struct {
	ID          string            `json:"id"`           // 集合ID
	Name        string            `json:"name"`         // 集合名称
	Description string            `json:"description"`  // 集合描述
	Rules       []string          `json:"rules"`        // 规则ID列表
	Tags        []string          `json:"tags"`         // 集合标签
	Enabled     bool              `json:"enabled"`      // 是否启用
	CreatedAt   time.Time         `json:"created_at"`   // 创建时间
	UpdatedAt   time.Time         `json:"updated_at"`   // 更新时间
	CreatedBy   string            `json:"created_by"`   // 创建者
	Metadata    map[string]string `json:"metadata"`     // 集合元数据
}

// MockEnvironment Mock 环境
type MockEnvironment struct {
	ID          string                 `json:"id"`           // 环境ID
	Name        string                 `json:"name"`         // 环境名称
	Description string                 `json:"description"`  // 环境描述
	Variables   map[string]interface{} `json:"variables"`    // 环境变量
	Collections []string               `json:"collections"`  // 集合列表
	Active      bool                   `json:"active"`       // 是否激活
	CreatedAt   time.Time              `json:"created_at"`   // 创建时间
	UpdatedAt   time.Time              `json:"updated_at"`   // 更新时间
	CreatedBy   string                 `json:"created_by"`   // 创建者
}

// MockProxy Mock 代理配置
type MockProxy struct {
	ID          string            `json:"id"`           // 代理ID
	Name        string            `json:"name"`         // 代理名称
	Description string            `json:"description"`  // 代理描述
	TargetURL   string            `json:"target_url"`   // 目标URL
	Headers     map[string]string `json:"headers"`      // 代理请求头
	Timeout     time.Duration     `json:"timeout"`      // 超时时间
	Enabled     bool              `json:"enabled"`      // 是否启用
	Transform   bool              `json:"transform"`    // 是否转换响应
	Cache       bool              `json:"cache"`        // 是否缓存
	CacheTTL    time.Duration     `json:"cache_ttl"`    // 缓存TTL
	CreatedAt   time.Time         `json:"created_at"`   // 创建时间
	UpdatedAt   time.Time         `json:"updated_at"`   // 更新时间
}

// MockValidation Mock 验证结果
type MockValidation struct {
	Valid   bool     `json:"valid"`   // 是否有效
	Errors  []string `json:"errors"`  // 错误列表
	Warnings []string `json:"warnings"` // 警告列表
}

// MockImportResult Mock 导入结果
type MockImportResult struct {
	Success     int      `json:"success"`     // 成功数量
	Failed      int      `json:"failed"`      // 失败数量
	Skipped     int      `json:"skipped"`     // 跳过数量
	Errors      []string `json:"errors"`      // 错误列表
	ImportedIDs []string `json:"imported_ids"` // 导入的ID列表
}

// MockExportResult Mock 导出结果
type MockExportResult struct {
	Format    string    `json:"format"`     // 导出格式
	Size      int       `json:"size"`       // 导出大小
	Count     int       `json:"count"`      // 导出数量
	Timestamp time.Time `json:"timestamp"`  // 导出时间
	Checksum  string    `json:"checksum"`   // 校验和
}

// MockHealth Mock 健康状态
type MockHealth struct {
	Status      string            `json:"status"`       // 健康状态
	Enabled     bool              `json:"enabled"`      // 是否启用
	RuleCount   int               `json:"rule_count"`   // 规则数量
	ActiveRules int               `json:"active_rules"` // 活跃规则数量
	LastRequest time.Time         `json:"last_request"` // 最后请求时间
	Uptime      time.Duration     `json:"uptime"`       // 运行时间
	Memory      int64             `json:"memory"`       // 内存使用
	Issues      []HealthIssue     `json:"issues"`       // 健康问题
	Metadata    map[string]string `json:"metadata"`     // 健康元数据
}

// HealthIssue 健康问题
type HealthIssue struct {
	Type        string    `json:"type"`         // 问题类型
	Severity    string    `json:"severity"`     // 严重程度
	Message     string    `json:"message"`      // 问题描述
	RuleID      string    `json:"rule_id"`      // 相关规则ID
	Timestamp   time.Time `json:"timestamp"`    // 发生时间
	Resolved    bool      `json:"resolved"`     // 是否已解决
	ResolvedAt  *time.Time `json:"resolved_at"` // 解决时间
}

// DefaultMockConfig 默认 Mock 配置
func DefaultMockConfig() MockConfig {
	return MockConfig{
		Enabled:         true,
		DefaultDelay:    0,
		MaxDelay:        5000,
		EnableFaker:     true,
		EnableScenarios: true,
		EnableProxy:     true,
		CacheEnabled:    true,
		CacheTTL:        5 * time.Minute,
		LogRequests:     true,
		LogResponses:    false,
		StrictMode:      false,
	}
}

// DevelopmentMockConfig 开发环境 Mock 配置
func DevelopmentMockConfig() MockConfig {
	config := DefaultMockConfig()
	config.LogRequests = true
	config.LogResponses = true
	config.StrictMode = false
	return config
}

// ProductionMockConfig 生产环境 Mock 配置
func ProductionMockConfig() MockConfig {
	config := DefaultMockConfig()
	config.LogRequests = false
	config.LogResponses = false
	config.StrictMode = true
	config.EnableFaker = false // 生产环境禁用数据生成器
	return config
}
