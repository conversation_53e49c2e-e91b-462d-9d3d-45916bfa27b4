package mock

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"paas-platform/pkg/logger"
)

// MockManager Mock 服务管理器
type MockManager struct {
	mocks   map[string]*MockRule
	mutex   sync.RWMutex
	logger  logger.Logger
	config  MockConfig
	faker   *DataFaker
}

// MockRule Mock 规则
type MockRule struct {
	ID          string            `json:"id"`           // 规则ID
	Name        string            `json:"name"`         // 规则名称
	Description string            `json:"description"`  // 规则描述
	Path        string            `json:"path"`         // API 路径
	Method      string            `json:"method"`       // HTTP 方法
	Enabled     bool              `json:"enabled"`      // 是否启用
	Priority    int               `json:"priority"`     // 优先级
	
	// 匹配条件
	Conditions  []MockCondition   `json:"conditions"`   // 匹配条件
	Headers     map[string]string `json:"headers"`      // 请求头匹配
	QueryParams map[string]string `json:"query_params"` // 查询参数匹配
	
	// 响应配置
	Response    MockResponse      `json:"response"`     // 响应配置
	Delay       *DelayConfig      `json:"delay"`        // 延迟配置
	
	// 高级功能
	Scenarios   []MockScenario    `json:"scenarios"`    // 场景配置
	Fallback    *FallbackConfig   `json:"fallback"`     // 降级配置
	
	// 元数据
	Tags        []string          `json:"tags"`         // 标签
	CreatedAt   time.Time         `json:"created_at"`   // 创建时间
	UpdatedAt   time.Time         `json:"updated_at"`   // 更新时间
	CreatedBy   string            `json:"created_by"`   // 创建者
	Metadata    map[string]string `json:"metadata"`     // 元数据
}

// MockCondition Mock 匹配条件
type MockCondition struct {
	Type      ConditionType `json:"type"`       // 条件类型
	Field     string        `json:"field"`      // 字段名
	Operator  Operator      `json:"operator"`   // 操作符
	Value     interface{}   `json:"value"`      // 期望值
	Values    []interface{} `json:"values"`     // 多个值（用于 in 操作）
	Pattern   string        `json:"pattern"`    // 正则表达式
	CaseSensitive bool      `json:"case_sensitive"` // 是否区分大小写
}

// MockResponse Mock 响应配置
type MockResponse struct {
	StatusCode  int                    `json:"status_code"`  // HTTP 状态码
	Headers     map[string]string      `json:"headers"`      // 响应头
	Body        interface{}            `json:"body"`         // 响应体
	BodyType    ResponseBodyType       `json:"body_type"`    // 响应体类型
	Template    string                 `json:"template"`     // 响应模板
	Schema      *ResponseSchema        `json:"schema"`       // 响应结构
	Dynamic     *DynamicResponse       `json:"dynamic"`      // 动态响应
	File        string                 `json:"file"`         // 文件路径
	Proxy       *ProxyConfig           `json:"proxy"`        // 代理配置
}

// MockScenario Mock 场景
type MockScenario struct {
	Name        string            `json:"name"`         // 场景名称
	Description string            `json:"description"`  // 场景描述
	Conditions  []MockCondition   `json:"conditions"`   // 场景条件
	Response    MockResponse      `json:"response"`     // 场景响应
	Probability float64           `json:"probability"`  // 触发概率
	Weight      int               `json:"weight"`       // 权重
	Enabled     bool              `json:"enabled"`      // 是否启用
}

// DelayConfig 延迟配置
type DelayConfig struct {
	Type     DelayType `json:"type"`      // 延迟类型
	Fixed    int       `json:"fixed"`     // 固定延迟(ms)
	Min      int       `json:"min"`       // 最小延迟(ms)
	Max      int       `json:"max"`       // 最大延迟(ms)
	Mean     float64   `json:"mean"`      // 平均延迟(ms)
	StdDev   float64   `json:"std_dev"`   // 标准差(ms)
}

// FallbackConfig 降级配置
type FallbackConfig struct {
	Enabled     bool          `json:"enabled"`      // 是否启用降级
	ProxyURL    string        `json:"proxy_url"`    // 降级代理URL
	Response    MockResponse  `json:"response"`     // 降级响应
	Conditions  []string      `json:"conditions"`   // 降级条件
	Timeout     time.Duration `json:"timeout"`      // 超时时间
}

// ResponseSchema 响应结构定义
type ResponseSchema struct {
	Type       string                    `json:"type"`       // 数据类型
	Properties map[string]*ResponseSchema `json:"properties"` // 属性定义
	Items      *ResponseSchema           `json:"items"`      // 数组项定义
	Format     string                    `json:"format"`     // 数据格式
	Example    interface{}               `json:"example"`    // 示例值
	Faker      string                    `json:"faker"`      // Faker 类型
	Min        *int                      `json:"min"`        // 最小值
	Max        *int                      `json:"max"`        // 最大值
	Length     *int                      `json:"length"`     // 长度
	Pattern    string                    `json:"pattern"`    // 正则模式
	Enum       []interface{}             `json:"enum"`       // 枚举值
}

// DynamicResponse 动态响应配置
type DynamicResponse struct {
	Enabled     bool              `json:"enabled"`      // 是否启用动态响应
	Script      string            `json:"script"`       // JavaScript 脚本
	Template    string            `json:"template"`     // 模板字符串
	Variables   map[string]string `json:"variables"`    // 变量定义
	Functions   []string          `json:"functions"`    // 可用函数
}

// ProxyConfig 代理配置
type ProxyConfig struct {
	URL         string            `json:"url"`          // 代理URL
	Headers     map[string]string `json:"headers"`      // 代理请求头
	Timeout     time.Duration     `json:"timeout"`      // 超时时间
	Transform   bool              `json:"transform"`    // 是否转换响应
}

// MockConfig Mock 服务配置
type MockConfig struct {
	Enabled         bool          `json:"enabled"`          // 是否启用 Mock
	DefaultDelay    int           `json:"default_delay"`    // 默认延迟(ms)
	MaxDelay        int           `json:"max_delay"`        // 最大延迟(ms)
	EnableFaker     bool          `json:"enable_faker"`     // 启用数据生成器
	EnableScenarios bool          `json:"enable_scenarios"` // 启用场景模式
	EnableProxy     bool          `json:"enable_proxy"`     // 启用代理模式
	CacheEnabled    bool          `json:"cache_enabled"`    // 启用缓存
	CacheTTL        time.Duration `json:"cache_ttl"`        // 缓存TTL
	LogRequests     bool          `json:"log_requests"`     // 记录请求日志
	LogResponses    bool          `json:"log_responses"`    // 记录响应日志
	StrictMode      bool          `json:"strict_mode"`      // 严格模式
}

// 枚举类型定义
type ConditionType string
const (
	ConditionTypeHeader     ConditionType = "header"      // 请求头条件
	ConditionTypeQuery      ConditionType = "query"       // 查询参数条件
	ConditionTypeBody       ConditionType = "body"        // 请求体条件
	ConditionTypePath       ConditionType = "path"        // 路径条件
	ConditionTypeMethod     ConditionType = "method"      // 方法条件
	ConditionTypeUserAgent  ConditionType = "user_agent"  // User-Agent条件
	ConditionTypeIP         ConditionType = "ip"          // IP地址条件
	ConditionTypeTime       ConditionType = "time"        // 时间条件
	ConditionTypeRandom     ConditionType = "random"      // 随机条件
)

type Operator string
const (
	OperatorEquals      Operator = "equals"       // 等于
	OperatorNotEquals   Operator = "not_equals"   // 不等于
	OperatorContains    Operator = "contains"     // 包含
	OperatorNotContains Operator = "not_contains" // 不包含
	OperatorStartsWith  Operator = "starts_with"  // 开始于
	OperatorEndsWith    Operator = "ends_with"    // 结束于
	OperatorRegex       Operator = "regex"        // 正则匹配
	OperatorIn          Operator = "in"           // 在列表中
	OperatorNotIn       Operator = "not_in"       // 不在列表中
	OperatorGreater     Operator = "greater"      // 大于
	OperatorLess        Operator = "less"         // 小于
	OperatorExists      Operator = "exists"       // 存在
	OperatorNotExists   Operator = "not_exists"   // 不存在
)

type ResponseBodyType string
const (
	ResponseBodyTypeJSON     ResponseBodyType = "json"     // JSON 响应
	ResponseBodyTypeXML      ResponseBodyType = "xml"      // XML 响应
	ResponseBodyTypeText     ResponseBodyType = "text"     // 文本响应
	ResponseBodyTypeHTML     ResponseBodyType = "html"     // HTML 响应
	ResponseBodyTypeBinary   ResponseBodyType = "binary"   // 二进制响应
	ResponseBodyTypeTemplate ResponseBodyType = "template" // 模板响应
	ResponseBodyTypeSchema   ResponseBodyType = "schema"   // 结构化响应
	ResponseBodyTypeFile     ResponseBodyType = "file"     // 文件响应
	ResponseBodyTypeProxy    ResponseBodyType = "proxy"    // 代理响应
)

type DelayType string
const (
	DelayTypeFixed    DelayType = "fixed"    // 固定延迟
	DelayTypeRandom   DelayType = "random"   // 随机延迟
	DelayTypeNormal   DelayType = "normal"   // 正态分布延迟
)

// NewMockManager 创建 Mock 管理器
func NewMockManager(config MockConfig, logger logger.Logger) *MockManager {
	return &MockManager{
		mocks:  make(map[string]*MockRule),
		logger: logger,
		config: config,
		faker:  NewDataFaker(),
	}
}

// RegisterMock 注册 Mock 规则
func (mm *MockManager) RegisterMock(rule *MockRule) error {
	if rule.ID == "" {
		return fmt.Errorf("Mock 规则 ID 不能为空")
	}
	
	if rule.Path == "" || rule.Method == "" {
		return fmt.Errorf("Mock 规则路径和方法不能为空")
	}
	
	// 验证规则
	if err := mm.validateMockRule(rule); err != nil {
		return fmt.Errorf("Mock 规则验证失败: %w", err)
	}
	
	mm.mutex.Lock()
	defer mm.mutex.Unlock()
	
	// 设置默认值
	if rule.CreatedAt.IsZero() {
		rule.CreatedAt = time.Now()
	}
	rule.UpdatedAt = time.Now()
	
	if rule.Response.StatusCode == 0 {
		rule.Response.StatusCode = 200
	}
	
	if rule.Response.Headers == nil {
		rule.Response.Headers = make(map[string]string)
	}
	
	if rule.Metadata == nil {
		rule.Metadata = make(map[string]string)
	}
	
	mm.mocks[rule.ID] = rule
	
	mm.logger.Info("注册 Mock 规则",
		"id", rule.ID,
		"name", rule.Name,
		"path", rule.Path,
		"method", rule.Method,
		"enabled", rule.Enabled)
	
	return nil
}

// validateMockRule 验证 Mock 规则
func (mm *MockManager) validateMockRule(rule *MockRule) error {
	// 验证路径格式
	if !strings.HasPrefix(rule.Path, "/") {
		return fmt.Errorf("路径必须以 / 开头")
	}
	
	// 验证 HTTP 方法
	validMethods := []string{"GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"}
	validMethod := false
	for _, method := range validMethods {
		if rule.Method == method {
			validMethod = true
			break
		}
	}
	if !validMethod {
		return fmt.Errorf("无效的 HTTP 方法: %s", rule.Method)
	}
	
	// 验证状态码
	if rule.Response.StatusCode < 100 || rule.Response.StatusCode > 599 {
		return fmt.Errorf("无效的 HTTP 状态码: %d", rule.Response.StatusCode)
	}
	
	// 验证条件
	for i, condition := range rule.Conditions {
		if err := mm.validateCondition(&condition); err != nil {
			return fmt.Errorf("条件 %d 验证失败: %w", i, err)
		}
	}
	
	// 验证场景
	for i, scenario := range rule.Scenarios {
		if err := mm.validateScenario(&scenario); err != nil {
			return fmt.Errorf("场景 %d 验证失败: %w", i, err)
		}
	}
	
	return nil
}

// validateCondition 验证条件
func (mm *MockManager) validateCondition(condition *MockCondition) error {
	if condition.Field == "" && condition.Type != ConditionTypeRandom {
		return fmt.Errorf("条件字段不能为空")
	}
	
	// 验证操作符
	validOperators := []Operator{
		OperatorEquals, OperatorNotEquals, OperatorContains, OperatorNotContains,
		OperatorStartsWith, OperatorEndsWith, OperatorRegex, OperatorIn, OperatorNotIn,
		OperatorGreater, OperatorLess, OperatorExists, OperatorNotExists,
	}
	
	validOperator := false
	for _, op := range validOperators {
		if condition.Operator == op {
			validOperator = true
			break
		}
	}
	if !validOperator {
		return fmt.Errorf("无效的操作符: %s", condition.Operator)
	}
	
	// 验证正则表达式
	if condition.Operator == OperatorRegex && condition.Pattern != "" {
		if _, err := regexp.Compile(condition.Pattern); err != nil {
			return fmt.Errorf("无效的正则表达式: %w", err)
		}
	}
	
	return nil
}

// validateScenario 验证场景
func (mm *MockManager) validateScenario(scenario *MockScenario) error {
	if scenario.Name == "" {
		return fmt.Errorf("场景名称不能为空")
	}
	
	if scenario.Probability < 0 || scenario.Probability > 1 {
		return fmt.Errorf("场景概率必须在 0-1 之间")
	}
	
	// 验证场景条件
	for i, condition := range scenario.Conditions {
		if err := mm.validateCondition(&condition); err != nil {
			return fmt.Errorf("场景条件 %d 验证失败: %w", i, err)
		}
	}
	
	return nil
}

// FindMatchingMock 查找匹配的 Mock 规则
func (mm *MockManager) FindMatchingMock(request *MockRequest) (*MockRule, *MockScenario, error) {
	if !mm.config.Enabled {
		return nil, nil, fmt.Errorf("Mock 服务未启用")
	}

	mm.mutex.RLock()
	defer mm.mutex.RUnlock()

	var candidates []*MockRule

	// 查找匹配的规则
	for _, rule := range mm.mocks {
		if !rule.Enabled {
			continue
		}

		if mm.matchRule(rule, request) {
			candidates = append(candidates, rule)
		}
	}

	if len(candidates) == 0 {
		return nil, nil, fmt.Errorf("未找到匹配的 Mock 规则")
	}

	// 按优先级排序
	mm.sortRulesByPriority(candidates)

	// 选择最匹配的规则
	selectedRule := candidates[0]

	// 查找匹配的场景
	var selectedScenario *MockScenario
	if mm.config.EnableScenarios && len(selectedRule.Scenarios) > 0 {
		selectedScenario = mm.findMatchingScenario(selectedRule, request)
	}

	mm.logger.Debug("找到匹配的 Mock 规则",
		"rule_id", selectedRule.ID,
		"rule_name", selectedRule.Name,
		"scenario", selectedScenario != nil)

	return selectedRule, selectedScenario, nil
}

// matchRule 检查规则是否匹配请求
func (mm *MockManager) matchRule(rule *MockRule, request *MockRequest) bool {
	// 检查路径和方法
	if !mm.matchPath(rule.Path, request.Path) {
		return false
	}

	if rule.Method != "*" && rule.Method != request.Method {
		return false
	}

	// 检查请求头匹配
	for key, expectedValue := range rule.Headers {
		actualValue, exists := request.Headers[key]
		if !exists || actualValue != expectedValue {
			return false
		}
	}

	// 检查查询参数匹配
	for key, expectedValue := range rule.QueryParams {
		actualValue, exists := request.QueryParams[key]
		if !exists || actualValue != expectedValue {
			return false
		}
	}

	// 检查条件匹配
	for _, condition := range rule.Conditions {
		if !mm.evaluateCondition(&condition, request) {
			return false
		}
	}

	return true
}

// matchPath 路径匹配
func (mm *MockManager) matchPath(pattern, path string) bool {
	// 支持通配符匹配
	if pattern == "*" {
		return true
	}

	// 精确匹配
	if pattern == path {
		return true
	}

	// 支持路径参数匹配 /api/users/{id}
	patternParts := strings.Split(pattern, "/")
	pathParts := strings.Split(path, "/")

	if len(patternParts) != len(pathParts) {
		return false
	}

	for i, part := range patternParts {
		if strings.HasPrefix(part, "{") && strings.HasSuffix(part, "}") {
			// 路径参数，跳过
			continue
		}
		if part != pathParts[i] {
			return false
		}
	}

	return true
}

// evaluateCondition 评估条件
func (mm *MockManager) evaluateCondition(condition *MockCondition, request *MockRequest) bool {
	var actualValue interface{}

	// 获取实际值
	switch condition.Type {
	case ConditionTypeHeader:
		actualValue = request.Headers[condition.Field]
	case ConditionTypeQuery:
		actualValue = request.QueryParams[condition.Field]
	case ConditionTypeBody:
		actualValue = mm.getBodyField(request.Body, condition.Field)
	case ConditionTypePath:
		actualValue = request.Path
	case ConditionTypeMethod:
		actualValue = request.Method
	case ConditionTypeUserAgent:
		actualValue = request.UserAgent
	case ConditionTypeIP:
		actualValue = request.RemoteAddr
	case ConditionTypeTime:
		actualValue = request.Timestamp.Format("15:04:05")
	case ConditionTypeRandom:
		actualValue = rand.Float64()
	default:
		return false
	}

	// 执行比较
	return mm.compareValues(actualValue, condition.Operator, condition.Value, condition.Values, condition.Pattern, condition.CaseSensitive)
}

// compareValues 比较值
func (mm *MockManager) compareValues(actual interface{}, operator Operator, expected interface{}, expectedList []interface{}, pattern string, caseSensitive bool) bool {
	actualStr := fmt.Sprintf("%v", actual)
	expectedStr := fmt.Sprintf("%v", expected)

	// 处理大小写敏感性
	if !caseSensitive {
		actualStr = strings.ToLower(actualStr)
		expectedStr = strings.ToLower(expectedStr)
	}

	switch operator {
	case OperatorEquals:
		return actualStr == expectedStr
	case OperatorNotEquals:
		return actualStr != expectedStr
	case OperatorContains:
		return strings.Contains(actualStr, expectedStr)
	case OperatorNotContains:
		return !strings.Contains(actualStr, expectedStr)
	case OperatorStartsWith:
		return strings.HasPrefix(actualStr, expectedStr)
	case OperatorEndsWith:
		return strings.HasSuffix(actualStr, expectedStr)
	case OperatorRegex:
		if pattern != "" {
			matched, _ := regexp.MatchString(pattern, actualStr)
			return matched
		}
		return false
	case OperatorIn:
		for _, item := range expectedList {
			if fmt.Sprintf("%v", item) == actualStr {
				return true
			}
		}
		return false
	case OperatorNotIn:
		for _, item := range expectedList {
			if fmt.Sprintf("%v", item) == actualStr {
				return false
			}
		}
		return true
	case OperatorGreater:
		actualNum, err1 := strconv.ParseFloat(actualStr, 64)
		expectedNum, err2 := strconv.ParseFloat(expectedStr, 64)
		if err1 == nil && err2 == nil {
			return actualNum > expectedNum
		}
		return false
	case OperatorLess:
		actualNum, err1 := strconv.ParseFloat(actualStr, 64)
		expectedNum, err2 := strconv.ParseFloat(expectedStr, 64)
		if err1 == nil && err2 == nil {
			return actualNum < expectedNum
		}
		return false
	case OperatorExists:
		return actual != nil && actualStr != ""
	case OperatorNotExists:
		return actual == nil || actualStr == ""
	default:
		return false
	}
}

// getBodyField 从请求体中获取字段值
func (mm *MockManager) getBodyField(body []byte, field string) interface{} {
	if len(body) == 0 {
		return nil
	}

	var data map[string]interface{}
	if err := json.Unmarshal(body, &data); err != nil {
		return nil
	}

	// 支持嵌套字段访问 user.name
	fields := strings.Split(field, ".")
	current := data

	for i, f := range fields {
		if i == len(fields)-1 {
			return current[f]
		}

		if next, ok := current[f].(map[string]interface{}); ok {
			current = next
		} else {
			return nil
		}
	}

	return nil
}

// findMatchingScenario 查找匹配的场景
func (mm *MockManager) findMatchingScenario(rule *MockRule, request *MockRequest) *MockScenario {
	var candidates []*MockScenario

	// 查找匹配的场景
	for i := range rule.Scenarios {
		scenario := &rule.Scenarios[i]
		if !scenario.Enabled {
			continue
		}

		// 检查场景条件
		matched := true
		for _, condition := range scenario.Conditions {
			if !mm.evaluateCondition(&condition, request) {
				matched = false
				break
			}
		}

		if matched {
			// 检查概率
			if scenario.Probability > 0 && rand.Float64() > scenario.Probability {
				continue
			}

			candidates = append(candidates, scenario)
		}
	}

	if len(candidates) == 0 {
		return nil
	}

	// 按权重选择场景
	return mm.selectScenarioByWeight(candidates)
}

// selectScenarioByWeight 按权重选择场景
func (mm *MockManager) selectScenarioByWeight(scenarios []*MockScenario) *MockScenario {
	if len(scenarios) == 1 {
		return scenarios[0]
	}

	// 计算总权重
	totalWeight := 0
	for _, scenario := range scenarios {
		totalWeight += scenario.Weight
	}

	if totalWeight == 0 {
		// 如果没有权重，随机选择
		return scenarios[rand.Intn(len(scenarios))]
	}

	// 按权重随机选择
	randomWeight := rand.Intn(totalWeight)
	currentWeight := 0

	for _, scenario := range scenarios {
		currentWeight += scenario.Weight
		if randomWeight < currentWeight {
			return scenario
		}
	}

	return scenarios[0]
}

// sortRulesByPriority 按优先级排序规则
func (mm *MockManager) sortRulesByPriority(rules []*MockRule) {
	// 简单的冒泡排序，按优先级降序
	for i := 0; i < len(rules)-1; i++ {
		for j := 0; j < len(rules)-i-1; j++ {
			if rules[j].Priority < rules[j+1].Priority {
				rules[j], rules[j+1] = rules[j+1], rules[j]
			}
		}
	}
}

// ProcessMockRequest 处理 Mock 请求
func (mm *MockManager) ProcessMockRequest(request *MockRequest) (*MockResult, error) {
	startTime := time.Now()

	result := &MockResult{
		Matched:     false,
		ProcessTime: 0,
		Metadata:    make(map[string]interface{}),
	}

	// 查找匹配的规则
	rule, scenario, err := mm.FindMatchingMock(request)
	if err != nil {
		result.Error = err
		result.ProcessTime = time.Since(startTime)
		return result, nil // 不匹配不算错误
	}

	result.Matched = true
	result.Rule = rule
	result.Scenario = scenario

	// 生成响应
	generator := NewResponseGenerator(mm.faker, mm.logger, mm.config)
	response, err := generator.GenerateResponse(rule, scenario, request)
	if err != nil {
		result.Error = err
		result.ProcessTime = time.Since(startTime)
		return result, err
	}

	result.Response = response
	result.ProcessTime = time.Since(startTime)

	// 记录统计信息
	if mm.config.LogRequests {
		mm.recordMockUsage(rule, scenario, request, response)
	}

	mm.logger.Info("Mock 请求处理完成",
		"rule_id", rule.ID,
		"matched", true,
		"status_code", response.StatusCode,
		"process_time", result.ProcessTime)

	return result, nil
}

// recordMockUsage 记录 Mock 使用情况
func (mm *MockManager) recordMockUsage(rule *MockRule, scenario *MockScenario, request *MockRequest, response *GeneratedResponse) {
	// 这里可以实现统计信息记录
	// 实际项目中可以集成到监控系统
	mm.logger.Debug("记录 Mock 使用情况",
		"rule_id", rule.ID,
		"rule_name", rule.Name,
		"method", request.Method,
		"path", request.Path,
		"status_code", response.StatusCode,
		"response_size", response.Size)
}

// GetMock 获取 Mock 规则
func (mm *MockManager) GetMock(id string) (*MockRule, bool) {
	mm.mutex.RLock()
	defer mm.mutex.RUnlock()

	rule, exists := mm.mocks[id]
	return rule, exists
}

// GetAllMocks 获取所有 Mock 规则
func (mm *MockManager) GetAllMocks() []*MockRule {
	mm.mutex.RLock()
	defer mm.mutex.RUnlock()

	rules := make([]*MockRule, 0, len(mm.mocks))
	for _, rule := range mm.mocks {
		rules = append(rules, rule)
	}

	return rules
}

// UpdateMock 更新 Mock 规则
func (mm *MockManager) UpdateMock(rule *MockRule) error {
	if rule.ID == "" {
		return fmt.Errorf("Mock 规则 ID 不能为空")
	}

	// 验证规则
	if err := mm.validateMockRule(rule); err != nil {
		return fmt.Errorf("Mock 规则验证失败: %w", err)
	}

	mm.mutex.Lock()
	defer mm.mutex.Unlock()

	// 检查规则是否存在
	if _, exists := mm.mocks[rule.ID]; !exists {
		return fmt.Errorf("Mock 规则不存在: %s", rule.ID)
	}

	rule.UpdatedAt = time.Now()
	mm.mocks[rule.ID] = rule

	mm.logger.Info("更新 Mock 规则",
		"id", rule.ID,
		"name", rule.Name,
		"enabled", rule.Enabled)

	return nil
}

// DeleteMock 删除 Mock 规则
func (mm *MockManager) DeleteMock(id string) error {
	mm.mutex.Lock()
	defer mm.mutex.Unlock()

	if _, exists := mm.mocks[id]; !exists {
		return fmt.Errorf("Mock 规则不存在: %s", id)
	}

	delete(mm.mocks, id)

	mm.logger.Info("删除 Mock 规则", "id", id)

	return nil
}

// EnableMock 启用 Mock 规则
func (mm *MockManager) EnableMock(id string) error {
	mm.mutex.Lock()
	defer mm.mutex.Unlock()

	rule, exists := mm.mocks[id]
	if !exists {
		return fmt.Errorf("Mock 规则不存在: %s", id)
	}

	rule.Enabled = true
	rule.UpdatedAt = time.Now()

	mm.logger.Info("启用 Mock 规则", "id", id, "name", rule.Name)

	return nil
}

// DisableMock 禁用 Mock 规则
func (mm *MockManager) DisableMock(id string) error {
	mm.mutex.Lock()
	defer mm.mutex.Unlock()

	rule, exists := mm.mocks[id]
	if !exists {
		return fmt.Errorf("Mock 规则不存在: %s", id)
	}

	rule.Enabled = false
	rule.UpdatedAt = time.Now()

	mm.logger.Info("禁用 Mock 规则", "id", id, "name", rule.Name)

	return nil
}

// GetMocksByPath 根据路径获取 Mock 规则
func (mm *MockManager) GetMocksByPath(path string) []*MockRule {
	mm.mutex.RLock()
	defer mm.mutex.RUnlock()

	var rules []*MockRule
	for _, rule := range mm.mocks {
		if mm.matchPath(rule.Path, path) {
			rules = append(rules, rule)
		}
	}

	return rules
}

// GetMocksByTag 根据标签获取 Mock 规则
func (mm *MockManager) GetMocksByTag(tag string) []*MockRule {
	mm.mutex.RLock()
	defer mm.mutex.RUnlock()

	var rules []*MockRule
	for _, rule := range mm.mocks {
		for _, ruleTag := range rule.Tags {
			if ruleTag == tag {
				rules = append(rules, rule)
				break
			}
		}
	}

	return rules
}
