package mock

import (
	"bytes"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"io"
	"net/http"
	"os"
	"text/template"
	"time"

	"paas-platform/pkg/logger"
)

// ResponseGenerator 响应生成器
type ResponseGenerator struct {
	faker  *DataFaker
	logger logger.Logger
	config MockConfig
}

// NewResponseGenerator 创建响应生成器
func NewResponseGenerator(faker *DataFaker, logger logger.Logger, config MockConfig) *ResponseGenerator {
	return &ResponseGenerator{
		faker:  faker,
		logger: logger,
		config: config,
	}
}

// GenerateResponse 生成响应
func (rg *ResponseGenerator) GenerateResponse(rule *MockRule, scenario *MockScenario, request *MockRequest) (*GeneratedResponse, error) {
	startTime := time.Now()
	
	// 选择响应配置
	var response MockResponse
	if scenario != nil {
		response = scenario.Response
	} else {
		response = rule.Response
	}
	
	// 生成响应
	generatedResponse, err := rg.generateResponseFromConfig(&response, request)
	if err != nil {
		return nil, fmt.Errorf("生成响应失败: %w", err)
	}
	
	// 应用延迟
	if rule.Delay != nil {
		delay := rg.calculateDelay(rule.Delay)
		if delay > 0 {
			time.Sleep(delay)
		}
	}
	
	// 设置元数据
	generatedResponse.Metadata = map[string]interface{}{
		"rule_id":      rule.ID,
		"rule_name":    rule.Name,
		"scenario":     scenario != nil,
		"process_time": time.Since(startTime).String(),
	}
	
	if scenario != nil {
		generatedResponse.Metadata["scenario_name"] = scenario.Name
	}
	
	rg.logger.Debug("生成响应完成",
		"rule_id", rule.ID,
		"status_code", generatedResponse.StatusCode,
		"body_size", generatedResponse.Size,
		"source", generatedResponse.Source)
	
	return generatedResponse, nil
}

// generateResponseFromConfig 根据配置生成响应
func (rg *ResponseGenerator) generateResponseFromConfig(response *MockResponse, request *MockRequest) (*GeneratedResponse, error) {
	result := &GeneratedResponse{
		StatusCode: response.StatusCode,
		Headers:    make(map[string]string),
		Generated:  true,
		Metadata:   make(map[string]interface{}),
	}
	
	// 复制响应头
	for key, value := range response.Headers {
		result.Headers[key] = value
	}
	
	// 设置默认 Content-Type
	if _, exists := result.Headers["Content-Type"]; !exists {
		switch response.BodyType {
		case ResponseBodyTypeJSON:
			result.Headers["Content-Type"] = "application/json"
		case ResponseBodyTypeXML:
			result.Headers["Content-Type"] = "application/xml"
		case ResponseBodyTypeHTML:
			result.Headers["Content-Type"] = "text/html"
		case ResponseBodyTypeText:
			result.Headers["Content-Type"] = "text/plain"
		default:
			result.Headers["Content-Type"] = "application/json"
		}
	}
	
	// 生成响应体
	var err error
	switch response.BodyType {
	case ResponseBodyTypeJSON:
		result.Body, result.Source, err = rg.generateJSONResponse(response, request)
	case ResponseBodyTypeXML:
		result.Body, result.Source, err = rg.generateXMLResponse(response, request)
	case ResponseBodyTypeText:
		result.Body, result.Source, err = rg.generateTextResponse(response, request)
	case ResponseBodyTypeHTML:
		result.Body, result.Source, err = rg.generateHTMLResponse(response, request)
	case ResponseBodyTypeTemplate:
		result.Body, result.Source, err = rg.generateTemplateResponse(response, request)
	case ResponseBodyTypeSchema:
		result.Body, result.Source, err = rg.generateSchemaResponse(response, request)
	case ResponseBodyTypeFile:
		result.Body, result.Source, err = rg.generateFileResponse(response, request)
	case ResponseBodyTypeProxy:
		result.Body, result.Source, err = rg.generateProxyResponse(response, request)
	default:
		result.Body, result.Source, err = rg.generateJSONResponse(response, request)
	}
	
	if err != nil {
		return nil, err
	}
	
	result.BodyType = response.BodyType
	result.Size = len(result.Body)
	
	return result, nil
}

// generateJSONResponse 生成 JSON 响应
func (rg *ResponseGenerator) generateJSONResponse(response *MockResponse, request *MockRequest) ([]byte, ResponseSource, error) {
	var data interface{}
	var source ResponseSource
	
	if response.Body != nil {
		// 使用静态数据
		data = response.Body
		source = ResponseSourceStatic
	} else if response.Schema != nil {
		// 使用结构生成数据
		data = rg.faker.GenerateFromSchema(response.Schema)
		source = ResponseSourceSchema
	} else if response.Dynamic != nil && response.Dynamic.Enabled {
		// 使用动态生成
		var err error
		data, err = rg.generateDynamicData(response.Dynamic, request)
		if err != nil {
			return nil, source, err
		}
		source = ResponseSourceDynamic
	} else {
		// 默认响应
		data = map[string]interface{}{
			"message": "Mock response",
			"timestamp": time.Now().Format(time.RFC3339),
			"request_id": request.RequestID,
		}
		source = ResponseSourceStatic
	}
	
	jsonData, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		return nil, source, fmt.Errorf("JSON 序列化失败: %w", err)
	}
	
	return jsonData, source, nil
}

// generateXMLResponse 生成 XML 响应
func (rg *ResponseGenerator) generateXMLResponse(response *MockResponse, request *MockRequest) ([]byte, ResponseSource, error) {
	var data interface{}
	var source ResponseSource
	
	if response.Body != nil {
		data = response.Body
		source = ResponseSourceStatic
	} else {
		data = map[string]interface{}{
			"message": "Mock XML response",
			"timestamp": time.Now().Format(time.RFC3339),
		}
		source = ResponseSourceStatic
	}
	
	xmlData, err := xml.MarshalIndent(data, "", "  ")
	if err != nil {
		return nil, source, fmt.Errorf("XML 序列化失败: %w", err)
	}
	
	return xmlData, source, nil
}

// generateTextResponse 生成文本响应
func (rg *ResponseGenerator) generateTextResponse(response *MockResponse, request *MockRequest) ([]byte, ResponseSource, error) {
	var text string
	var source ResponseSource
	
	if response.Body != nil {
		text = fmt.Sprintf("%v", response.Body)
		source = ResponseSourceStatic
	} else {
		text = "Mock text response"
		source = ResponseSourceStatic
	}
	
	return []byte(text), source, nil
}

// generateHTMLResponse 生成 HTML 响应
func (rg *ResponseGenerator) generateHTMLResponse(response *MockResponse, request *MockRequest) ([]byte, ResponseSource, error) {
	var html string
	var source ResponseSource
	
	if response.Body != nil {
		html = fmt.Sprintf("%v", response.Body)
		source = ResponseSourceStatic
	} else {
		html = `<!DOCTYPE html>
<html>
<head>
    <title>Mock Response</title>
</head>
<body>
    <h1>Mock HTML Response</h1>
    <p>This is a mock HTML response generated at ` + time.Now().Format(time.RFC3339) + `</p>
</body>
</html>`
		source = ResponseSourceStatic
	}
	
	return []byte(html), source, nil
}

// generateTemplateResponse 生成模板响应
func (rg *ResponseGenerator) generateTemplateResponse(response *MockResponse, request *MockRequest) ([]byte, ResponseSource, error) {
	if response.Template == "" {
		return nil, ResponseSourceTemplate, fmt.Errorf("模板内容为空")
	}
	
	// 创建模板
	tmpl, err := template.New("mock").Parse(response.Template)
	if err != nil {
		return nil, ResponseSourceTemplate, fmt.Errorf("模板解析失败: %w", err)
	}
	
	// 准备模板数据
	data := rg.prepareTemplateData(request)
	
	// 执行模板
	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, data); err != nil {
		return nil, ResponseSourceTemplate, fmt.Errorf("模板执行失败: %w", err)
	}
	
	return buf.Bytes(), ResponseSourceTemplate, nil
}

// generateSchemaResponse 生成结构化响应
func (rg *ResponseGenerator) generateSchemaResponse(response *MockResponse, request *MockRequest) ([]byte, ResponseSource, error) {
	if response.Schema == nil {
		return nil, ResponseSourceSchema, fmt.Errorf("响应结构未定义")
	}
	
	data := rg.faker.GenerateFromSchema(response.Schema)
	
	jsonData, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		return nil, ResponseSourceSchema, fmt.Errorf("JSON 序列化失败: %w", err)
	}
	
	return jsonData, ResponseSourceSchema, nil
}

// generateFileResponse 生成文件响应
func (rg *ResponseGenerator) generateFileResponse(response *MockResponse, request *MockRequest) ([]byte, ResponseSource, error) {
	if response.File == "" {
		return nil, ResponseSourceFile, fmt.Errorf("文件路径为空")
	}
	
	// 读取文件
	data, err := os.ReadFile(response.File)
	if err != nil {
		return nil, ResponseSourceFile, fmt.Errorf("读取文件失败: %w", err)
	}
	
	return data, ResponseSourceFile, nil
}

// generateProxyResponse 生成代理响应
func (rg *ResponseGenerator) generateProxyResponse(response *MockResponse, request *MockRequest) ([]byte, ResponseSource, error) {
	if response.Proxy == nil || response.Proxy.URL == "" {
		return nil, ResponseSourceProxy, fmt.Errorf("代理配置为空")
	}
	
	// 创建 HTTP 客户端
	client := &http.Client{
		Timeout: response.Proxy.Timeout,
	}
	
	// 构建请求
	req, err := http.NewRequest(request.Method, response.Proxy.URL, bytes.NewReader(request.Body))
	if err != nil {
		return nil, ResponseSourceProxy, fmt.Errorf("创建代理请求失败: %w", err)
	}
	
	// 复制请求头
	for key, value := range request.Headers {
		req.Header.Set(key, value)
	}
	
	// 添加代理请求头
	for key, value := range response.Proxy.Headers {
		req.Header.Set(key, value)
	}
	
	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return nil, ResponseSourceProxy, fmt.Errorf("代理请求失败: %w", err)
	}
	defer resp.Body.Close()
	
	// 读取响应
	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, ResponseSourceProxy, fmt.Errorf("读取代理响应失败: %w", err)
	}
	
	return data, ResponseSourceProxy, nil
}

// generateDynamicData 生成动态数据
func (rg *ResponseGenerator) generateDynamicData(dynamic *DynamicResponse, request *MockRequest) (interface{}, error) {
	// 简化的动态数据生成实现
	// 实际项目中可以集成 JavaScript 引擎或其他脚本引擎
	
	if dynamic.Template != "" {
		// 使用模板生成
		tmpl, err := template.New("dynamic").Parse(dynamic.Template)
		if err != nil {
			return nil, fmt.Errorf("动态模板解析失败: %w", err)
		}
		
		data := rg.prepareTemplateData(request)
		
		var buf bytes.Buffer
		if err := tmpl.Execute(&buf, data); err != nil {
			return nil, fmt.Errorf("动态模板执行失败: %w", err)
		}
		
		var result interface{}
		if err := json.Unmarshal(buf.Bytes(), &result); err != nil {
			return buf.String(), nil // 返回字符串
		}
		
		return result, nil
	}
	
	// 默认动态数据
	return map[string]interface{}{
		"dynamic": true,
		"timestamp": time.Now().Unix(),
		"random": rg.faker.GenerateByType("uuid"),
	}, nil
}

// prepareTemplateData 准备模板数据
func (rg *ResponseGenerator) prepareTemplateData(request *MockRequest) map[string]interface{} {
	return map[string]interface{}{
		"request": map[string]interface{}{
			"method":       request.Method,
			"path":         request.Path,
			"headers":      request.Headers,
			"query_params": request.QueryParams,
			"remote_addr":  request.RemoteAddr,
			"user_agent":   request.UserAgent,
			"timestamp":    request.Timestamp,
			"request_id":   request.RequestID,
		},
		"faker": map[string]interface{}{
			"name":      rg.faker.GenerateByType("name"),
			"email":     rg.faker.GenerateByType("email"),
			"phone":     rg.faker.GenerateByType("phone"),
			"company":   rg.faker.GenerateByType("company"),
			"address":   rg.faker.GenerateByType("address"),
			"uuid":      rg.faker.GenerateByType("uuid"),
			"date":      rg.faker.GenerateByType("date"),
			"datetime":  rg.faker.GenerateByType("datetime"),
			"url":       rg.faker.GenerateByType("url"),
			"sentence":  rg.faker.GenerateByType("sentence"),
		},
		"time": map[string]interface{}{
			"now":       time.Now().Format(time.RFC3339),
			"timestamp": time.Now().Unix(),
			"date":      time.Now().Format("2006-01-02"),
			"time":      time.Now().Format("15:04:05"),
		},
	}
}

// calculateDelay 计算延迟时间
func (rg *ResponseGenerator) calculateDelay(delayConfig *DelayConfig) time.Duration {
	switch delayConfig.Type {
	case DelayTypeFixed:
		return time.Duration(delayConfig.Fixed) * time.Millisecond
	case DelayTypeRandom:
		min := delayConfig.Min
		max := delayConfig.Max
		if max <= min {
			return time.Duration(min) * time.Millisecond
		}
		delay := min + rg.faker.random.Intn(max-min)
		return time.Duration(delay) * time.Millisecond
	case DelayTypeNormal:
		// 简化的正态分布实现
		mean := delayConfig.Mean
		stdDev := delayConfig.StdDev
		if stdDev <= 0 {
			return time.Duration(mean) * time.Millisecond
		}
		// 使用 Box-Muller 变换生成正态分布随机数
		delay := mean + stdDev*rg.faker.random.NormFloat64()
		if delay < 0 {
			delay = 0
		}
		return time.Duration(delay) * time.Millisecond
	default:
		return 0
	}
}
