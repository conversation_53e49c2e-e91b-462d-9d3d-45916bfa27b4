package mock

import (
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"time"
)

// DataFaker 数据生成器
type DataFaker struct {
	random *rand.Rand
}

// NewDataFaker 创建数据生成器
func NewDataFaker() *DataFaker {
	return &DataFaker{
		random: rand.New(rand.NewSource(time.Now().UnixNano())),
	}
}

// GenerateFromSchema 根据结构生成数据
func (df *DataFaker) GenerateFromSchema(schema *ResponseSchema) interface{} {
	if schema == nil {
		return nil
	}

	// 如果有示例值，直接返回
	if schema.Example != nil {
		return schema.Example
	}

	// 如果有枚举值，随机选择一个
	if len(schema.Enum) > 0 {
		return schema.Enum[df.random.Intn(len(schema.Enum))]
	}

	// 如果指定了 Faker 类型，使用 Faker 生成
	if schema.Faker != "" {
		return df.GenerateByType(schema.Faker)
	}

	// 根据数据类型生成
	switch schema.Type {
	case "string":
		return df.generateString(schema)
	case "number", "integer":
		return df.generateNumber(schema)
	case "boolean":
		return df.random.Intn(2) == 1
	case "array":
		return df.generateArray(schema)
	case "object":
		return df.generateObject(schema)
	default:
		return nil
	}
}

// generateString 生成字符串
func (df *DataFaker) generateString(schema *ResponseSchema) string {
	// 如果有正则模式，尝试生成匹配的字符串
	if schema.Pattern != "" {
		return df.generateFromPattern(schema.Pattern)
	}

	// 根据格式生成
	switch schema.Format {
	case "email":
		return df.GenerateByType("email").(string)
	case "date":
		return df.GenerateByType("date").(string)
	case "date-time":
		return df.GenerateByType("datetime").(string)
	case "uuid":
		return df.GenerateByType("uuid").(string)
	case "uri":
		return df.GenerateByType("url").(string)
	default:
		// 生成随机字符串
		length := 10
		if schema.Length != nil {
			length = *schema.Length
		}
		return df.generateRandomString(length)
	}
}

// generateNumber 生成数字
func (df *DataFaker) generateNumber(schema *ResponseSchema) interface{} {
	min := 0
	max := 100

	if schema.Min != nil {
		min = *schema.Min
	}
	if schema.Max != nil {
		max = *schema.Max
	}

	if schema.Type == "integer" {
		return min + df.random.Intn(max-min+1)
	}

	// 生成浮点数
	return float64(min) + df.random.Float64()*float64(max-min)
}

// generateArray 生成数组
func (df *DataFaker) generateArray(schema *ResponseSchema) []interface{} {
	length := 3 // 默认长度
	if schema.Length != nil {
		length = *schema.Length
	} else {
		length = 1 + df.random.Intn(5) // 1-5 个元素
	}

	var result []interface{}
	for i := 0; i < length; i++ {
		if schema.Items != nil {
			result = append(result, df.GenerateFromSchema(schema.Items))
		} else {
			result = append(result, df.GenerateByType("string"))
		}
	}

	return result
}

// generateObject 生成对象
func (df *DataFaker) generateObject(schema *ResponseSchema) map[string]interface{} {
	result := make(map[string]interface{})

	for key, propSchema := range schema.Properties {
		result[key] = df.GenerateFromSchema(propSchema)
	}

	return result
}

// GenerateByType 根据类型生成数据
func (df *DataFaker) GenerateByType(fakerType string) interface{} {
	switch fakerType {
	// 基础类型
	case "string":
		return df.generateRandomString(10)
	case "int", "integer":
		return df.random.Intn(1000)
	case "float", "number":
		return df.random.Float64() * 1000
	case "bool", "boolean":
		return df.random.Intn(2) == 1

	// 身份信息
	case "name":
		return df.generateName()
	case "first_name":
		return df.generateFirstName()
	case "last_name":
		return df.generateLastName()
	case "username":
		return df.generateUsername()
	case "email":
		return df.generateEmail()
	case "phone":
		return df.generatePhone()
	case "id_card":
		return df.generateIDCard()

	// 地址信息
	case "address":
		return df.generateAddress()
	case "city":
		return df.generateCity()
	case "country":
		return df.generateCountry()
	case "zipcode":
		return df.generateZipCode()

	// 公司信息
	case "company":
		return df.generateCompany()
	case "job_title":
		return df.generateJobTitle()
	case "department":
		return df.generateDepartment()

	// 网络信息
	case "url":
		return df.generateURL()
	case "domain":
		return df.generateDomain()
	case "ip":
		return df.generateIP()
	case "mac":
		return df.generateMAC()

	// 时间信息
	case "date":
		return df.generateDate()
	case "datetime":
		return df.generateDateTime()
	case "timestamp":
		return df.generateTimestamp()
	case "time":
		return df.generateTime()

	// 标识符
	case "uuid":
		return df.generateUUID()
	case "id":
		return df.generateID()

	// 文本内容
	case "sentence":
		return df.generateSentence()
	case "paragraph":
		return df.generateParagraph()
	case "word":
		return df.generateWord()

	// 数值
	case "price":
		return df.generatePrice()
	case "percentage":
		return df.generatePercentage()

	default:
		return df.generateRandomString(10)
	}
}

// 生成器实现
func (df *DataFaker) generateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	result := make([]byte, length)
	for i := range result {
		result[i] = charset[df.random.Intn(len(charset))]
	}
	return string(result)
}

func (df *DataFaker) generateName() string {
	firstNames := []string{"张三", "李四", "王五", "赵六", "钱七", "孙八", "周九", "吴十"}
	return firstNames[df.random.Intn(len(firstNames))]
}

func (df *DataFaker) generateFirstName() string {
	names := []string{"张", "李", "王", "赵", "钱", "孙", "周", "吴", "郑", "冯"}
	return names[df.random.Intn(len(names))]
}

func (df *DataFaker) generateLastName() string {
	names := []string{"三", "四", "五", "六", "七", "八", "九", "十", "明", "华"}
	return names[df.random.Intn(len(names))]
}

func (df *DataFaker) generateUsername() string {
	return "user" + strconv.Itoa(df.random.Intn(10000))
}

func (df *DataFaker) generateEmail() string {
	domains := []string{"example.com", "test.com", "demo.org", "sample.net"}
	username := df.generateUsername()
	domain := domains[df.random.Intn(len(domains))]
	return username + "@" + domain
}

func (df *DataFaker) generatePhone() string {
	return fmt.Sprintf("138%08d", df.random.Intn(100000000))
}

func (df *DataFaker) generateIDCard() string {
	return fmt.Sprintf("11010119%02d%02d%02d%04d",
		80+df.random.Intn(20), // 年份
		1+df.random.Intn(12),  // 月份
		1+df.random.Intn(28),  // 日期
		df.random.Intn(10000)) // 序号
}

func (df *DataFaker) generateAddress() string {
	addresses := []string{
		"北京市朝阳区建国门外大街1号",
		"上海市浦东新区陆家嘴环路1000号",
		"广州市天河区珠江新城花城大道85号",
		"深圳市南山区科技园南区深南大道9988号",
	}
	return addresses[df.random.Intn(len(addresses))]
}

func (df *DataFaker) generateCity() string {
	cities := []string{"北京", "上海", "广州", "深圳", "杭州", "南京", "武汉", "成都"}
	return cities[df.random.Intn(len(cities))]
}

func (df *DataFaker) generateCountry() string {
	countries := []string{"中国", "美国", "日本", "德国", "英国", "法国", "加拿大", "澳大利亚"}
	return countries[df.random.Intn(len(countries))]
}

func (df *DataFaker) generateZipCode() string {
	return fmt.Sprintf("%06d", df.random.Intn(1000000))
}

func (df *DataFaker) generateCompany() string {
	companies := []string{
		"阿里巴巴集团", "腾讯科技", "百度公司", "京东集团",
		"华为技术", "小米科技", "字节跳动", "美团点评",
	}
	return companies[df.random.Intn(len(companies))]
}

func (df *DataFaker) generateJobTitle() string {
	titles := []string{
		"软件工程师", "产品经理", "UI设计师", "数据分析师",
		"项目经理", "运维工程师", "测试工程师", "架构师",
	}
	return titles[df.random.Intn(len(titles))]
}

func (df *DataFaker) generateDepartment() string {
	departments := []string{"技术部", "产品部", "设计部", "运营部", "市场部", "人事部", "财务部", "法务部"}
	return departments[df.random.Intn(len(departments))]
}

func (df *DataFaker) generateURL() string {
	domains := []string{"example.com", "test.org", "demo.net", "sample.io"}
	paths := []string{"api/v1/users", "dashboard", "products", "about"}
	return fmt.Sprintf("https://%s/%s", 
		domains[df.random.Intn(len(domains))],
		paths[df.random.Intn(len(paths))])
}

func (df *DataFaker) generateDomain() string {
	domains := []string{"example.com", "test.org", "demo.net", "sample.io", "mock.dev"}
	return domains[df.random.Intn(len(domains))]
}

func (df *DataFaker) generateIP() string {
	return fmt.Sprintf("%d.%d.%d.%d",
		df.random.Intn(256),
		df.random.Intn(256),
		df.random.Intn(256),
		df.random.Intn(256))
}

func (df *DataFaker) generateMAC() string {
	return fmt.Sprintf("%02x:%02x:%02x:%02x:%02x:%02x",
		df.random.Intn(256), df.random.Intn(256), df.random.Intn(256),
		df.random.Intn(256), df.random.Intn(256), df.random.Intn(256))
}

func (df *DataFaker) generateDate() string {
	start := time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)
	end := time.Now()
	delta := end.Sub(start)
	randomDuration := time.Duration(df.random.Int63n(int64(delta)))
	randomTime := start.Add(randomDuration)
	return randomTime.Format("2006-01-02")
}

func (df *DataFaker) generateDateTime() string {
	start := time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)
	end := time.Now()
	delta := end.Sub(start)
	randomDuration := time.Duration(df.random.Int63n(int64(delta)))
	randomTime := start.Add(randomDuration)
	return randomTime.Format("2006-01-02T15:04:05Z")
}

func (df *DataFaker) generateTimestamp() int64 {
	start := time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)
	end := time.Now()
	delta := end.Sub(start)
	randomDuration := time.Duration(df.random.Int63n(int64(delta)))
	randomTime := start.Add(randomDuration)
	return randomTime.Unix()
}

func (df *DataFaker) generateTime() string {
	hour := df.random.Intn(24)
	minute := df.random.Intn(60)
	second := df.random.Intn(60)
	return fmt.Sprintf("%02d:%02d:%02d", hour, minute, second)
}

func (df *DataFaker) generateUUID() string {
	return fmt.Sprintf("%08x-%04x-%04x-%04x-%012x",
		df.random.Uint32(),
		df.random.Uint32()&0xffff,
		df.random.Uint32()&0xffff,
		df.random.Uint32()&0xffff,
		df.random.Uint64()&0xffffffffffff)
}

func (df *DataFaker) generateID() string {
	return strconv.Itoa(df.random.Intn(1000000))
}

func (df *DataFaker) generateSentence() string {
	sentences := []string{
		"这是一个测试句子。",
		"Mock 数据生成器工作正常。",
		"API 接口返回模拟数据。",
		"系统运行状态良好。",
		"数据处理完成。",
	}
	return sentences[df.random.Intn(len(sentences))]
}

func (df *DataFaker) generateParagraph() string {
	sentences := []string{
		df.generateSentence(),
		df.generateSentence(),
		df.generateSentence(),
	}
	return strings.Join(sentences, " ")
}

func (df *DataFaker) generateWord() string {
	words := []string{"测试", "数据", "接口", "系统", "服务", "应用", "平台", "工具"}
	return words[df.random.Intn(len(words))]
}

func (df *DataFaker) generatePrice() float64 {
	return float64(df.random.Intn(100000)) / 100.0 // 0.00 - 999.99
}

func (df *DataFaker) generatePercentage() float64 {
	return float64(df.random.Intn(10000)) / 100.0 // 0.00 - 99.99
}

func (df *DataFaker) generateFromPattern(pattern string) string {
	// 简化的正则模式生成，实际项目中可以使用更复杂的实现
	// 这里只处理一些基本模式
	switch pattern {
	case "\\d{11}":
		return fmt.Sprintf("%011d", df.random.Int63n(100000000000))
	case "\\d{4}-\\d{2}-\\d{2}":
		return df.generateDate()
	case "[a-zA-Z0-9]{8}":
		return df.generateRandomString(8)
	default:
		return df.generateRandomString(10)
	}
}
