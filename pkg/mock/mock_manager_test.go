package mock

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"paas-platform/pkg/logger"
)

func TestMockManager_RegisterMock(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultMockConfig()
	manager := NewMockManager(config, logger)

	// 测试注册有效 Mock 规则
	rule := &MockRule{
		ID:          "test-mock-1",
		Name:        "测试 Mock 规则",
		Description: "用于测试的 Mock 规则",
		Path:        "/api/v1/test",
		Method:      "GET",
		Enabled:     true,
		Priority:    100,
		Response: MockResponse{
			StatusCode: 200,
			Headers:    map[string]string{"Content-Type": "application/json"},
			Body:       map[string]interface{}{"message": "test"},
			BodyType:   ResponseBodyTypeJSON,
		},
		Tags:     []string{"test"},
		Metadata: map[string]string{"category": "test"},
	}

	err := manager.RegisterMock(rule)
	require.NoError(t, err)

	// 验证规则已注册
	registeredRule, exists := manager.GetMock("test-mock-1")
	assert.True(t, exists)
	assert.Equal(t, "test-mock-1", registeredRule.ID)
	assert.Equal(t, "测试 Mock 规则", registeredRule.Name)
	assert.True(t, registeredRule.Enabled)

	// 测试重复注册（相同ID的规则）
	duplicateRule := &MockRule{
		ID:          "test-mock-1", // 相同的ID
		Name:        "重复的测试规则",
		Path:        "/api/v1/duplicate",
		Method:      "POST",
		Enabled:     true,
		Response: MockResponse{
			StatusCode: 201,
			BodyType:   ResponseBodyTypeJSON,
		},
	}

	// 由于我们的实现允许覆盖，这里应该成功
	err = manager.RegisterMock(duplicateRule)
	assert.NoError(t, err)

	// 验证规则已被更新
	updatedRule, exists := manager.GetMock("test-mock-1")
	assert.True(t, exists)
	assert.Equal(t, "重复的测试规则", updatedRule.Name)
	assert.Equal(t, "POST", updatedRule.Method)
}

func TestMockManager_FindMatchingMock(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultMockConfig()
	manager := NewMockManager(config, logger)

	// 注册测试规则
	rule := &MockRule{
		ID:       "test-mock-1",
		Name:     "测试 Mock 规则",
		Path:     "/api/v1/users",
		Method:   "GET",
		Enabled:  true,
		Priority: 100,
		Response: MockResponse{
			StatusCode: 200,
			Body:       map[string]interface{}{"message": "success"},
			BodyType:   ResponseBodyTypeJSON,
		},
	}

	err := manager.RegisterMock(rule)
	require.NoError(t, err)

	// 测试匹配请求
	request := &MockRequest{
		Method:      "GET",
		Path:        "/api/v1/users",
		Headers:     map[string]string{},
		QueryParams: map[string]string{},
		Timestamp:   time.Now(),
	}

	matchedRule, scenario, err := manager.FindMatchingMock(request)
	require.NoError(t, err)
	assert.NotNil(t, matchedRule)
	assert.Nil(t, scenario)
	assert.Equal(t, "test-mock-1", matchedRule.ID)

	// 测试不匹配的请求
	request.Path = "/api/v1/apps"
	matchedRule, scenario, err = manager.FindMatchingMock(request)
	assert.Error(t, err)
	assert.Nil(t, matchedRule)
	assert.Nil(t, scenario)
}

func TestMockManager_ProcessMockRequest(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultMockConfig()
	manager := NewMockManager(config, logger)

	// 注册测试规则
	rule := &MockRule{
		ID:       "test-mock-1",
		Name:     "测试 Mock 规则",
		Path:     "/api/v1/test",
		Method:   "GET",
		Enabled:  true,
		Priority: 100,
		Response: MockResponse{
			StatusCode: 200,
			Headers:    map[string]string{"Content-Type": "application/json"},
			Body:       map[string]interface{}{"message": "test response"},
			BodyType:   ResponseBodyTypeJSON,
		},
	}

	err := manager.RegisterMock(rule)
	require.NoError(t, err)

	// 处理匹配的请求
	request := &MockRequest{
		Method:      "GET",
		Path:        "/api/v1/test",
		Headers:     map[string]string{},
		QueryParams: map[string]string{},
		Timestamp:   time.Now(),
		RequestID:   "test-request-1",
	}

	result, err := manager.ProcessMockRequest(request)
	require.NoError(t, err)
	assert.True(t, result.Matched)
	assert.NotNil(t, result.Rule)
	assert.NotNil(t, result.Response)
	assert.Equal(t, 200, result.Response.StatusCode)
	assert.Greater(t, result.ProcessTime, time.Duration(0))

	// 处理不匹配的请求
	request.Path = "/api/v1/notfound"
	result, err = manager.ProcessMockRequest(request)
	require.NoError(t, err)
	assert.False(t, result.Matched)
	assert.Nil(t, result.Rule)
	assert.Nil(t, result.Response)
}

func TestMockManager_MatchPath(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultMockConfig()
	manager := NewMockManager(config, logger)

	tests := []struct {
		name        string
		pattern     string
		path        string
		shouldMatch bool
	}{
		{
			name:        "精确匹配",
			pattern:     "/api/v1/users",
			path:        "/api/v1/users",
			shouldMatch: true,
		},
		{
			name:        "通配符匹配",
			pattern:     "*",
			path:        "/any/path",
			shouldMatch: true,
		},
		{
			name:        "路径参数匹配",
			pattern:     "/api/v1/users/{id}",
			path:        "/api/v1/users/123",
			shouldMatch: true,
		},
		{
			name:        "不匹配",
			pattern:     "/api/v1/users",
			path:        "/api/v1/apps",
			shouldMatch: false,
		},
		{
			name:        "多个路径参数",
			pattern:     "/api/v1/users/{id}/posts/{postId}",
			path:        "/api/v1/users/123/posts/456",
			shouldMatch: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := manager.matchPath(tt.pattern, tt.path)
			assert.Equal(t, tt.shouldMatch, result)
		})
	}
}

func TestMockManager_EvaluateCondition(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultMockConfig()
	manager := NewMockManager(config, logger)

	request := &MockRequest{
		Method: "GET",
		Path:   "/api/v1/users",
		Headers: map[string]string{
			"Authorization": "Bearer token123",
			"User-Agent":    "TestClient/1.0",
		},
		QueryParams: map[string]string{
			"page": "1",
			"size": "10",
		},
		Body:      []byte(`{"name": "test"}`),
		UserAgent: "TestClient/1.0", // 设置 UserAgent 字段
		Timestamp: time.Now(),
	}

	tests := []struct {
		name      string
		condition MockCondition
		expected  bool
	}{
		{
			name: "请求头等于条件",
			condition: MockCondition{
				Type:     ConditionTypeHeader,
				Field:    "Authorization",
				Operator: OperatorEquals,
				Value:    "Bearer token123",
			},
			expected: true,
		},
		{
			name: "查询参数存在条件",
			condition: MockCondition{
				Type:     ConditionTypeQuery,
				Field:    "page",
				Operator: OperatorExists,
			},
			expected: true,
		},
		{
			name: "方法匹配条件",
			condition: MockCondition{
				Type:     ConditionTypeMethod,
				Operator: OperatorEquals,
				Value:    "GET",
			},
			expected: true,
		},
		{
			name: "路径包含条件",
			condition: MockCondition{
				Type:     ConditionTypePath,
				Operator: OperatorContains,
				Value:    "users",
			},
			expected: true,
		},
		{
			name: "User-Agent 开始于条件",
			condition: MockCondition{
				Type:     ConditionTypeUserAgent,
				Field:    "", // User-Agent 条件不需要 Field
				Operator: OperatorStartsWith,
				Value:    "TestClient",
			},
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := manager.evaluateCondition(&tt.condition, request)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestMockManager_CompareValues(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultMockConfig()
	manager := NewMockManager(config, logger)

	tests := []struct {
		name         string
		actual       interface{}
		operator     Operator
		expected     interface{}
		expectedList []interface{}
		pattern      string
		caseSensitive bool
		result       bool
	}{
		{
			name:     "字符串相等",
			actual:   "test",
			operator: OperatorEquals,
			expected: "test",
			result:   true,
		},
		{
			name:     "字符串不相等",
			actual:   "test",
			operator: OperatorNotEquals,
			expected: "other",
			result:   true,
		},
		{
			name:     "字符串包含",
			actual:   "hello world",
			operator: OperatorContains,
			expected: "world",
			result:   true,
		},
		{
			name:     "字符串开始于",
			actual:   "hello world",
			operator: OperatorStartsWith,
			expected: "hello",
			result:   true,
		},
		{
			name:     "字符串结束于",
			actual:   "hello world",
			operator: OperatorEndsWith,
			expected: "world",
			result:   true,
		},
		{
			name:         "在列表中",
			actual:       "apple",
			operator:     OperatorIn,
			expectedList: []interface{}{"apple", "banana", "orange"},
			result:       true,
		},
		{
			name:         "不在列表中",
			actual:       "grape",
			operator:     OperatorNotIn,
			expectedList: []interface{}{"apple", "banana", "orange"},
			result:       true,
		},
		{
			name:     "数字大于",
			actual:   "10",
			operator: OperatorGreater,
			expected: "5",
			result:   true,
		},
		{
			name:     "数字小于",
			actual:   "3",
			operator: OperatorLess,
			expected: "5",
			result:   true,
		},
		{
			name:     "值存在",
			actual:   "test",
			operator: OperatorExists,
			result:   true,
		},
		{
			name:     "值不存在",
			actual:   "",
			operator: OperatorNotExists,
			result:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := manager.compareValues(
				tt.actual,
				tt.operator,
				tt.expected,
				tt.expectedList,
				tt.pattern,
				tt.caseSensitive,
			)
			assert.Equal(t, tt.result, result)
		})
	}
}

func TestMockManager_GetBodyField(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultMockConfig()
	manager := NewMockManager(config, logger)

	// 测试 JSON 数据
	jsonData := map[string]interface{}{
		"user": map[string]interface{}{
			"name":  "John Doe",
			"email": "<EMAIL>",
			"profile": map[string]interface{}{
				"age": 30,
			},
		},
		"status": "active",
	}

	jsonBytes, _ := json.Marshal(jsonData)

	tests := []struct {
		name     string
		field    string
		expected interface{}
	}{
		{
			name:     "顶级字段",
			field:    "status",
			expected: "active",
		},
		{
			name:     "嵌套字段",
			field:    "user.name",
			expected: "John Doe",
		},
		{
			name:     "深层嵌套字段",
			field:    "user.profile.age",
			expected: float64(30), // JSON 数字解析为 float64
		},
		{
			name:     "不存在的字段",
			field:    "nonexistent",
			expected: nil,
		},
		{
			name:     "不存在的嵌套字段",
			field:    "user.nonexistent",
			expected: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := manager.getBodyField(jsonBytes, tt.field)
			assert.Equal(t, tt.expected, result)
		})
	}

	// 测试无效 JSON
	invalidJSON := []byte(`{"invalid": json}`)
	result := manager.getBodyField(invalidJSON, "any")
	assert.Nil(t, result)

	// 测试空数据
	result = manager.getBodyField([]byte{}, "any")
	assert.Nil(t, result)
}

func TestMockManager_CRUD_Operations(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultMockConfig()
	manager := NewMockManager(config, logger)

	// 创建测试规则
	rule := &MockRule{
		ID:       "crud-test",
		Name:     "CRUD 测试规则",
		Path:     "/api/v1/crud",
		Method:   "GET",
		Enabled:  true,
		Priority: 100,
		Response: MockResponse{
			StatusCode: 200,
			Body:       map[string]interface{}{"message": "crud test"},
			BodyType:   ResponseBodyTypeJSON,
		},
		Tags: []string{"crud", "test"},
	}

	// 测试创建
	err := manager.RegisterMock(rule)
	require.NoError(t, err)

	// 测试读取
	retrievedRule, exists := manager.GetMock("crud-test")
	assert.True(t, exists)
	assert.Equal(t, rule.ID, retrievedRule.ID)

	// 测试更新
	rule.Name = "更新后的 CRUD 测试规则"
	rule.Priority = 200
	err = manager.UpdateMock(rule)
	require.NoError(t, err)

	updatedRule, _ := manager.GetMock("crud-test")
	assert.Equal(t, "更新后的 CRUD 测试规则", updatedRule.Name)
	assert.Equal(t, 200, updatedRule.Priority)

	// 测试启用/禁用
	err = manager.DisableMock("crud-test")
	require.NoError(t, err)

	disabledRule, _ := manager.GetMock("crud-test")
	assert.False(t, disabledRule.Enabled)

	err = manager.EnableMock("crud-test")
	require.NoError(t, err)

	enabledRule, _ := manager.GetMock("crud-test")
	assert.True(t, enabledRule.Enabled)

	// 测试删除
	err = manager.DeleteMock("crud-test")
	require.NoError(t, err)

	_, exists = manager.GetMock("crud-test")
	assert.False(t, exists)
}

func TestMockManager_GetMocksByPath(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultMockConfig()
	manager := NewMockManager(config, logger)

	// 注册多个规则
	rules := []*MockRule{
		{
			ID:     "rule-1",
			Path:   "/api/v1/users",
			Method: "GET",
			Response: MockResponse{StatusCode: 200, BodyType: ResponseBodyTypeJSON},
		},
		{
			ID:     "rule-2",
			Path:   "/api/v1/users/{id}",
			Method: "GET",
			Response: MockResponse{StatusCode: 200, BodyType: ResponseBodyTypeJSON},
		},
		{
			ID:     "rule-3",
			Path:   "/api/v1/apps",
			Method: "GET",
			Response: MockResponse{StatusCode: 200, BodyType: ResponseBodyTypeJSON},
		},
	}

	for _, rule := range rules {
		err := manager.RegisterMock(rule)
		require.NoError(t, err)
	}

	// 测试路径匹配
	userRules := manager.GetMocksByPath("/api/v1/users")
	assert.Len(t, userRules, 1)
	assert.Equal(t, "rule-1", userRules[0].ID)

	userDetailRules := manager.GetMocksByPath("/api/v1/users/123")
	assert.Len(t, userDetailRules, 1)
	assert.Equal(t, "rule-2", userDetailRules[0].ID)

	appRules := manager.GetMocksByPath("/api/v1/apps")
	assert.Len(t, appRules, 1)
	assert.Equal(t, "rule-3", appRules[0].ID)

	// 测试不匹配的路径
	noMatchRules := manager.GetMocksByPath("/api/v1/nonexistent")
	assert.Len(t, noMatchRules, 0)
}

func TestMockManager_GetMocksByTag(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultMockConfig()
	manager := NewMockManager(config, logger)

	// 注册带标签的规则
	rules := []*MockRule{
		{
			ID:     "rule-1",
			Path:   "/api/v1/users",
			Method: "GET",
			Tags:   []string{"user", "list"},
			Response: MockResponse{StatusCode: 200, BodyType: ResponseBodyTypeJSON},
		},
		{
			ID:     "rule-2",
			Path:   "/api/v1/users/{id}",
			Method: "GET",
			Tags:   []string{"user", "detail"},
			Response: MockResponse{StatusCode: 200, BodyType: ResponseBodyTypeJSON},
		},
		{
			ID:     "rule-3",
			Path:   "/api/v1/apps",
			Method: "GET",
			Tags:   []string{"app", "list"},
			Response: MockResponse{StatusCode: 200, BodyType: ResponseBodyTypeJSON},
		},
	}

	for _, rule := range rules {
		err := manager.RegisterMock(rule)
		require.NoError(t, err)
	}

	// 测试标签匹配
	userRules := manager.GetMocksByTag("user")
	assert.Len(t, userRules, 2)

	listRules := manager.GetMocksByTag("list")
	assert.Len(t, listRules, 2)

	detailRules := manager.GetMocksByTag("detail")
	assert.Len(t, detailRules, 1)
	assert.Equal(t, "rule-2", detailRules[0].ID)

	appRules := manager.GetMocksByTag("app")
	assert.Len(t, appRules, 1)
	assert.Equal(t, "rule-3", appRules[0].ID)

	// 测试不存在的标签
	noMatchRules := manager.GetMocksByTag("nonexistent")
	assert.Len(t, noMatchRules, 0)
}
