package version

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"paas-platform/pkg/logger"
)

// ConfigLoader 版本配置加载器
type ConfigLoader struct {
	logger    logger.Logger
	configDir string
}

// VersionConfigFile 版本配置文件结构
type VersionConfigFile struct {
	Versions []*APIVersion     `json:"versions"`
	Routes   []*VersionRoute   `json:"routes"`
	Policies []*VersionPolicy  `json:"policies"`
	Config   VersionConfig     `json:"config"`
}

// NewConfigLoader 创建配置加载器
func NewConfigLoader(configDir string, logger logger.Logger) *ConfigLoader {
	return &ConfigLoader{
		logger:    logger,
		configDir: configDir,
	}
}

// LoadVersionConfig 加载版本配置
func (cl *ConfigLoader) LoadVersionConfig(filename string) (*VersionConfigFile, error) {
	configPath := filepath.Join(cl.configDir, filename)
	
	cl.logger.Info("加载版本配置", "file", configPath)
	
	// 检查文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("配置文件不存在: %s", configPath)
	}
	
	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}
	
	// 解析配置
	var config VersionConfigFile
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %w", err)
	}
	
	// 验证配置
	if err := cl.validateConfig(&config); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}
	
	// 处理配置
	cl.processConfig(&config)
	
	cl.logger.Info("版本配置加载完成",
		"versions", len(config.Versions),
		"routes", len(config.Routes),
		"policies", len(config.Policies))
	
	return &config, nil
}

// validateConfig 验证配置
func (cl *ConfigLoader) validateConfig(config *VersionConfigFile) error {
	// 验证版本配置
	if len(config.Versions) == 0 {
		return fmt.Errorf("至少需要定义一个版本")
	}
	
	versionMap := make(map[string]bool)
	defaultVersionCount := 0
	
	for i, version := range config.Versions {
		// 检查版本号是否为空
		if version.Version == "" {
			return fmt.Errorf("版本 %d 的版本号不能为空", i)
		}
		
		// 检查版本号是否重复
		if versionMap[version.Version] {
			return fmt.Errorf("版本号重复: %s", version.Version)
		}
		versionMap[version.Version] = true
		
		// 检查默认版本数量
		if version.IsDefault {
			defaultVersionCount++
		}
		
		// 验证版本状态
		if !cl.isValidVersionStatus(version.Status) {
			return fmt.Errorf("无效的版本状态: %s", version.Status)
		}
		
		// 验证日期
		if err := cl.validateVersionDates(version); err != nil {
			return fmt.Errorf("版本 %s 日期验证失败: %w", version.Version, err)
		}
	}
	
	// 检查默认版本
	if defaultVersionCount == 0 {
		return fmt.Errorf("必须指定一个默认版本")
	}
	if defaultVersionCount > 1 {
		return fmt.Errorf("只能有一个默认版本")
	}
	
	// 验证路由配置
	for i, route := range config.Routes {
		if err := cl.validateRoute(route, versionMap); err != nil {
			return fmt.Errorf("路由 %d 验证失败: %w", i, err)
		}
	}
	
	// 验证策略配置
	for i, policy := range config.Policies {
		if err := cl.validatePolicy(policy); err != nil {
			return fmt.Errorf("策略 %d 验证失败: %w", i, err)
		}
	}
	
	return nil
}

// isValidVersionStatus 检查版本状态是否有效
func (cl *ConfigLoader) isValidVersionStatus(status VersionStatus) bool {
	validStatuses := []VersionStatus{
		VersionStatusDraft,
		VersionStatusBeta,
		VersionStatusStable,
		VersionStatusDeprecated,
		VersionStatusSunset,
	}
	
	for _, validStatus := range validStatuses {
		if status == validStatus {
			return true
		}
	}
	
	return false
}

// validateVersionDates 验证版本日期
func (cl *ConfigLoader) validateVersionDates(version *APIVersion) error {
	now := time.Now()
	
	// 发布日期不能在未来
	if version.ReleaseDate != nil && version.ReleaseDate.After(now) {
		return fmt.Errorf("发布日期不能在未来")
	}
	
	// 废弃日期必须在发布日期之后
	if version.DeprecatedDate != nil && version.ReleaseDate != nil {
		if version.DeprecatedDate.Before(*version.ReleaseDate) {
			return fmt.Errorf("废弃日期不能早于发布日期")
		}
	}
	
	// 下线日期必须在废弃日期之后
	if version.SunsetDate != nil && version.DeprecatedDate != nil {
		if version.SunsetDate.Before(*version.DeprecatedDate) {
			return fmt.Errorf("下线日期不能早于废弃日期")
		}
	}
	
	return nil
}

// validateRoute 验证路由配置
func (cl *ConfigLoader) validateRoute(route *VersionRoute, versionMap map[string]bool) error {
	// 检查必需字段
	if route.Path == "" {
		return fmt.Errorf("路由路径不能为空")
	}
	
	if route.Method == "" {
		return fmt.Errorf("路由方法不能为空")
	}
	
	// 验证支持的版本
	for _, version := range route.Versions {
		if !versionMap[version] {
			return fmt.Errorf("路由引用了不存在的版本: %s", version)
		}
	}
	
	// 验证默认版本
	if route.DefaultVersion != "" && !versionMap[route.DefaultVersion] {
		return fmt.Errorf("路由的默认版本不存在: %s", route.DefaultVersion)
	}
	
	// 验证版本映射
	for fromVersion, toVersion := range route.VersionMap {
		if !versionMap[toVersion] {
			return fmt.Errorf("版本映射的目标版本不存在: %s -> %s", fromVersion, toVersion)
		}
	}
	
	// 验证兼容性规则
	for _, rule := range route.CompatibilityRules {
		if err := cl.validateCompatibilityRule(&rule, versionMap); err != nil {
			return fmt.Errorf("兼容性规则验证失败: %w", err)
		}
	}
	
	return nil
}

// validateCompatibilityRule 验证兼容性规则
func (cl *ConfigLoader) validateCompatibilityRule(rule *CompatibilityRule, versionMap map[string]bool) error {
	// 检查版本是否存在
	if !versionMap[rule.FromVersion] {
		return fmt.Errorf("兼容性规则的源版本不存在: %s", rule.FromVersion)
	}
	
	if !versionMap[rule.ToVersion] {
		return fmt.Errorf("兼容性规则的目标版本不存在: %s", rule.ToVersion)
	}
	
	// 验证兼容性类型
	validTypes := []CompatibilityType{
		CompatibilityTypeBackward,
		CompatibilityTypeForward,
		CompatibilityTypeBridge,
	}
	
	validType := false
	for _, validT := range validTypes {
		if rule.Type == validT {
			validType = true
			break
		}
	}
	
	if !validType {
		return fmt.Errorf("无效的兼容性类型: %s", rule.Type)
	}
	
	// 验证转换规则
	for _, transform := range rule.Transforms {
		if err := cl.validateTransform(&transform); err != nil {
			return fmt.Errorf("转换规则验证失败: %w", err)
		}
	}
	
	return nil
}

// validateTransform 验证转换规则
func (cl *ConfigLoader) validateTransform(transform *CompatibilityTransform) error {
	// 验证转换类型
	validTypes := []TransformType{
		TransformTypeRename,
		TransformTypeDefault,
		TransformTypeMapping,
		TransformTypeRemove,
		TransformTypeAdd,
	}
	
	validType := false
	for _, validT := range validTypes {
		if transform.Type == validT {
			validType = true
			break
		}
	}
	
	if !validType {
		return fmt.Errorf("无效的转换类型: %s", transform.Type)
	}
	
	// 根据转换类型验证必需字段
	switch transform.Type {
	case TransformTypeRename:
		if transform.Source == "" || transform.Target == "" {
			return fmt.Errorf("重命名转换需要指定源字段和目标字段")
		}
	case TransformTypeDefault:
		if transform.Target == "" || transform.DefaultValue == nil {
			return fmt.Errorf("默认值转换需要指定目标字段和默认值")
		}
	case TransformTypeMapping:
		if transform.Source == "" || transform.Target == "" || len(transform.Mapping) == 0 {
			return fmt.Errorf("映射转换需要指定源字段、目标字段和映射关系")
		}
	case TransformTypeRemove:
		if transform.Source == "" {
			return fmt.Errorf("移除转换需要指定源字段")
		}
	case TransformTypeAdd:
		if transform.Target == "" || transform.DefaultValue == nil {
			return fmt.Errorf("添加转换需要指定目标字段和默认值")
		}
	}
	
	return nil
}

// validatePolicy 验证策略配置
func (cl *ConfigLoader) validatePolicy(policy *VersionPolicy) error {
	// 检查必需字段
	if policy.Name == "" {
		return fmt.Errorf("策略名称不能为空")
	}
	
	if len(policy.Rules) == 0 {
		return fmt.Errorf("策略必须包含至少一个规则")
	}
	
	// 验证策略规则
	for i, rule := range policy.Rules {
		if err := cl.validatePolicyRule(&rule); err != nil {
			return fmt.Errorf("策略规则 %d 验证失败: %w", i, err)
		}
	}
	
	return nil
}

// validatePolicyRule 验证策略规则
func (cl *ConfigLoader) validatePolicyRule(rule *PolicyRule) error {
	// 验证动作类型
	validActions := []PolicyAction{
		PolicyActionRoute,
		PolicyActionReject,
		PolicyActionTransform,
		PolicyActionLog,
	}
	
	validAction := false
	for _, validA := range validActions {
		if rule.Action == validA {
			validAction = true
			break
		}
	}
	
	if !validAction {
		return fmt.Errorf("无效的策略动作: %s", rule.Action)
	}
	
	// 检查条件表达式
	if rule.Condition == "" {
		return fmt.Errorf("策略规则必须指定条件表达式")
	}
	
	return nil
}

// processConfig 处理配置
func (cl *ConfigLoader) processConfig(config *VersionConfigFile) {
	// 设置默认值
	for _, version := range config.Versions {
		if version.CreatedAt.IsZero() {
			version.CreatedAt = time.Now()
		}
		if version.UpdatedAt.IsZero() {
			version.UpdatedAt = time.Now()
		}
		if version.Metadata == nil {
			version.Metadata = make(map[string]string)
		}
		if version.ServiceMap == nil {
			version.ServiceMap = make(map[string]string)
		}
	}
	
	for _, route := range config.Routes {
		if route.VersionMap == nil {
			route.VersionMap = make(map[string]string)
		}
		if route.Metadata == nil {
			route.Metadata = make(map[string]string)
		}
	}
	
	for _, policy := range config.Policies {
		if policy.Metadata == nil {
			policy.Metadata = make(map[string]string)
		}
	}
}

// LoadAndApplyConfig 加载并应用配置到版本管理器
func (cl *ConfigLoader) LoadAndApplyConfig(manager *VersionManager, filename string) error {
	config, err := cl.LoadVersionConfig(filename)
	if err != nil {
		return err
	}
	
	// 应用版本配置
	manager.config = config.Config
	
	// 注册版本
	for _, version := range config.Versions {
		if err := manager.RegisterVersion(version); err != nil {
			cl.logger.Error("注册版本失败", "error", err, "version", version.Version)
			return err
		}
	}
	
	// 注册路由
	for _, route := range config.Routes {
		if err := manager.RegisterRoute(route); err != nil {
			cl.logger.Error("注册路由失败", "error", err, "path", route.Path, "method", route.Method)
			return err
		}
	}
	
	// 注册策略
	for _, policy := range config.Policies {
		if err := manager.RegisterPolicy(policy); err != nil {
			cl.logger.Error("注册策略失败", "error", err, "policy", policy.Name)
			return err
		}
	}
	
	cl.logger.Info("版本配置应用完成")
	return nil
}
