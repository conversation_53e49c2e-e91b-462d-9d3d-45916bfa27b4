package version

import (
	"fmt"
	"regexp"
	"strings"
	"sync"
	"time"

	"paas-platform/pkg/logger"
)

// VersionManager API 版本管理器
type VersionManager struct {
	versions    map[string]*APIVersion
	routes      map[string]*VersionRoute
	policies    map[string]*VersionPolicy
	mutex       sync.RWMutex
	logger      logger.Logger
	config      VersionConfig
}

// APIVersion API 版本信息
type APIVersion struct {
	Version     string            `json:"version"`      // 版本号 (如: v1, v2, v1.2.3)
	Name        string            `json:"name"`         // 版本名称
	Description string            `json:"description"`  // 版本描述
	Status      VersionStatus     `json:"status"`       // 版本状态
	CreatedAt   time.Time         `json:"created_at"`   // 创建时间
	UpdatedAt   time.Time         `json:"updated_at"`   // 更新时间
	
	// 版本生命周期
	ReleaseDate    *time.Time `json:"release_date"`     // 发布日期
	DeprecatedDate *time.Time `json:"deprecated_date"`  // 废弃日期
	SunsetDate     *time.Time `json:"sunset_date"`      // 下线日期
	
	// 版本配置
	IsDefault      bool              `json:"is_default"`       // 是否为默认版本
	IsStable       bool              `json:"is_stable"`        // 是否为稳定版本
	Metadata       map[string]string `json:"metadata"`         // 版本元数据
	
	// 兼容性配置
	BackwardCompatible []string `json:"backward_compatible"` // 向后兼容的版本
	ForwardCompatible  []string `json:"forward_compatible"`  // 向前兼容的版本
	
	// 路由配置
	BaseURL    string            `json:"base_url"`     // 基础URL
	ServiceMap map[string]string `json:"service_map"`  // 服务映射
}

// VersionRoute 版本路由配置
type VersionRoute struct {
	Path           string            `json:"path"`            // 路由路径
	Method         string            `json:"method"`          // HTTP 方法
	Versions       []string          `json:"versions"`        // 支持的版本
	DefaultVersion string            `json:"default_version"` // 默认版本
	VersionMap     map[string]string `json:"version_map"`     // 版本映射
	
	// 路由策略
	Strategy       RoutingStrategy   `json:"strategy"`        // 路由策略
	FallbackPolicy FallbackPolicy    `json:"fallback_policy"` // 降级策略
	
	// 兼容性处理
	CompatibilityRules []CompatibilityRule `json:"compatibility_rules"` // 兼容性规则
	
	// 元数据
	Description string            `json:"description"` // 路由描述
	Tags        []string          `json:"tags"`        // 路由标签
	Metadata    map[string]string `json:"metadata"`    // 路由元数据
}

// VersionPolicy 版本策略
type VersionPolicy struct {
	Name        string            `json:"name"`         // 策略名称
	Description string            `json:"description"`  // 策略描述
	Rules       []PolicyRule      `json:"rules"`        // 策略规则
	Priority    int               `json:"priority"`     // 策略优先级
	Enabled     bool              `json:"enabled"`      // 是否启用
	Metadata    map[string]string `json:"metadata"`     // 策略元数据
}

// PolicyRule 策略规则
type PolicyRule struct {
	Condition string      `json:"condition"` // 条件表达式
	Action    PolicyAction `json:"action"`   // 执行动作
	Target    string      `json:"target"`    // 目标版本
	Priority  int         `json:"priority"`  // 规则优先级
}

// CompatibilityRule 兼容性规则
type CompatibilityRule struct {
	FromVersion string                 `json:"from_version"` // 源版本
	ToVersion   string                 `json:"to_version"`   // 目标版本
	Type        CompatibilityType      `json:"type"`         // 兼容性类型
	Transforms  []CompatibilityTransform `json:"transforms"` // 转换规则
}

// CompatibilityTransform 兼容性转换
type CompatibilityTransform struct {
	Type        TransformType     `json:"type"`         // 转换类型
	Source      string            `json:"source"`       // 源字段/参数
	Target      string            `json:"target"`       // 目标字段/参数
	DefaultValue interface{}      `json:"default_value"` // 默认值
	Mapping     map[string]string `json:"mapping"`      // 值映射
}

// VersionConfig 版本控制配置
type VersionConfig struct {
	// 版本检测配置
	DetectionMethods []DetectionMethod `json:"detection_methods"` // 版本检测方法
	DefaultVersion   string            `json:"default_version"`   // 默认版本
	
	// 版本格式配置
	VersionFormat    VersionFormat `json:"version_format"`     // 版本格式
	StrictValidation bool          `json:"strict_validation"`  // 严格验证
	
	// 兼容性配置
	EnableCompatibility bool `json:"enable_compatibility"` // 启用兼容性处理
	MaxCompatibleVersions int `json:"max_compatible_versions"` // 最大兼容版本数
	
	// 缓存配置
	EnableCache   bool          `json:"enable_cache"`    // 启用缓存
	CacheTTL      time.Duration `json:"cache_ttl"`       // 缓存TTL
	
	// 监控配置
	EnableMetrics bool `json:"enable_metrics"` // 启用指标收集
	EnableTracing bool `json:"enable_tracing"` // 启用链路追踪
}

// 枚举类型定义
type VersionStatus string
const (
	VersionStatusDraft      VersionStatus = "draft"      // 草稿
	VersionStatusBeta       VersionStatus = "beta"       // 测试版
	VersionStatusStable     VersionStatus = "stable"     // 稳定版
	VersionStatusDeprecated VersionStatus = "deprecated" // 已废弃
	VersionStatusSunset     VersionStatus = "sunset"     // 已下线
)

type RoutingStrategy string
const (
	RoutingStrategyHeader    RoutingStrategy = "header"     // 请求头路由
	RoutingStrategyPath      RoutingStrategy = "path"       // 路径路由
	RoutingStrategyQuery     RoutingStrategy = "query"      // 查询参数路由
	RoutingStrategySubdomain RoutingStrategy = "subdomain"  // 子域名路由
	RoutingStrategyContent   RoutingStrategy = "content"    // 内容协商路由
)

type FallbackPolicy string
const (
	FallbackPolicyLatest     FallbackPolicy = "latest"      // 最新版本
	FallbackPolicyDefault    FallbackPolicy = "default"     // 默认版本
	FallbackPolicyCompatible FallbackPolicy = "compatible"  // 兼容版本
	FallbackPolicyError      FallbackPolicy = "error"       // 返回错误
)

type PolicyAction string
const (
	PolicyActionRoute     PolicyAction = "route"      // 路由到指定版本
	PolicyActionReject    PolicyAction = "reject"     // 拒绝请求
	PolicyActionTransform PolicyAction = "transform"  // 转换请求
	PolicyActionLog       PolicyAction = "log"        // 记录日志
)

type CompatibilityType string
const (
	CompatibilityTypeBackward CompatibilityType = "backward" // 向后兼容
	CompatibilityTypeForward  CompatibilityType = "forward"  // 向前兼容
	CompatibilityTypeBridge   CompatibilityType = "bridge"   // 桥接兼容
)

type TransformType string
const (
	TransformTypeRename   TransformType = "rename"    // 重命名
	TransformTypeDefault  TransformType = "default"   // 设置默认值
	TransformTypeMapping  TransformType = "mapping"   // 值映射
	TransformTypeRemove   TransformType = "remove"    // 移除字段
	TransformTypeAdd      TransformType = "add"       // 添加字段
)

type DetectionMethod string
const (
	DetectionMethodHeader    DetectionMethod = "header"     // 请求头检测
	DetectionMethodPath      DetectionMethod = "path"       // 路径检测
	DetectionMethodQuery     DetectionMethod = "query"      // 查询参数检测
	DetectionMethodSubdomain DetectionMethod = "subdomain"  // 子域名检测
	DetectionMethodAccept    DetectionMethod = "accept"     // Accept头检测
)

type VersionFormat string
const (
	VersionFormatSemantic VersionFormat = "semantic" // 语义化版本 (v1.2.3)
	VersionFormatMajor    VersionFormat = "major"    // 主版本号 (v1, v2)
	VersionFormatDate     VersionFormat = "date"     // 日期版本 (2023-01-01)
	VersionFormatCustom   VersionFormat = "custom"   // 自定义格式
)

// NewVersionManager 创建版本管理器
func NewVersionManager(config VersionConfig, logger logger.Logger) *VersionManager {
	return &VersionManager{
		versions: make(map[string]*APIVersion),
		routes:   make(map[string]*VersionRoute),
		policies: make(map[string]*VersionPolicy),
		logger:   logger,
		config:   config,
	}
}

// RegisterVersion 注册API版本
func (vm *VersionManager) RegisterVersion(version *APIVersion) error {
	if version.Version == "" {
		return fmt.Errorf("版本号不能为空")
	}
	
	// 验证版本格式
	if err := vm.validateVersionFormat(version.Version); err != nil {
		return fmt.Errorf("版本格式验证失败: %w", err)
	}
	
	vm.mutex.Lock()
	defer vm.mutex.Unlock()
	
	// 检查版本是否已存在
	if _, exists := vm.versions[version.Version]; exists {
		return fmt.Errorf("版本已存在: %s", version.Version)
	}
	
	// 设置默认值
	if version.CreatedAt.IsZero() {
		version.CreatedAt = time.Now()
	}
	version.UpdatedAt = time.Now()
	
	if version.Status == "" {
		version.Status = VersionStatusDraft
	}
	
	if version.Metadata == nil {
		version.Metadata = make(map[string]string)
	}
	
	if version.ServiceMap == nil {
		version.ServiceMap = make(map[string]string)
	}
	
	// 如果是第一个版本或设置为默认版本，更新默认版本
	if len(vm.versions) == 0 || version.IsDefault {
		vm.setDefaultVersion(version.Version)
	}
	
	vm.versions[version.Version] = version
	
	vm.logger.Info("注册API版本",
		"version", version.Version,
		"name", version.Name,
		"status", version.Status,
		"is_default", version.IsDefault)
	
	return nil
}

// validateVersionFormat 验证版本格式
func (vm *VersionManager) validateVersionFormat(version string) error {
	switch vm.config.VersionFormat {
	case VersionFormatSemantic:
		// 语义化版本验证 (v1.2.3, v1.0.0-beta.1)
		pattern := `^v?(\d+)\.(\d+)\.(\d+)(?:-([a-zA-Z0-9\-\.]+))?(?:\+([a-zA-Z0-9\-\.]+))?$`
		matched, _ := regexp.MatchString(pattern, version)
		if !matched {
			return fmt.Errorf("无效的语义化版本格式: %s", version)
		}
	case VersionFormatMajor:
		// 主版本号验证 (v1, v2)
		pattern := `^v?(\d+)$`
		matched, _ := regexp.MatchString(pattern, version)
		if !matched {
			return fmt.Errorf("无效的主版本号格式: %s", version)
		}
	case VersionFormatDate:
		// 日期版本验证 (2023-01-01, 20230101)
		pattern := `^(\d{4})-?(0[1-9]|1[0-2])-?(0[1-9]|[12]\d|3[01])$`
		matched, _ := regexp.MatchString(pattern, version)
		if !matched {
			return fmt.Errorf("无效的日期版本格式: %s", version)
		}
	case VersionFormatCustom:
		// 自定义格式，暂时允许所有格式
		if version == "" {
			return fmt.Errorf("版本号不能为空")
		}
	default:
		return fmt.Errorf("不支持的版本格式: %s", vm.config.VersionFormat)
	}
	
	return nil
}

// setDefaultVersion 设置默认版本
func (vm *VersionManager) setDefaultVersion(version string) {
	// 清除其他版本的默认标记
	for _, v := range vm.versions {
		v.IsDefault = false
	}
	
	// 设置新的默认版本
	if v, exists := vm.versions[version]; exists {
		v.IsDefault = true
	}
	
	vm.config.DefaultVersion = version
}

// RegisterRoute 注册版本路由
func (vm *VersionManager) RegisterRoute(route *VersionRoute) error {
	if route.Path == "" || route.Method == "" {
		return fmt.Errorf("路由路径和方法不能为空")
	}

	vm.mutex.Lock()
	defer vm.mutex.Unlock()

	key := fmt.Sprintf("%s:%s", route.Method, route.Path)

	// 设置默认值
	if route.Strategy == "" {
		route.Strategy = RoutingStrategyHeader
	}

	if route.FallbackPolicy == "" {
		route.FallbackPolicy = FallbackPolicyDefault
	}

	if route.VersionMap == nil {
		route.VersionMap = make(map[string]string)
	}

	if route.Metadata == nil {
		route.Metadata = make(map[string]string)
	}

	vm.routes[key] = route

	vm.logger.Info("注册版本路由",
		"method", route.Method,
		"path", route.Path,
		"versions", route.Versions,
		"strategy", route.Strategy)

	return nil
}

// DetectVersion 检测请求版本
func (vm *VersionManager) DetectVersion(req *VersionRequest) (*VersionDetectionResult, error) {
	result := &VersionDetectionResult{
		DetectedVersion: "",
		Method:          DetectionMethodHeader,
		Confidence:      0.0,
		Fallback:        false,
	}

	// 按优先级检测版本
	for _, method := range vm.config.DetectionMethods {
		version, confidence := vm.detectVersionByMethod(req, method)
		if version != "" && confidence > result.Confidence {
			result.DetectedVersion = version
			result.Method = method
			result.Confidence = confidence
		}
	}

	// 如果没有检测到版本，使用默认版本
	if result.DetectedVersion == "" {
		result.DetectedVersion = vm.config.DefaultVersion
		result.Fallback = true
		result.Confidence = 1.0
	}

	// 验证检测到的版本是否存在
	if !vm.VersionExists(result.DetectedVersion) {
		// 尝试兼容性处理
		compatibleVersion := vm.findCompatibleVersion(result.DetectedVersion)
		if compatibleVersion != "" {
			result.DetectedVersion = compatibleVersion
			result.Fallback = true
		} else {
			return nil, fmt.Errorf("版本不存在: %s", result.DetectedVersion)
		}
	}

	vm.logger.Debug("版本检测完成",
		"detected_version", result.DetectedVersion,
		"method", result.Method,
		"confidence", result.Confidence,
		"fallback", result.Fallback)

	return result, nil
}

// detectVersionByMethod 通过指定方法检测版本
func (vm *VersionManager) detectVersionByMethod(req *VersionRequest, method DetectionMethod) (string, float64) {
	switch method {
	case DetectionMethodHeader:
		return vm.detectVersionFromHeader(req)
	case DetectionMethodPath:
		return vm.detectVersionFromPath(req)
	case DetectionMethodQuery:
		return vm.detectVersionFromQuery(req)
	case DetectionMethodSubdomain:
		return vm.detectVersionFromSubdomain(req)
	case DetectionMethodAccept:
		return vm.detectVersionFromAccept(req)
	default:
		return "", 0.0
	}
}

// detectVersionFromHeader 从请求头检测版本
func (vm *VersionManager) detectVersionFromHeader(req *VersionRequest) (string, float64) {
	// 检查常见的版本头
	headers := []string{"API-Version", "X-API-Version", "Version", "X-Version"}

	for _, header := range headers {
		if version := req.Headers[header]; version != "" {
			if vm.VersionExists(version) {
				return version, 1.0
			}
			// 尝试标准化版本格式
			if normalized := vm.normalizeVersion(version); vm.VersionExists(normalized) {
				return normalized, 0.9
			}
		}
	}

	return "", 0.0
}

// detectVersionFromPath 从路径检测版本
func (vm *VersionManager) detectVersionFromPath(req *VersionRequest) (string, float64) {
	// 匹配路径中的版本模式 /api/v1/users, /v2/apps
	patterns := []string{
		`/api/(v\d+(?:\.\d+)*)/`,
		`/(v\d+(?:\.\d+)*)/`,
		`/api/(v\d+)/`,
		`/(v\d+)/`,
	}

	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		if matches := re.FindStringSubmatch(req.Path); len(matches) > 1 {
			version := matches[1]
			if vm.VersionExists(version) {
				return version, 1.0
			}
		}
	}

	return "", 0.0
}

// detectVersionFromQuery 从查询参数检测版本
func (vm *VersionManager) detectVersionFromQuery(req *VersionRequest) (string, float64) {
	// 检查常见的版本查询参数
	params := []string{"version", "api_version", "v", "ver"}

	for _, param := range params {
		if version := req.QueryParams[param]; version != "" {
			if vm.VersionExists(version) {
				return version, 0.8 // 查询参数优先级稍低
			}
			if normalized := vm.normalizeVersion(version); vm.VersionExists(normalized) {
				return normalized, 0.7
			}
		}
	}

	return "", 0.0
}

// detectVersionFromSubdomain 从子域名检测版本
func (vm *VersionManager) detectVersionFromSubdomain(req *VersionRequest) (string, float64) {
	// 匹配子域名中的版本 v1.api.example.com, api-v2.example.com
	patterns := []string{
		`^(v\d+(?:\.\d+)*)\.`,
		`^api-(v\d+(?:\.\d+)*)\.`,
		`^(v\d+)-api\.`,
	}

	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		if matches := re.FindStringSubmatch(req.Host); len(matches) > 1 {
			version := matches[1]
			if vm.VersionExists(version) {
				return version, 0.9
			}
		}
	}

	return "", 0.0
}

// detectVersionFromAccept 从Accept头检测版本
func (vm *VersionManager) detectVersionFromAccept(req *VersionRequest) (string, float64) {
	accept := req.Headers["Accept"]
	if accept == "" {
		return "", 0.0
	}

	// 匹配 Accept 头中的版本信息
	// application/vnd.api+json;version=1
	// application/json;version=v2
	pattern := `version=([^;,\s]+)`
	re := regexp.MustCompile(pattern)

	if matches := re.FindStringSubmatch(accept); len(matches) > 1 {
		version := matches[1]
		if vm.VersionExists(version) {
			return version, 0.8
		}
		if normalized := vm.normalizeVersion(version); vm.VersionExists(normalized) {
			return normalized, 0.7
		}
	}

	return "", 0.0
}

// normalizeVersion 标准化版本格式
func (vm *VersionManager) normalizeVersion(version string) string {
	// 移除前缀和后缀空格
	version = strings.TrimSpace(version)

	// 确保版本以 v 开头（如果配置要求）
	if vm.config.VersionFormat == VersionFormatMajor || vm.config.VersionFormat == VersionFormatSemantic {
		if !strings.HasPrefix(version, "v") && regexp.MustCompile(`^\d+`).MatchString(version) {
			version = "v" + version
		}
	}

	return version
}

// findCompatibleVersion 查找兼容版本
func (vm *VersionManager) findCompatibleVersion(requestedVersion string) string {
	vm.mutex.RLock()
	defer vm.mutex.RUnlock()

	// 查找向后兼容的版本
	for _, version := range vm.versions {
		for _, compatible := range version.BackwardCompatible {
			if compatible == requestedVersion {
				return version.Version
			}
		}
	}

	// 如果没有找到兼容版本，返回默认版本
	if vm.config.DefaultVersion != "" && vm.VersionExists(vm.config.DefaultVersion) {
		return vm.config.DefaultVersion
	}

	return ""
}

// VersionExists 检查版本是否存在
func (vm *VersionManager) VersionExists(version string) bool {
	vm.mutex.RLock()
	defer vm.mutex.RUnlock()

	_, exists := vm.versions[version]
	return exists
}

// RegisterPolicy 注册版本策略
func (vm *VersionManager) RegisterPolicy(policy *VersionPolicy) error {
	if policy.Name == "" {
		return fmt.Errorf("策略名称不能为空")
	}

	vm.mutex.Lock()
	defer vm.mutex.Unlock()

	// 检查策略是否已存在
	if _, exists := vm.policies[policy.Name]; exists {
		return fmt.Errorf("策略已存在: %s", policy.Name)
	}

	// 设置默认值
	if policy.Metadata == nil {
		policy.Metadata = make(map[string]string)
	}

	vm.policies[policy.Name] = policy

	vm.logger.Info("注册版本策略",
		"name", policy.Name,
		"priority", policy.Priority,
		"enabled", policy.Enabled,
		"rules_count", len(policy.Rules))

	return nil
}

// GetVersion 获取版本信息
func (vm *VersionManager) GetVersion(version string) (*APIVersion, bool) {
	vm.mutex.RLock()
	defer vm.mutex.RUnlock()

	v, exists := vm.versions[version]
	return v, exists
}

// GetAllVersions 获取所有版本信息
func (vm *VersionManager) GetAllVersions() []*APIVersion {
	vm.mutex.RLock()
	defer vm.mutex.RUnlock()

	versions := make([]*APIVersion, 0, len(vm.versions))
	for _, version := range vm.versions {
		versions = append(versions, version)
	}

	return versions
}

// GetRoute 获取路由信息
func (vm *VersionManager) GetRoute(method, path string) (*VersionRoute, bool) {
	vm.mutex.RLock()
	defer vm.mutex.RUnlock()

	key := fmt.Sprintf("%s:%s", method, path)
	route, exists := vm.routes[key]
	return route, exists
}

// GetAllRoutes 获取所有路由信息
func (vm *VersionManager) GetAllRoutes() []*VersionRoute {
	vm.mutex.RLock()
	defer vm.mutex.RUnlock()

	routes := make([]*VersionRoute, 0, len(vm.routes))
	for _, route := range vm.routes {
		routes = append(routes, route)
	}

	return routes
}

// GetPolicy 获取策略信息
func (vm *VersionManager) GetPolicy(name string) (*VersionPolicy, bool) {
	vm.mutex.RLock()
	defer vm.mutex.RUnlock()

	policy, exists := vm.policies[name]
	return policy, exists
}

// GetAllPolicies 获取所有策略信息
func (vm *VersionManager) GetAllPolicies() []*VersionPolicy {
	vm.mutex.RLock()
	defer vm.mutex.RUnlock()

	policies := make([]*VersionPolicy, 0, len(vm.policies))
	for _, policy := range vm.policies {
		policies = append(policies, policy)
	}

	return policies
}
