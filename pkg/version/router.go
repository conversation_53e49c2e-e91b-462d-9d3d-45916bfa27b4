package version

import (
	"fmt"
	"net/url"
	"strings"
	"time"

	"paas-platform/pkg/logger"
)

// VersionRouter 版本路由器
type VersionRouter struct {
	manager *VersionManager
	logger  logger.Logger
	stats   map[string]*VersionStats
	cache   map[string]*CacheEntry
}

// CacheEntry 缓存条目
type CacheEntry struct {
	Result    *VersionRouteResult
	ExpiresAt time.Time
}

// NewVersionRouter 创建版本路由器
func NewVersionRouter(manager *VersionManager, logger logger.Logger) *VersionRouter {
	return &VersionRouter{
		manager: manager,
		logger:  logger,
		stats:   make(map[string]*VersionStats),
		cache:   make(map[string]*CacheEntry),
	}
}

// RouteRequest 路由请求到指定版本
func (vr *VersionRouter) RouteRequest(req *VersionRequest) (*VersionRouteResult, error) {
	// 检测版本
	detection, err := vr.manager.DetectVersion(req)
	if err != nil {
		return nil, fmt.Errorf("版本检测失败: %w", err)
	}

	// 查找路由配置
	route := vr.findRoute(req.Method, req.Path)
	if route == nil {
		// 没有特定路由配置，使用默认路由
		return vr.createDefaultRoute(detection.DetectedVersion, req)
	}

	// 检查版本是否支持
	if !vr.isVersionSupported(route, detection.DetectedVersion) {
		// 尝试兼容性处理
		compatibleVersion := vr.findCompatibleVersion(route, detection.DetectedVersion)
		if compatibleVersion == "" {
			return nil, fmt.Errorf("版本不支持: %s", detection.DetectedVersion)
		}
		detection.DetectedVersion = compatibleVersion
		detection.Fallback = true
	}

	// 构建路由结果
	result := &VersionRouteResult{
		TargetVersion: detection.DetectedVersion,
		RouteStrategy: route.Strategy,
		Applied:       true,
		Metadata:      make(map[string]string),
	}

	// 应用版本映射
	if mappedVersion, exists := route.VersionMap[detection.DetectedVersion]; exists {
		result.TargetVersion = mappedVersion
		result.Metadata["mapped_from"] = detection.DetectedVersion
	}

	// 构建目标URL
	targetURL, serviceName, err := vr.buildTargetURL(result.TargetVersion, req)
	if err != nil {
		return nil, fmt.Errorf("构建目标URL失败: %w", err)
	}

	result.TargetURL = targetURL
	result.ServiceName = serviceName

	// 应用兼容性转换
	if detection.Fallback {
		transformations, err := vr.applyCompatibilityTransforms(route, detection.OriginalVersion, result.TargetVersion, req)
		if err != nil {
			vr.logger.Warn("兼容性转换失败", "error", err)
		} else {
			result.Transformations = transformations
		}
	}

	// 更新统计信息
	vr.updateStats(result.TargetVersion, req)

	vr.logger.Debug("版本路由完成",
		"original_version", detection.OriginalVersion,
		"target_version", result.TargetVersion,
		"target_url", result.TargetURL,
		"fallback", detection.Fallback)

	return result, nil
}

// findRoute 查找路由配置
func (vr *VersionRouter) findRoute(method, path string) *VersionRoute {
	vr.manager.mutex.RLock()
	defer vr.manager.mutex.RUnlock()

	// 精确匹配
	key := fmt.Sprintf("%s:%s", method, path)
	if route, exists := vr.manager.routes[key]; exists {
		return route
	}

	// 模式匹配
	for routeKey, route := range vr.manager.routes {
		parts := strings.Split(routeKey, ":")
		if len(parts) != 2 {
			continue
		}

		routeMethod, routePath := parts[0], parts[1]
		if routeMethod == method && vr.matchPath(routePath, path) {
			return route
		}
	}

	return nil
}

// matchPath 路径匹配
func (vr *VersionRouter) matchPath(pattern, path string) bool {
	// 支持通配符和参数匹配
	if pattern == "*" {
		return true
	}

	// 移除版本前缀进行匹配
	cleanPath := vr.removeVersionFromPath(path)
	cleanPattern := vr.removeVersionFromPath(pattern)

	// 简单的路径匹配
	patternParts := strings.Split(cleanPattern, "/")
	pathParts := strings.Split(cleanPath, "/")

	if len(patternParts) != len(pathParts) {
		return false
	}

	for i, part := range patternParts {
		if strings.HasPrefix(part, "{") && strings.HasSuffix(part, "}") {
			// 路径参数，跳过
			continue
		}
		if part != pathParts[i] {
			return false
		}
	}

	return true
}

// removeVersionFromPath 从路径中移除版本信息
func (vr *VersionRouter) removeVersionFromPath(path string) string {
	// 移除常见的版本模式
	if matched := strings.Contains(path, "/v"); matched {
			parts := strings.Split(path, "/")
			var cleanParts []string
			for _, part := range parts {
				if !strings.HasPrefix(part, "v") || !vr.isVersionPart(part) {
					cleanParts = append(cleanParts, part)
				}
			}
			return strings.Join(cleanParts, "/")
		}

	return path
}

// isVersionPart 检查是否为版本部分
func (vr *VersionRouter) isVersionPart(part string) bool {
	return strings.HasPrefix(part, "v") && len(part) > 1 && 
		   (part[1] >= '0' && part[1] <= '9')
}

// isVersionSupported 检查版本是否支持
func (vr *VersionRouter) isVersionSupported(route *VersionRoute, version string) bool {
	if len(route.Versions) == 0 {
		return true // 没有限制，支持所有版本
	}

	for _, supportedVersion := range route.Versions {
		if supportedVersion == version {
			return true
		}
	}

	return false
}

// findCompatibleVersion 查找兼容版本
func (vr *VersionRouter) findCompatibleVersion(route *VersionRoute, requestedVersion string) string {
	// 检查路由的兼容性规则
	for _, rule := range route.CompatibilityRules {
		if rule.FromVersion == requestedVersion {
			return rule.ToVersion
		}
	}

	// 检查版本管理器的兼容性配置
	return vr.manager.findCompatibleVersion(requestedVersion)
}

// buildTargetURL 构建目标URL
func (vr *VersionRouter) buildTargetURL(version string, req *VersionRequest) (string, string, error) {
	vr.manager.mutex.RLock()
	apiVersion, exists := vr.manager.versions[version]
	vr.manager.mutex.RUnlock()

	if !exists {
		return "", "", fmt.Errorf("版本不存在: %s", version)
	}

	// 获取服务名称
	serviceName := vr.getServiceName(req.Path, apiVersion)
	
	// 构建基础URL
	baseURL := apiVersion.BaseURL
	if baseURL == "" {
		baseURL = vr.getDefaultBaseURL(serviceName)
	}

	// 构建完整URL
	targetURL, err := url.JoinPath(baseURL, req.Path)
	if err != nil {
		return "", "", fmt.Errorf("构建URL失败: %w", err)
	}

	// 添加查询参数
	if len(req.QueryParams) > 0 {
		u, err := url.Parse(targetURL)
		if err != nil {
			return "", "", fmt.Errorf("解析URL失败: %w", err)
		}

		query := u.Query()
		for key, value := range req.QueryParams {
			query.Set(key, value)
		}
		u.RawQuery = query.Encode()
		targetURL = u.String()
	}

	return targetURL, serviceName, nil
}

// getServiceName 获取服务名称
func (vr *VersionRouter) getServiceName(path string, version *APIVersion) string {
	// 从路径提取服务名称
	pathParts := strings.Split(strings.Trim(path, "/"), "/")
	
	// 跳过 api 和版本部分
	for i, part := range pathParts {
		if part == "api" || vr.isVersionPart(part) {
			continue
		}
		if i < len(pathParts) {
			serviceName := part
			// 检查服务映射
			if mapped, exists := version.ServiceMap[serviceName]; exists {
				return mapped
			}
			return serviceName
		}
	}

	return "default"
}

// getDefaultBaseURL 获取默认基础URL
func (vr *VersionRouter) getDefaultBaseURL(serviceName string) string {
	// 根据服务名称构建默认URL
	portMap := map[string]int{
		"users":   8081,
		"apps":    8082,
		"auth":    8083,
		"config":  8084,
		"scripts": 8085,
		"default": 8080,
	}

	port, exists := portMap[serviceName]
	if !exists {
		port = portMap["default"]
	}

	return fmt.Sprintf("http://localhost:%d", port)
}

// createDefaultRoute 创建默认路由
func (vr *VersionRouter) createDefaultRoute(version string, req *VersionRequest) (*VersionRouteResult, error) {
	targetURL, serviceName, err := vr.buildTargetURL(version, req)
	if err != nil {
		return nil, err
	}

	return &VersionRouteResult{
		TargetVersion: version,
		TargetURL:     targetURL,
		ServiceName:   serviceName,
		RouteStrategy: RoutingStrategyHeader,
		Applied:       false,
		Metadata:      map[string]string{"default_route": "true"},
	}, nil
}

// applyCompatibilityTransforms 应用兼容性转换
func (vr *VersionRouter) applyCompatibilityTransforms(route *VersionRoute, fromVersion, toVersion string, req *VersionRequest) ([]Transformation, error) {
	var transformations []Transformation

	// 查找适用的兼容性规则
	for _, rule := range route.CompatibilityRules {
		if rule.FromVersion == fromVersion && rule.ToVersion == toVersion {
			for _, transform := range rule.Transforms {
				transformation := Transformation{
					Type:        transform.Type,
					Source:      transform.Source,
					Target:      transform.Target,
					Description: fmt.Sprintf("兼容性转换: %s -> %s", fromVersion, toVersion),
				}

				// 应用转换逻辑
				switch transform.Type {
				case TransformTypeRename:
					transformation.OldValue = transform.Source
					transformation.NewValue = transform.Target
				case TransformTypeDefault:
					transformation.NewValue = transform.DefaultValue
				case TransformTypeMapping:
					// 应用值映射
					if mappedValue, exists := transform.Mapping[req.QueryParams[transform.Source]]; exists {
						transformation.OldValue = req.QueryParams[transform.Source]
						transformation.NewValue = mappedValue
					}
				}

				transformations = append(transformations, transformation)
			}
		}
	}

	return transformations, nil
}

// updateStats 更新统计信息
func (vr *VersionRouter) updateStats(version string, req *VersionRequest) {
	if !vr.manager.config.EnableMetrics {
		return
	}

	stats, exists := vr.stats[version]
	if !exists {
		stats = &VersionStats{
			Version:      version,
			RequestCount: 0,
			ErrorCount:   0,
			LastAccessed: time.Now(),
			TopPaths:     make([]PathStats, 0),
			Metadata:     make(map[string]string),
		}
		vr.stats[version] = stats
	}

	stats.RequestCount++
	stats.LastAccessed = time.Now()

	// 更新路径统计
	vr.updatePathStats(stats, req.Path)
}

// updatePathStats 更新路径统计
func (vr *VersionRouter) updatePathStats(stats *VersionStats, path string) {
	// 查找现有路径统计
	for i, pathStat := range stats.TopPaths {
		if pathStat.Path == path {
			stats.TopPaths[i].RequestCount++
			return
		}
	}

	// 添加新路径统计
	stats.TopPaths = append(stats.TopPaths, PathStats{
		Path:         path,
		RequestCount: 1,
		ErrorRate:    0.0,
	})

	// 保持前10个热门路径
	if len(stats.TopPaths) > 10 {
		stats.TopPaths = stats.TopPaths[:10]
	}
}

// GetStats 获取版本统计信息
func (vr *VersionRouter) GetStats(version string) *VersionStats {
	if stats, exists := vr.stats[version]; exists {
		return stats
	}
	return nil
}

// GetAllStats 获取所有版本统计信息
func (vr *VersionRouter) GetAllStats() map[string]*VersionStats {
	result := make(map[string]*VersionStats)
	for version, stats := range vr.stats {
		result[version] = stats
	}
	return result
}
