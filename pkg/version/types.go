package version

import (
	"time"
)

// VersionRequest 版本检测请求
type VersionRequest struct {
	Method      string            `json:"method"`       // HTTP 方法
	Path        string            `json:"path"`         // 请求路径
	Host        string            `json:"host"`         // 主机名
	Headers     map[string]string `json:"headers"`      // 请求头
	QueryParams map[string]string `json:"query_params"` // 查询参数
	Body        []byte            `json:"body"`         // 请求体
	RemoteAddr  string            `json:"remote_addr"`  // 客户端地址
	UserAgent   string            `json:"user_agent"`   // 用户代理
	Timestamp   time.Time         `json:"timestamp"`    // 请求时间
}

// VersionDetectionResult 版本检测结果
type VersionDetectionResult struct {
	DetectedVersion string          `json:"detected_version"` // 检测到的版本
	Method          DetectionMethod `json:"method"`           // 检测方法
	Confidence      float64         `json:"confidence"`       // 置信度 (0.0-1.0)
	Fallback        bool            `json:"fallback"`         // 是否为降级结果
	OriginalVersion string          `json:"original_version"` // 原始请求版本
	Reason          string          `json:"reason"`           // 检测原因
	Metadata        map[string]interface{} `json:"metadata"` // 额外元数据
}

// VersionRouteResult 版本路由结果
type VersionRouteResult struct {
	TargetVersion   string            `json:"target_version"`   // 目标版本
	TargetURL       string            `json:"target_url"`       // 目标URL
	ServiceName     string            `json:"service_name"`     // 服务名称
	RouteStrategy   RoutingStrategy   `json:"route_strategy"`   // 路由策略
	Applied         bool              `json:"applied"`          // 是否应用了路由
	Transformations []Transformation  `json:"transformations"`  // 应用的转换
	Metadata        map[string]string `json:"metadata"`         // 路由元数据
}

// Transformation 转换信息
type Transformation struct {
	Type        TransformType `json:"type"`         // 转换类型
	Source      string        `json:"source"`       // 源字段
	Target      string        `json:"target"`       // 目标字段
	OldValue    interface{}   `json:"old_value"`    // 原值
	NewValue    interface{}   `json:"new_value"`    // 新值
	Description string        `json:"description"`  // 转换描述
}

// VersionStats 版本统计信息
type VersionStats struct {
	Version         string            `json:"version"`          // 版本号
	RequestCount    int64             `json:"request_count"`    // 请求数量
	ErrorCount      int64             `json:"error_count"`      // 错误数量
	AvgResponseTime float64           `json:"avg_response_time"` // 平均响应时间
	LastAccessed    time.Time         `json:"last_accessed"`    // 最后访问时间
	ClientCount     int               `json:"client_count"`     // 客户端数量
	TopPaths        []PathStats       `json:"top_paths"`        // 热门路径
	Metadata        map[string]string `json:"metadata"`         // 统计元数据
}

// PathStats 路径统计
type PathStats struct {
	Path         string  `json:"path"`          // 路径
	RequestCount int64   `json:"request_count"` // 请求数量
	ErrorRate    float64 `json:"error_rate"`    // 错误率
}

// VersionHealth 版本健康状态
type VersionHealth struct {
	Version     string        `json:"version"`      // 版本号
	Status      VersionStatus `json:"status"`       // 版本状态
	Healthy     bool          `json:"healthy"`      // 是否健康
	Issues      []HealthIssue `json:"issues"`       // 健康问题
	LastCheck   time.Time     `json:"last_check"`   // 最后检查时间
	Uptime      time.Duration `json:"uptime"`       // 运行时间
	Metadata    map[string]interface{} `json:"metadata"` // 健康元数据
}

// HealthIssue 健康问题
type HealthIssue struct {
	Type        string    `json:"type"`         // 问题类型
	Severity    string    `json:"severity"`     // 严重程度
	Message     string    `json:"message"`      // 问题描述
	Timestamp   time.Time `json:"timestamp"`    // 发生时间
	Resolved    bool      `json:"resolved"`     // 是否已解决
	ResolvedAt  *time.Time `json:"resolved_at"` // 解决时间
}

// VersionMigration 版本迁移信息
type VersionMigration struct {
	ID              string            `json:"id"`               // 迁移ID
	FromVersion     string            `json:"from_version"`     // 源版本
	ToVersion       string            `json:"to_version"`       // 目标版本
	Status          MigrationStatus   `json:"status"`           // 迁移状态
	StartTime       time.Time         `json:"start_time"`       // 开始时间
	EndTime         *time.Time        `json:"end_time"`         // 结束时间
	Progress        float64           `json:"progress"`         // 进度 (0.0-1.0)
	AffectedClients int               `json:"affected_clients"` // 受影响的客户端数
	MigrationRules  []MigrationRule   `json:"migration_rules"`  // 迁移规则
	Metadata        map[string]string `json:"metadata"`         // 迁移元数据
}

// MigrationRule 迁移规则
type MigrationRule struct {
	Type        string      `json:"type"`         // 规则类型
	Condition   string      `json:"condition"`    // 条件表达式
	Action      string      `json:"action"`       // 执行动作
	Parameters  interface{} `json:"parameters"`   // 规则参数
	Description string      `json:"description"`  // 规则描述
}

// MigrationStatus 迁移状态
type MigrationStatus string
const (
	MigrationStatusPending    MigrationStatus = "pending"     // 待执行
	MigrationStatusRunning    MigrationStatus = "running"     // 执行中
	MigrationStatusCompleted  MigrationStatus = "completed"   // 已完成
	MigrationStatusFailed     MigrationStatus = "failed"      // 失败
	MigrationStatusRollback   MigrationStatus = "rollback"    // 回滚中
	MigrationStatusCancelled  MigrationStatus = "cancelled"   // 已取消
)

// VersionMetrics 版本指标
type VersionMetrics struct {
	Version         string            `json:"version"`          // 版本号
	Timestamp       time.Time         `json:"timestamp"`        // 时间戳
	RequestsPerSec  float64           `json:"requests_per_sec"` // 每秒请求数
	ErrorRate       float64           `json:"error_rate"`       // 错误率
	ResponseTime    ResponseTimeStats `json:"response_time"`    // 响应时间统计
	ThroughputMB    float64           `json:"throughput_mb"`    // 吞吐量(MB/s)
	ActiveClients   int               `json:"active_clients"`   // 活跃客户端数
	CacheHitRate    float64           `json:"cache_hit_rate"`   // 缓存命中率
	ResourceUsage   ResourceUsage     `json:"resource_usage"`   // 资源使用情况
}

// ResponseTimeStats 响应时间统计
type ResponseTimeStats struct {
	Min     float64 `json:"min"`     // 最小响应时间
	Max     float64 `json:"max"`     // 最大响应时间
	Avg     float64 `json:"avg"`     // 平均响应时间
	P50     float64 `json:"p50"`     // 50分位数
	P90     float64 `json:"p90"`     // 90分位数
	P95     float64 `json:"p95"`     // 95分位数
	P99     float64 `json:"p99"`     // 99分位数
}

// ResourceUsage 资源使用情况
type ResourceUsage struct {
	CPUPercent    float64 `json:"cpu_percent"`    // CPU使用率
	MemoryMB      float64 `json:"memory_mb"`      // 内存使用量(MB)
	DiskIOPS      float64 `json:"disk_iops"`      // 磁盘IOPS
	NetworkMbps   float64 `json:"network_mbps"`   // 网络带宽(Mbps)
	ConnectionCount int   `json:"connection_count"` // 连接数
}

// VersionEvent 版本事件
type VersionEvent struct {
	ID          string                 `json:"id"`           // 事件ID
	Type        VersionEventType       `json:"type"`         // 事件类型
	Version     string                 `json:"version"`      // 相关版本
	Timestamp   time.Time              `json:"timestamp"`    // 事件时间
	Source      string                 `json:"source"`       // 事件源
	Actor       string                 `json:"actor"`        // 操作者
	Description string                 `json:"description"`  // 事件描述
	Data        map[string]interface{} `json:"data"`         // 事件数据
	Severity    EventSeverity          `json:"severity"`     // 事件严重程度
}

// VersionEventType 版本事件类型
type VersionEventType string
const (
	EventTypeVersionCreated    VersionEventType = "version_created"     // 版本创建
	EventTypeVersionUpdated    VersionEventType = "version_updated"     // 版本更新
	EventTypeVersionDeprecated VersionEventType = "version_deprecated"  // 版本废弃
	EventTypeVersionSunset     VersionEventType = "version_sunset"      // 版本下线
	EventTypeRouteAdded        VersionEventType = "route_added"         // 路由添加
	EventTypeRouteUpdated      VersionEventType = "route_updated"       // 路由更新
	EventTypeRouteRemoved      VersionEventType = "route_removed"       // 路由移除
	EventTypePolicyApplied     VersionEventType = "policy_applied"      // 策略应用
	EventTypeMigrationStarted  VersionEventType = "migration_started"   // 迁移开始
	EventTypeMigrationCompleted VersionEventType = "migration_completed" // 迁移完成
	EventTypeHealthCheckFailed VersionEventType = "health_check_failed" // 健康检查失败
	EventTypeTrafficShifted    VersionEventType = "traffic_shifted"     // 流量切换
)

// EventSeverity 事件严重程度
type EventSeverity string
const (
	SeverityInfo     EventSeverity = "info"     // 信息
	SeverityWarning  EventSeverity = "warning"  // 警告
	SeverityError    EventSeverity = "error"    // 错误
	SeverityCritical EventSeverity = "critical" // 严重
)

// VersionConfig 版本配置选项
type VersionConfigOptions struct {
	// 检测配置
	HeaderNames     []string `json:"header_names"`     // 版本头名称列表
	QueryParamNames []string `json:"query_param_names"` // 查询参数名称列表
	PathPatterns    []string `json:"path_patterns"`    // 路径模式列表
	
	// 验证配置
	AllowedVersions []string `json:"allowed_versions"` // 允许的版本列表
	RequiredHeaders []string `json:"required_headers"` // 必需的请求头
	
	// 缓存配置
	CacheSize       int           `json:"cache_size"`       // 缓存大小
	CacheTTL        time.Duration `json:"cache_ttl"`        // 缓存TTL
	EnableCache     bool          `json:"enable_cache"`     // 启用缓存
	
	// 监控配置
	MetricsInterval time.Duration `json:"metrics_interval"` // 指标收集间隔
	HealthInterval  time.Duration `json:"health_interval"`  // 健康检查间隔
	EventRetention  time.Duration `json:"event_retention"`  // 事件保留时间
	
	// 安全配置
	RateLimitPerVersion map[string]int `json:"rate_limit_per_version"` // 每版本限流
	AllowedOrigins      []string       `json:"allowed_origins"`        // 允许的来源
	RequireAuth         bool           `json:"require_auth"`           // 需要认证
}

// DefaultVersionConfig 默认版本配置
func DefaultVersionConfig() VersionConfig {
	return VersionConfig{
		DetectionMethods: []DetectionMethod{
			DetectionMethodHeader,
			DetectionMethodPath,
			DetectionMethodQuery,
		},
		DefaultVersion:        "v1",
		VersionFormat:         VersionFormatMajor,
		StrictValidation:      false,
		EnableCompatibility:   true,
		MaxCompatibleVersions: 3,
		EnableCache:           true,
		CacheTTL:              5 * time.Minute,
		EnableMetrics:         true,
		EnableTracing:         true,
	}
}
