package version

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"paas-platform/pkg/logger"
)

func TestVersionManager_RegisterVersion(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultVersionConfig()
	manager := NewVersionManager(config, logger)

	// 测试注册有效版本
	version := &APIVersion{
		Version:     "v1",
		Name:        "API Version 1.0",
		Description: "第一个版本",
		Status:      VersionStatusStable,
		IsDefault:   true,
		IsStable:    true,
		BaseURL:     "http://localhost:8080",
		Metadata:    map[string]string{"test": "value"},
	}

	err := manager.RegisterVersion(version)
	require.NoError(t, err)

	// 验证版本已注册
	assert.True(t, manager.VersionExists("v1"))
	
	// 获取注册的版本
	registeredVersion, exists := manager.GetVersion("v1")
	assert.True(t, exists)
	assert.Equal(t, "v1", registeredVersion.Version)
	assert.Equal(t, "API Version 1.0", registeredVersion.Name)
	assert.True(t, registeredVersion.IsDefault)

	// 测试重复注册
	err = manager.RegisterVersion(version)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "版本已存在")
}

func TestVersionManager_RegisterRoute(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultVersionConfig()
	manager := NewVersionManager(config, logger)

	// 先注册版本
	version := &APIVersion{
		Version:   "v1",
		Name:      "API Version 1.0",
		Status:    VersionStatusStable,
		IsDefault: true,
	}
	err := manager.RegisterVersion(version)
	require.NoError(t, err)

	// 测试注册路由
	route := &VersionRoute{
		Path:           "/api/v1/users",
		Method:         "GET",
		Versions:       []string{"v1"},
		DefaultVersion: "v1",
		Strategy:       RoutingStrategyHeader,
		FallbackPolicy: FallbackPolicyDefault,
		Description:    "用户列表API",
	}

	err = manager.RegisterRoute(route)
	require.NoError(t, err)

	// 验证路由已注册
	registeredRoute, exists := manager.GetRoute("GET", "/api/v1/users")
	assert.True(t, exists)
	assert.Equal(t, "/api/v1/users", registeredRoute.Path)
	assert.Equal(t, "GET", registeredRoute.Method)
	assert.Equal(t, []string{"v1"}, registeredRoute.Versions)
}

func TestVersionManager_DetectVersion(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultVersionConfig()
	manager := NewVersionManager(config, logger)

	// 注册测试版本
	versions := []*APIVersion{
		{Version: "v1", Name: "Version 1", Status: VersionStatusStable, IsDefault: true},
		{Version: "v2", Name: "Version 2", Status: VersionStatusStable},
	}

	for _, v := range versions {
		err := manager.RegisterVersion(v)
		require.NoError(t, err)
	}

	tests := []struct {
		name           string
		request        *VersionRequest
		expectedVersion string
		expectedMethod DetectionMethod
	}{
		{
			name: "从请求头检测版本",
			request: &VersionRequest{
				Method: "GET",
				Path:   "/api/users",
				Headers: map[string]string{
					"API-Version": "v2",
				},
			},
			expectedVersion: "v2",
			expectedMethod:  DetectionMethodHeader,
		},
		{
			name: "从路径检测版本",
			request: &VersionRequest{
				Method: "GET",
				Path:   "/api/v1/users",
				Headers: map[string]string{},
			},
			expectedVersion: "v1",
			expectedMethod:  DetectionMethodPath,
		},
		{
			name: "从查询参数检测版本",
			request: &VersionRequest{
				Method: "GET",
				Path:   "/api/users",
				QueryParams: map[string]string{
					"version": "v2",
				},
			},
			expectedVersion: "v2",
			expectedMethod:  DetectionMethodQuery,
		},
		{
			name: "使用默认版本",
			request: &VersionRequest{
				Method:      "GET",
				Path:        "/api/users",
				Headers:     map[string]string{},
				QueryParams: map[string]string{},
			},
			expectedVersion: "v1", // 默认版本
			expectedMethod:  DetectionMethodHeader,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := manager.DetectVersion(tt.request)
			require.NoError(t, err)
			assert.Equal(t, tt.expectedVersion, result.DetectedVersion)
			assert.Equal(t, tt.expectedMethod, result.Method)
		})
	}
}

func TestVersionManager_ValidateVersionFormat(t *testing.T) {
	logger := logger.NewTestLogger()
	
	tests := []struct {
		name          string
		versionFormat VersionFormat
		version       string
		expectError   bool
	}{
		{
			name:          "有效的主版本号",
			versionFormat: VersionFormatMajor,
			version:       "v1",
			expectError:   false,
		},
		{
			name:          "有效的语义化版本",
			versionFormat: VersionFormatSemantic,
			version:       "v1.2.3",
			expectError:   false,
		},
		{
			name:          "有效的日期版本",
			versionFormat: VersionFormatDate,
			version:       "2023-01-01",
			expectError:   false,
		},
		{
			name:          "无效的主版本号",
			versionFormat: VersionFormatMajor,
			version:       "v1.2",
			expectError:   true,
		},
		{
			name:          "无效的语义化版本",
			versionFormat: VersionFormatSemantic,
			version:       "v1.2",
			expectError:   true,
		},
		{
			name:          "无效的日期版本",
			versionFormat: VersionFormatDate,
			version:       "2023-13-01",
			expectError:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config := DefaultVersionConfig()
			config.VersionFormat = tt.versionFormat
			manager := NewVersionManager(config, logger)

			err := manager.validateVersionFormat(tt.version)
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestVersionManager_FindCompatibleVersion(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultVersionConfig()
	manager := NewVersionManager(config, logger)

	// 注册版本
	versions := []*APIVersion{
		{
			Version:            "v1",
			Status:             VersionStatusStable,
			IsDefault:          true,
			BackwardCompatible: []string{"v0.9"},
		},
		{
			Version:            "v2",
			Status:             VersionStatusStable,
			BackwardCompatible: []string{"v1"},
		},
	}

	for _, v := range versions {
		err := manager.RegisterVersion(v)
		require.NoError(t, err)
	}

	// 测试兼容性查找
	compatibleVersion := manager.findCompatibleVersion("v0.9")
	assert.Equal(t, "v1", compatibleVersion)

	compatibleVersion = manager.findCompatibleVersion("v1")
	assert.Equal(t, "v2", compatibleVersion)

	// 测试不存在的版本
	compatibleVersion = manager.findCompatibleVersion("v99")
	assert.Equal(t, "v1", compatibleVersion) // 返回默认版本
}

func TestVersionManager_NormalizeVersion(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultVersionConfig()
	config.VersionFormat = VersionFormatMajor
	manager := NewVersionManager(config, logger)

	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "添加v前缀",
			input:    "1",
			expected: "v1",
		},
		{
			name:     "保持v前缀",
			input:    "v2",
			expected: "v2",
		},
		{
			name:     "去除空格",
			input:    " v1 ",
			expected: "v1",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := manager.normalizeVersion(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestVersionManager_SetDefaultVersion(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultVersionConfig()
	manager := NewVersionManager(config, logger)

	// 注册多个版本
	versions := []*APIVersion{
		{Version: "v1", Status: VersionStatusStable, IsDefault: true},
		{Version: "v2", Status: VersionStatusStable, IsDefault: false},
	}

	for _, v := range versions {
		err := manager.RegisterVersion(v)
		require.NoError(t, err)
	}

	// 验证初始默认版本
	v1, _ := manager.GetVersion("v1")
	v2, _ := manager.GetVersion("v2")
	assert.True(t, v1.IsDefault)
	assert.False(t, v2.IsDefault)

	// 更改默认版本
	manager.setDefaultVersion("v2")

	// 验证默认版本已更改
	v1, _ = manager.GetVersion("v1")
	v2, _ = manager.GetVersion("v2")
	assert.False(t, v1.IsDefault)
	assert.True(t, v2.IsDefault)
	assert.Equal(t, "v2", manager.config.DefaultVersion)
}

func TestVersionManager_GetAllVersions(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultVersionConfig()
	manager := NewVersionManager(config, logger)

	// 注册多个版本
	versions := []*APIVersion{
		{Version: "v1", Status: VersionStatusStable, IsDefault: true},
		{Version: "v2", Status: VersionStatusStable},
		{Version: "v3", Status: VersionStatusBeta},
	}

	for _, v := range versions {
		err := manager.RegisterVersion(v)
		require.NoError(t, err)
	}

	// 获取所有版本
	allVersions := manager.GetAllVersions()
	assert.Len(t, allVersions, 3)

	// 验证版本内容
	versionMap := make(map[string]*APIVersion)
	for _, v := range allVersions {
		versionMap[v.Version] = v
	}

	assert.Contains(t, versionMap, "v1")
	assert.Contains(t, versionMap, "v2")
	assert.Contains(t, versionMap, "v3")
	assert.True(t, versionMap["v1"].IsDefault)
	assert.Equal(t, VersionStatusBeta, versionMap["v3"].Status)
}

func TestDefaultVersionConfig(t *testing.T) {
	config := DefaultVersionConfig()

	// 验证默认配置
	assert.Equal(t, "v1", config.DefaultVersion)
	assert.Equal(t, VersionFormatMajor, config.VersionFormat)
	assert.False(t, config.StrictValidation)
	assert.True(t, config.EnableCompatibility)
	assert.Equal(t, 3, config.MaxCompatibleVersions)
	assert.True(t, config.EnableCache)
	assert.Equal(t, 5*time.Minute, config.CacheTTL)
	assert.True(t, config.EnableMetrics)
	assert.True(t, config.EnableTracing)

	// 验证检测方法
	expectedMethods := []DetectionMethod{
		DetectionMethodHeader,
		DetectionMethodPath,
		DetectionMethodQuery,
	}
	assert.Equal(t, expectedMethods, config.DetectionMethods)
}
