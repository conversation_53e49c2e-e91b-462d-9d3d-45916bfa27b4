package graphql

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"paas-platform/pkg/logger"
)

// APIHandler GraphQL API 处理器
type APIHandler struct {
	gateway *GraphQLGateway
	logger  logger.Logger
}

// NewAPIHandler 创建 API 处理器
func NewAPIHandler(gateway *GraphQLGateway, logger logger.Logger) *APIHandler {
	return &APIHandler{
		gateway: gateway,
		logger:  logger,
	}
}

// RegisterRoutes 注册路由
func (h *APIHandler) RegisterRoutes(router *gin.RouterGroup) {
	gql := router.Group("/graphql")
	{
		// 统计信息
		gql.GET("/stats", h.GetStats)
		
		// 服务管理
		gql.GET("/services", h.GetServices)
		gql.POST("/services", h.RegisterService)
		gql.GET("/services/:id", h.GetService)
		gql.PUT("/services/:id", h.UpdateService)
		gql.DELETE("/services/:id", h.UnregisterService)
		
		// Schema 管理
		gql.GET("/schema", h.GetSchema)
		gql.GET("/services/:id/schema", h.GetServiceSchema)
		gql.POST("/services/:id/schema", h.UpdateServiceSchema)
		
		// 查询管理
		gql.POST("/query", h.ExecuteQuery)
		gql.POST("/validate", h.ValidateQuery)
		gql.POST("/introspect", h.IntrospectSchema)
		
		// 缓存管理
		gql.GET("/cache/stats", h.GetCacheStats)
		gql.DELETE("/cache", h.ClearCache)
		gql.DELETE("/cache/:key", h.DeleteCacheKey)
		
		// 健康检查
		gql.GET("/health", h.GetHealth)
	}
}

// GetStats 获取统计信息
func (h *APIHandler) GetStats(c *gin.Context) {
	stats := h.gateway.GetStats()
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    stats,
	})
}

// GetServices 获取所有服务
func (h *APIHandler) GetServices(c *gin.Context) {
	services := h.gateway.GetServices()
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    services,
		"total":   len(services),
	})
}

// RegisterService 注册服务
func (h *APIHandler) RegisterService(c *gin.Context) {
	var req struct {
		ID          string            `json:"id" binding:"required"`
		Name        string            `json:"name" binding:"required"`
		URL         string            `json:"url" binding:"required"`
		Schema      string            `json:"schema"`
		Headers     map[string]string `json:"headers"`
		Timeout     int               `json:"timeout"`     // 秒
		RetryCount  int               `json:"retry_count"`
		Enabled     bool              `json:"enabled"`
		HealthCheck string            `json:"health_check"`
		Weight      int               `json:"weight"`
		Version     string            `json:"version"`
		Metadata    map[string]interface{} `json:"metadata"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    -1,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	service := &Service{
		ID:          req.ID,
		Name:        req.Name,
		URL:         req.URL,
		Schema:      req.Schema,
		Headers:     req.Headers,
		Timeout:     time.Duration(req.Timeout) * time.Second,
		RetryCount:  req.RetryCount,
		Enabled:     req.Enabled,
		HealthCheck: req.HealthCheck,
		Weight:      req.Weight,
		Version:     req.Version,
		Metadata:    req.Metadata,
	}

	if err := h.gateway.RegisterService(service); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    -1,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"code":    0,
		"message": "服务注册成功",
		"data": gin.H{
			"id": service.ID,
		},
	})
}

// GetService 获取指定服务
func (h *APIHandler) GetService(c *gin.Context) {
	id := c.Param("id")
	services := h.gateway.GetServices()

	for _, service := range services {
		if service.ID == id {
			c.JSON(http.StatusOK, gin.H{
				"code":    0,
				"message": "success",
				"data":    service,
			})
			return
		}
	}

	c.JSON(http.StatusNotFound, gin.H{
		"code":    -1,
		"message": "服务不存在",
	})
}

// UpdateService 更新服务
func (h *APIHandler) UpdateService(c *gin.Context) {
	id := c.Param("id")
	var req struct {
		Name        string            `json:"name"`
		URL         string            `json:"url"`
		Schema      string            `json:"schema"`
		Headers     map[string]string `json:"headers"`
		Timeout     int               `json:"timeout"`
		RetryCount  int               `json:"retry_count"`
		Enabled     *bool             `json:"enabled"`
		HealthCheck string            `json:"health_check"`
		Weight      int               `json:"weight"`
		Version     string            `json:"version"`
		Metadata    map[string]interface{} `json:"metadata"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    -1,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 先注销旧服务
	if err := h.gateway.UnregisterService(id); err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    -1,
			"message": err.Error(),
		})
		return
	}

	// 注册新服务
	service := &Service{
		ID:          id,
		Name:        req.Name,
		URL:         req.URL,
		Schema:      req.Schema,
		Headers:     req.Headers,
		Timeout:     time.Duration(req.Timeout) * time.Second,
		RetryCount:  req.RetryCount,
		Enabled:     req.Enabled != nil && *req.Enabled,
		HealthCheck: req.HealthCheck,
		Weight:      req.Weight,
		Version:     req.Version,
		Metadata:    req.Metadata,
	}

	if err := h.gateway.RegisterService(service); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    -1,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "服务更新成功",
	})
}

// UnregisterService 注销服务
func (h *APIHandler) UnregisterService(c *gin.Context) {
	id := c.Param("id")
	if err := h.gateway.UnregisterService(id); err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    -1,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "服务注销成功",
	})
}

// GetSchema 获取聚合 Schema
func (h *APIHandler) GetSchema(c *gin.Context) {
	schema := h.gateway.GetSchema()
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data": gin.H{
			"schema": schema,
		},
	})
}

// GetServiceSchema 获取服务 Schema
func (h *APIHandler) GetServiceSchema(c *gin.Context) {
	id := c.Param("id")
	services := h.gateway.GetServices()

	for _, service := range services {
		if service.ID == id {
			c.JSON(http.StatusOK, gin.H{
				"code":    0,
				"message": "success",
				"data": gin.H{
					"service_id": id,
					"schema":     service.Schema,
				},
			})
			return
		}
	}

	c.JSON(http.StatusNotFound, gin.H{
		"code":    -1,
		"message": "服务不存在",
	})
}

// UpdateServiceSchema 更新服务 Schema
func (h *APIHandler) UpdateServiceSchema(c *gin.Context) {
	id := c.Param("id")
	var req struct {
		Schema string `json:"schema" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    -1,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 获取现有服务
	services := h.gateway.GetServices()
	var existingService *Service
	for _, service := range services {
		if service.ID == id {
			existingService = service
			break
		}
	}

	if existingService == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    -1,
			"message": "服务不存在",
		})
		return
	}

	// 更新 Schema
	existingService.Schema = req.Schema

	// 重新注册服务
	if err := h.gateway.UnregisterService(id); err != nil {
		h.logger.Error("注销服务失败", "service_id", id, "error", err)
	}

	if err := h.gateway.RegisterService(existingService); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    -1,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "Schema 更新成功",
	})
}

// ExecuteQuery 执行查询
func (h *APIHandler) ExecuteQuery(c *gin.Context) {
	var req Request
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    -1,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 创建临时 Gin 上下文用于执行查询
	tempCtx := &gin.Context{Request: c.Request}
	tempCtx.Set("original_context", c)

	response, err := h.gateway.doExecuteQuery(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    -1,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// ValidateQuery 验证查询
func (h *APIHandler) ValidateQuery(c *gin.Context) {
	var req struct {
		Query string `json:"query" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    -1,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 简化的查询验证
	if len(req.Query) == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":    -1,
			"message": "查询不能为空",
			"valid":   false,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "查询验证通过",
		"valid":   true,
	})
}

// IntrospectSchema 内省 Schema
func (h *APIHandler) IntrospectSchema(c *gin.Context) {
	schema := h.gateway.GetSchema()
	
	// 简化的内省响应
	introspection := map[string]interface{}{
		"__schema": map[string]interface{}{
			"types": []map[string]interface{}{
				{
					"name": "Query",
					"kind": "OBJECT",
					"fields": []map[string]interface{}{
						{
							"name": "hello",
							"type": map[string]interface{}{
								"name": "String",
								"kind": "SCALAR",
							},
						},
					},
				},
			},
		},
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    introspection,
		"schema":  schema,
	})
}

// GetCacheStats 获取缓存统计
func (h *APIHandler) GetCacheStats(c *gin.Context) {
	// 简化实现：返回基础缓存信息
	stats := map[string]interface{}{
		"enabled": h.gateway.config.CacheEnabled,
		"ttl":     h.gateway.config.CacheTTL.String(),
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    stats,
	})
}

// ClearCache 清空缓存
func (h *APIHandler) ClearCache(c *gin.Context) {
	if h.gateway.cache != nil {
		if err := h.gateway.cache.Clear(); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    -1,
				"message": err.Error(),
			})
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "缓存已清空",
	})
}

// DeleteCacheKey 删除缓存键
func (h *APIHandler) DeleteCacheKey(c *gin.Context) {
	key := c.Param("key")
	if h.gateway.cache != nil {
		if err := h.gateway.cache.Delete(key); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    -1,
				"message": err.Error(),
			})
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "缓存键已删除",
	})
}

// GetHealth 获取健康状态
func (h *APIHandler) GetHealth(c *gin.Context) {
	stats := h.gateway.GetStats()
	services := h.gateway.GetServices()

	enabledServices := 0
	for _, service := range services {
		if service.Enabled {
			enabledServices++
		}
	}

	health := gin.H{
		"status": "healthy",
		"graphql": gin.H{
			"enabled":          h.gateway.config.Enabled,
			"total_services":   len(services),
			"enabled_services": enabledServices,
			"total_requests":   stats.TotalRequests,
			"success_requests": stats.SuccessRequests,
			"error_requests":   stats.ErrorRequests,
			"cache_enabled":    h.gateway.config.CacheEnabled,
			"last_updated":     stats.LastUpdated,
		},
	}

	c.JSON(http.StatusOK, health)
}
