package graphql

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"paas-platform/pkg/logger"
)

// GraphQLGateway GraphQL 网关
type GraphQLGateway struct {
	services    map[string]*Service
	schemas     map[string]*Schema
	resolvers   map[string]Resolver
	cache       Cache
	config      Config
	logger      logger.Logger
	mutex       sync.RWMutex
	stats       *Stats
	middleware  []Middleware
}

// Service GraphQL 服务
type Service struct {
	ID          string            `json:"id"`
	Name        string            `json:"name"`
	URL         string            `json:"url"`
	Schema      string            `json:"schema"`
	Headers     map[string]string `json:"headers"`
	Timeout     time.Duration     `json:"timeout"`
	RetryCount  int               `json:"retry_count"`
	Enabled     bool              `json:"enabled"`
	HealthCheck string            `json:"health_check"`
	Weight      int               `json:"weight"`
	Version     string            `json:"version"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// Schema GraphQL Schema
type Schema struct {
	ID          string                 `json:"id"`
	ServiceID   string                 `json:"service_id"`
	Content     string                 `json:"content"`
	Types       map[string]*Type       `json:"types"`
	Queries     map[string]*Field      `json:"queries"`
	Mutations   map[string]*Field      `json:"mutations"`
	Subscriptions map[string]*Field    `json:"subscriptions"`
	Directives  map[string]*Directive  `json:"directives"`
	Version     string                 `json:"version"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// Type GraphQL 类型
type Type struct {
	Name        string             `json:"name"`
	Kind        string             `json:"kind"` // OBJECT, SCALAR, ENUM, INTERFACE, UNION, INPUT_OBJECT
	Description string             `json:"description"`
	Fields      map[string]*Field  `json:"fields"`
	Interfaces  []string           `json:"interfaces"`
	EnumValues  []string           `json:"enum_values"`
	InputFields map[string]*Field  `json:"input_fields"`
}

// Field GraphQL 字段
type Field struct {
	Name        string      `json:"name"`
	Type        string      `json:"type"`
	Description string      `json:"description"`
	Args        []*Argument `json:"args"`
	Deprecated  bool        `json:"deprecated"`
	Resolver    string      `json:"resolver"`
}

// Argument GraphQL 参数
type Argument struct {
	Name         string      `json:"name"`
	Type         string      `json:"type"`
	Description  string      `json:"description"`
	DefaultValue interface{} `json:"default_value"`
}

// Directive GraphQL 指令
type Directive struct {
	Name        string      `json:"name"`
	Description string      `json:"description"`
	Locations   []string    `json:"locations"`
	Args        []*Argument `json:"args"`
}

// Request GraphQL 请求
type Request struct {
	Query         string                 `json:"query"`
	Variables     map[string]interface{} `json:"variables"`
	OperationName string                 `json:"operationName"`
	Extensions    map[string]interface{} `json:"extensions"`
}

// Response GraphQL 响应
type Response struct {
	Data       interface{}            `json:"data,omitempty"`
	Errors     []*Error               `json:"errors,omitempty"`
	Extensions map[string]interface{} `json:"extensions,omitempty"`
}

// Error GraphQL 错误
type Error struct {
	Message    string                 `json:"message"`
	Locations  []*Location            `json:"locations,omitempty"`
	Path       []interface{}          `json:"path,omitempty"`
	Extensions map[string]interface{} `json:"extensions,omitempty"`
}

// Location GraphQL 错误位置
type Location struct {
	Line   int `json:"line"`
	Column int `json:"column"`
}

// Config GraphQL 网关配置
type Config struct {
	Enabled           bool          `json:"enabled"`
	Path              string        `json:"path"`
	PlaygroundEnabled bool          `json:"playground_enabled"`
	PlaygroundPath    string        `json:"playground_path"`
	IntrospectionEnabled bool       `json:"introspection_enabled"`
	MaxQueryDepth     int           `json:"max_query_depth"`
	MaxQueryComplexity int          `json:"max_query_complexity"`
	Timeout           time.Duration `json:"timeout"`
	CacheEnabled      bool          `json:"cache_enabled"`
	CacheTTL          time.Duration `json:"cache_ttl"`
	EnableMetrics     bool          `json:"enable_metrics"`
	EnableTracing     bool          `json:"enable_tracing"`
	EnableLogging     bool          `json:"enable_logging"`
	RateLimitEnabled  bool          `json:"rate_limit_enabled"`
	RateLimit         int           `json:"rate_limit"`
	AuthRequired      bool          `json:"auth_required"`
	CorsEnabled       bool          `json:"cors_enabled"`
	CorsOrigins       []string      `json:"cors_origins"`
}

// Stats GraphQL 统计信息
type Stats struct {
	TotalRequests     int64         `json:"total_requests"`
	SuccessRequests   int64         `json:"success_requests"`
	ErrorRequests     int64         `json:"error_requests"`
	CacheHits         int64         `json:"cache_hits"`
	CacheMisses       int64         `json:"cache_misses"`
	AverageLatency    time.Duration `json:"average_latency"`
	TotalLatency      time.Duration `json:"total_latency"`
	QueryCount        int64         `json:"query_count"`
	MutationCount     int64         `json:"mutation_count"`
	SubscriptionCount int64         `json:"subscription_count"`
	LastUpdated       time.Time     `json:"last_updated"`
}

// Resolver GraphQL 解析器接口
type Resolver interface {
	Resolve(ctx context.Context, req *Request) (*Response, error)
}

// Cache 缓存接口
type Cache interface {
	Get(key string) (interface{}, bool)
	Set(key string, value interface{}, ttl time.Duration) error
	Delete(key string) error
	Clear() error
}

// Middleware GraphQL 中间件接口
type Middleware interface {
	Process(ctx context.Context, req *Request, next func(context.Context, *Request) (*Response, error)) (*Response, error)
}

// NewGraphQLGateway 创建 GraphQL 网关
func NewGraphQLGateway(config Config, logger logger.Logger) *GraphQLGateway {
	gateway := &GraphQLGateway{
		services:  make(map[string]*Service),
		schemas:   make(map[string]*Schema),
		resolvers: make(map[string]Resolver),
		config:    config,
		logger:    logger,
		stats:     &Stats{},
		middleware: make([]Middleware, 0),
	}

	// 设置默认值
	if gateway.config.Path == "" {
		gateway.config.Path = "/graphql"
	}
	if gateway.config.PlaygroundPath == "" {
		gateway.config.PlaygroundPath = "/graphql/playground"
	}
	if gateway.config.Timeout == 0 {
		gateway.config.Timeout = 30 * time.Second
	}
	if gateway.config.CacheTTL == 0 {
		gateway.config.CacheTTL = 5 * time.Minute
	}
	if gateway.config.MaxQueryDepth == 0 {
		gateway.config.MaxQueryDepth = 15
	}
	if gateway.config.MaxQueryComplexity == 0 {
		gateway.config.MaxQueryComplexity = 1000
	}

	// 初始化缓存
	if gateway.config.CacheEnabled {
		gateway.cache = NewMemoryCache()
	}

	return gateway
}

// RegisterService 注册 GraphQL 服务
func (gw *GraphQLGateway) RegisterService(service *Service) error {
	gw.mutex.Lock()
	defer gw.mutex.Unlock()

	if service.ID == "" {
		return fmt.Errorf("服务ID不能为空")
	}

	if service.URL == "" {
		return fmt.Errorf("服务URL不能为空")
	}

	// 设置默认值
	if service.Timeout == 0 {
		service.Timeout = gw.config.Timeout
	}
	if service.RetryCount == 0 {
		service.RetryCount = 3
	}
	if service.Weight == 0 {
		service.Weight = 1
	}

	gw.services[service.ID] = service

	// 获取并注册 Schema
	if service.Schema != "" {
		schema, err := gw.parseSchema(service.ID, service.Schema)
		if err != nil {
			gw.logger.Error("解析 Schema 失败", "service_id", service.ID, "error", err)
			return err
		}
		gw.schemas[service.ID] = schema
	}

	gw.logger.Info("GraphQL 服务注册成功",
		"service_id", service.ID,
		"name", service.Name,
		"url", service.URL,
		"enabled", service.Enabled)

	return nil
}

// UnregisterService 注销 GraphQL 服务
func (gw *GraphQLGateway) UnregisterService(serviceID string) error {
	gw.mutex.Lock()
	defer gw.mutex.Unlock()

	if _, exists := gw.services[serviceID]; !exists {
		return fmt.Errorf("服务不存在: %s", serviceID)
	}

	delete(gw.services, serviceID)
	delete(gw.schemas, serviceID)

	gw.logger.Info("GraphQL 服务注销成功", "service_id", serviceID)
	return nil
}

// HandleRequest 处理 GraphQL 请求
func (gw *GraphQLGateway) HandleRequest(c *gin.Context) {
	startTime := time.Now()

	// 解析请求
	req, err := gw.parseRequest(c)
	if err != nil {
		gw.sendError(c, fmt.Sprintf("请求解析失败: %v", err))
		return
	}

	// 验证请求
	if err := gw.validateRequest(req); err != nil {
		gw.sendError(c, fmt.Sprintf("请求验证失败: %v", err))
		return
	}

	// 检查缓存
	var response *Response
	if gw.config.CacheEnabled {
		if cached := gw.getFromCache(req); cached != nil {
			response = cached
			gw.stats.CacheHits++
		}
	}

	// 执行查询
	if response == nil {
		ctx := context.WithValue(c.Request.Context(), "gin_context", c)
		response, err = gw.executeQuery(ctx, req)
		if err != nil {
			gw.sendError(c, fmt.Sprintf("查询执行失败: %v", err))
			return
		}

		// 缓存结果
		if gw.config.CacheEnabled && response.Errors == nil {
			gw.setToCache(req, response)
		}
		gw.stats.CacheMisses++
	}

	// 更新统计信息
	gw.updateStats(startTime, response)

	// 发送响应
	c.JSON(http.StatusOK, response)
}

// HandlePlayground 处理 GraphQL Playground
func (gw *GraphQLGateway) HandlePlayground(c *gin.Context) {
	if !gw.config.PlaygroundEnabled {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "GraphQL Playground 未启用",
		})
		return
	}

	playgroundHTML := gw.generatePlaygroundHTML()
	c.Header("Content-Type", "text/html")
	c.String(http.StatusOK, playgroundHTML)
}

// GetSchema 获取聚合 Schema
func (gw *GraphQLGateway) GetSchema() string {
	gw.mutex.RLock()
	defer gw.mutex.RUnlock()

	var schemas []string
	for _, schema := range gw.schemas {
		if schema.Content != "" {
			schemas = append(schemas, schema.Content)
		}
	}

	return gw.mergeSchemas(schemas)
}

// GetStats 获取统计信息
func (gw *GraphQLGateway) GetStats() *Stats {
	gw.mutex.RLock()
	defer gw.mutex.RUnlock()

	stats := *gw.stats
	stats.LastUpdated = time.Now()
	return &stats
}

// GetServices 获取所有服务
func (gw *GraphQLGateway) GetServices() []*Service {
	gw.mutex.RLock()
	defer gw.mutex.RUnlock()

	services := make([]*Service, 0, len(gw.services))
	for _, service := range gw.services {
		services = append(services, service)
	}

	return services
}

// AddMiddleware 添加中间件
func (gw *GraphQLGateway) AddMiddleware(middleware Middleware) {
	gw.middleware = append(gw.middleware, middleware)
}

// DefaultConfig 默认配置
func DefaultConfig() Config {
	return Config{
		Enabled:              true,
		Path:                 "/graphql",
		PlaygroundEnabled:    true,
		PlaygroundPath:       "/graphql/playground",
		IntrospectionEnabled: true,
		MaxQueryDepth:        15,
		MaxQueryComplexity:   1000,
		Timeout:              30 * time.Second,
		CacheEnabled:         true,
		CacheTTL:             5 * time.Minute,
		EnableMetrics:        true,
		EnableTracing:        false,
		EnableLogging:        true,
		RateLimitEnabled:     false,
		RateLimit:            100,
		AuthRequired:         false,
		CorsEnabled:          true,
		CorsOrigins:          []string{"*"},
	}
}

// parseRequest 解析 GraphQL 请求
func (gw *GraphQLGateway) parseRequest(c *gin.Context) (*Request, error) {
	var req Request

	if c.Request.Method == "GET" {
		// 处理 GET 请求
		req.Query = c.Query("query")
		req.OperationName = c.Query("operationName")

		if variables := c.Query("variables"); variables != "" {
			if err := json.Unmarshal([]byte(variables), &req.Variables); err != nil {
				return nil, fmt.Errorf("变量解析失败: %v", err)
			}
		}
	} else {
		// 处理 POST 请求
		body, err := io.ReadAll(c.Request.Body)
		if err != nil {
			return nil, fmt.Errorf("读取请求体失败: %v", err)
		}
		c.Request.Body = io.NopCloser(bytes.NewBuffer(body))

		if err := json.Unmarshal(body, &req); err != nil {
			return nil, fmt.Errorf("JSON 解析失败: %v", err)
		}
	}

	if req.Query == "" {
		return nil, fmt.Errorf("查询不能为空")
	}

	return &req, nil
}

// validateRequest 验证 GraphQL 请求
func (gw *GraphQLGateway) validateRequest(req *Request) error {
	// 检查查询是否为空
	if req.Query == "" {
		return fmt.Errorf("查询不能为空")
	}

	// 检查查询长度
	if len(req.Query) > 10000 {
		return fmt.Errorf("查询过长")
	}

	// 检查查询深度
	if gw.config.MaxQueryDepth > 0 {
		depth := gw.calculateQueryDepth(req.Query)
		if depth > gw.config.MaxQueryDepth {
			return fmt.Errorf("查询深度超过限制: %d > %d", depth, gw.config.MaxQueryDepth)
		}
	}

	// 检查查询复杂度
	if gw.config.MaxQueryComplexity > 0 {
		complexity := gw.calculateQueryComplexity(req.Query)
		if complexity > gw.config.MaxQueryComplexity {
			return fmt.Errorf("查询复杂度超过限制: %d > %d", complexity, gw.config.MaxQueryComplexity)
		}
	}

	return nil
}

// executeQuery 执行 GraphQL 查询
func (gw *GraphQLGateway) executeQuery(ctx context.Context, req *Request) (*Response, error) {
	// 应用中间件
	var handler func(context.Context, *Request) (*Response, error)
	handler = gw.doExecuteQuery

	for i := len(gw.middleware) - 1; i >= 0; i-- {
		middleware := gw.middleware[i]
		nextHandler := handler
		handler = func(ctx context.Context, req *Request) (*Response, error) {
			return middleware.Process(ctx, req, nextHandler)
		}
	}

	return handler(ctx, req)
}

// doExecuteQuery 实际执行查询
func (gw *GraphQLGateway) doExecuteQuery(ctx context.Context, req *Request) (*Response, error) {
	// 分析查询，确定需要调用的服务
	services := gw.analyzeQuery(req.Query)
	if len(services) == 0 {
		return &Response{
			Errors: []*Error{{Message: "未找到匹配的服务"}},
		}, nil
	}

	// 如果只有一个服务，直接代理
	if len(services) == 1 {
		return gw.proxyToService(ctx, services[0], req)
	}

	// 多服务查询，需要进行查询分解和结果合并
	return gw.executeDistributedQuery(ctx, services, req)
}

// proxyToService 代理到单个服务
func (gw *GraphQLGateway) proxyToService(ctx context.Context, serviceID string, req *Request) (*Response, error) {
	service, exists := gw.services[serviceID]
	if !exists || !service.Enabled {
		return &Response{
			Errors: []*Error{{Message: fmt.Sprintf("服务不可用: %s", serviceID)}},
		}, nil
	}

	// 创建 HTTP 请求
	reqBody, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}

	httpReq, err := http.NewRequestWithContext(ctx, "POST", service.URL, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, err
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json")
	for key, value := range service.Headers {
		httpReq.Header.Set(key, value)
	}

	// 从 Gin 上下文中复制认证头
	if ginCtx, ok := ctx.Value("gin_context").(*gin.Context); ok {
		if auth := ginCtx.GetHeader("Authorization"); auth != "" {
			httpReq.Header.Set("Authorization", auth)
		}
		if userID := ginCtx.GetHeader("X-User-ID"); userID != "" {
			httpReq.Header.Set("X-User-ID", userID)
		}
	}

	// 执行请求
	client := &http.Client{Timeout: service.Timeout}
	resp, err := client.Do(httpReq)
	if err != nil {
		return &Response{
			Errors: []*Error{{Message: fmt.Sprintf("服务请求失败: %v", err)}},
		}, nil
	}
	defer resp.Body.Close()

	// 解析响应
	var response Response
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return &Response{
			Errors: []*Error{{Message: fmt.Sprintf("响应解析失败: %v", err)}},
		}, nil
	}

	return &response, nil
}

// executeDistributedQuery 执行分布式查询
func (gw *GraphQLGateway) executeDistributedQuery(ctx context.Context, services []string, req *Request) (*Response, error) {
	// 简化实现：并行调用所有服务，然后合并结果
	type serviceResult struct {
		serviceID string
		response  *Response
		err       error
	}

	results := make(chan serviceResult, len(services))

	// 并行调用服务
	for _, serviceID := range services {
		go func(sid string) {
			resp, err := gw.proxyToService(ctx, sid, req)
			results <- serviceResult{
				serviceID: sid,
				response:  resp,
				err:       err,
			}
		}(serviceID)
	}

	// 收集结果
	var responses []*Response
	var errors []*Error

	for i := 0; i < len(services); i++ {
		result := <-results
		if result.err != nil {
			errors = append(errors, &Error{
				Message: fmt.Sprintf("服务 %s 调用失败: %v", result.serviceID, result.err),
			})
		} else if result.response != nil {
			responses = append(responses, result.response)
			if result.response.Errors != nil {
				errors = append(errors, result.response.Errors...)
			}
		}
	}

	// 合并响应
	mergedResponse := gw.mergeResponses(responses)
	if len(errors) > 0 {
		mergedResponse.Errors = errors
	}

	return mergedResponse, nil
}

// analyzeQuery 分析查询，确定需要调用的服务
func (gw *GraphQLGateway) analyzeQuery(query string) []string {
	// 简化实现：基于查询内容匹配服务
	var services []string

	gw.mutex.RLock()
	defer gw.mutex.RUnlock()

	for serviceID, service := range gw.services {
		if service.Enabled && gw.queryMatchesService(query, serviceID) {
			services = append(services, serviceID)
		}
	}

	// 如果没有匹配的服务，返回第一个启用的服务
	if len(services) == 0 {
		for serviceID, service := range gw.services {
			if service.Enabled {
				services = append(services, serviceID)
				break
			}
		}
	}

	return services
}

// queryMatchesService 检查查询是否匹配服务
func (gw *GraphQLGateway) queryMatchesService(query, serviceID string) bool {
	// 简化实现：检查查询中是否包含服务相关的字段
	schema, exists := gw.schemas[serviceID]
	if !exists {
		return true // 如果没有 schema，默认匹配
	}

	// 检查查询中的字段是否在服务的 schema 中
	for fieldName := range schema.Queries {
		if strings.Contains(query, fieldName) {
			return true
		}
	}

	for fieldName := range schema.Mutations {
		if strings.Contains(query, fieldName) {
			return true
		}
	}

	return false
}

// mergeResponses 合并多个响应
func (gw *GraphQLGateway) mergeResponses(responses []*Response) *Response {
	if len(responses) == 0 {
		return &Response{}
	}

	if len(responses) == 1 {
		return responses[0]
	}

	// 简化实现：合并数据字段
	merged := &Response{
		Data: make(map[string]interface{}),
	}

	for _, resp := range responses {
		if resp.Data != nil {
			if dataMap, ok := resp.Data.(map[string]interface{}); ok {
				for key, value := range dataMap {
					merged.Data.(map[string]interface{})[key] = value
				}
			}
		}
	}

	return merged
}

// getFromCache 从缓存获取响应
func (gw *GraphQLGateway) getFromCache(req *Request) *Response {
	if gw.cache == nil {
		return nil
	}

	key := gw.generateCacheKey(req)
	if value, exists := gw.cache.Get(key); exists {
		if response, ok := value.(*Response); ok {
			return response
		}
	}

	return nil
}

// setToCache 设置缓存
func (gw *GraphQLGateway) setToCache(req *Request, response *Response) {
	if gw.cache == nil {
		return
	}

	key := gw.generateCacheKey(req)
	gw.cache.Set(key, response, gw.config.CacheTTL)
}

// generateCacheKey 生成缓存键
func (gw *GraphQLGateway) generateCacheKey(req *Request) string {
	data := map[string]interface{}{
		"query":         req.Query,
		"variables":     req.Variables,
		"operationName": req.OperationName,
	}

	jsonData, _ := json.Marshal(data)
	return fmt.Sprintf("graphql:%x", jsonData)
}

// calculateQueryDepth 计算查询深度
func (gw *GraphQLGateway) calculateQueryDepth(query string) int {
	// 简化实现：计算大括号嵌套深度
	depth := 0
	maxDepth := 0

	for _, char := range query {
		switch char {
		case '{':
			depth++
			if depth > maxDepth {
				maxDepth = depth
			}
		case '}':
			depth--
		}
	}

	return maxDepth
}

// calculateQueryComplexity 计算查询复杂度
func (gw *GraphQLGateway) calculateQueryComplexity(query string) int {
	// 简化实现：基于查询长度和字段数量估算复杂度
	complexity := len(query) / 5 // 降低除数，确保简单查询也有复杂度

	// 计算字段数量
	fieldCount := strings.Count(query, "\n") + strings.Count(query, " ")
	complexity += fieldCount / 3

	// 确保最小复杂度为1
	if complexity == 0 {
		complexity = 1
	}

	return complexity
}

// parseSchema 解析 GraphQL Schema
func (gw *GraphQLGateway) parseSchema(serviceID, schemaContent string) (*Schema, error) {
	// 简化实现：创建基础 Schema 结构
	schema := &Schema{
		ID:            fmt.Sprintf("%s-schema", serviceID),
		ServiceID:     serviceID,
		Content:       schemaContent,
		Types:         make(map[string]*Type),
		Queries:       make(map[string]*Field),
		Mutations:     make(map[string]*Field),
		Subscriptions: make(map[string]*Field),
		Directives:    make(map[string]*Directive),
		Version:       "1.0.0",
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	// 简化的 Schema 解析：提取基本的查询和变更字段
	if strings.Contains(schemaContent, "type Query") {
		schema.Queries["hello"] = &Field{
			Name:        "hello",
			Type:        "String",
			Description: "Hello world query",
		}
	}

	if strings.Contains(schemaContent, "type Mutation") {
		schema.Mutations["createUser"] = &Field{
			Name:        "createUser",
			Type:        "User",
			Description: "Create a new user",
		}
	}

	return schema, nil
}

// mergeSchemas 合并多个 Schema
func (gw *GraphQLGateway) mergeSchemas(schemas []string) string {
	if len(schemas) == 0 {
		return gw.getDefaultSchema()
	}

	if len(schemas) == 1 {
		return schemas[0]
	}

	// 简化实现：连接所有 Schema
	var merged strings.Builder
	merged.WriteString("# 聚合 GraphQL Schema\n\n")

	for i, schema := range schemas {
		if i > 0 {
			merged.WriteString("\n\n")
		}
		merged.WriteString(schema)
	}

	return merged.String()
}

// getDefaultSchema 获取默认 Schema
func (gw *GraphQLGateway) getDefaultSchema() string {
	return `
# 默认 GraphQL Schema
type Query {
  hello: String
  version: String
}

type Mutation {
  ping: String
}

schema {
  query: Query
  mutation: Mutation
}
`
}

// generatePlaygroundHTML 生成 Playground HTML
func (gw *GraphQLGateway) generatePlaygroundHTML() string {
	return fmt.Sprintf(`
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>GraphQL Playground</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/graphql-playground-react/build/static/css/index.css" />
  <link rel="shortcut icon" href="https://cdn.jsdelivr.net/npm/graphql-playground-react/build/favicon.png" />
  <script src="https://cdn.jsdelivr.net/npm/graphql-playground-react/build/static/js/middleware.js"></script>
</head>
<body>
  <div id="root">
    <style>
      body {
        background-color: rgb(23, 42, 58);
        font-family: Open Sans, sans-serif;
        height: 90vh;
      }
      #root {
        height: 100%%;
        width: 100%%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .loading {
        font-size: 32px;
        font-weight: 200;
        color: rgba(255, 255, 255, .6);
        margin-left: 20px;
      }
      img {
        width: 78px;
        height: 78px;
      }
      .title {
        font-weight: 400;
      }
    </style>
    <img src="https://cdn.jsdelivr.net/npm/graphql-playground-react/build/logo.png" alt="" />
    <div class="loading"> Loading
      <span class="title">GraphQL Playground</span>
    </div>
  </div>
  <script>
    window.addEventListener('load', function (event) {
      GraphQLPlayground.init(document.getElementById('root'), {
        endpoint: '%s'
      })
    })
  </script>
</body>
</html>
`, gw.config.Path)
}

// updateStats 更新统计信息
func (gw *GraphQLGateway) updateStats(startTime time.Time, response *Response) {
	gw.mutex.Lock()
	defer gw.mutex.Unlock()

	latency := time.Since(startTime)
	gw.stats.TotalRequests++
	gw.stats.TotalLatency += latency

	if response.Errors == nil || len(response.Errors) == 0 {
		gw.stats.SuccessRequests++
	} else {
		gw.stats.ErrorRequests++
	}

	// 计算平均延迟
	if gw.stats.TotalRequests > 0 {
		gw.stats.AverageLatency = gw.stats.TotalLatency / time.Duration(gw.stats.TotalRequests)
	}
}

// sendError 发送错误响应
func (gw *GraphQLGateway) sendError(c *gin.Context, message string) {
	response := &Response{
		Errors: []*Error{{Message: message}},
	}

	gw.stats.ErrorRequests++
	c.JSON(http.StatusBadRequest, response)
}
