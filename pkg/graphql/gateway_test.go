package graphql

import (
	"bytes"
	"encoding/json"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"paas-platform/pkg/logger"
)

func TestGraphQLGateway_Creation(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	
	gateway := NewGraphQLGateway(config, logger)
	
	assert.NotNil(t, gateway)
	assert.Equal(t, config.Path, gateway.config.Path)
	assert.Equal(t, config.PlaygroundPath, gateway.config.PlaygroundPath)
	assert.NotNil(t, gateway.services)
	assert.NotNil(t, gateway.schemas)
	assert.NotNil(t, gateway.stats)
}

func TestGraphQLGateway_ServiceManagement(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	gateway := NewGraphQLGateway(config, logger)
	
	// 测试注册服务
	service := &Service{
		ID:      "test-service",
		Name:    "测试服务",
		URL:     "http://localhost:4000/graphql",
		Schema:  "type Query { hello: String }",
		Enabled: true,
		Weight:  1,
	}
	
	err := gateway.RegisterService(service)
	require.NoError(t, err)
	
	// 验证服务已注册
	services := gateway.GetServices()
	assert.Len(t, services, 1)
	assert.Equal(t, "test-service", services[0].ID)
	assert.Equal(t, "测试服务", services[0].Name)
	assert.True(t, services[0].Enabled)
	
	// 测试重复注册服务
	err = gateway.RegisterService(service)
	assert.NoError(t, err) // 应该覆盖现有服务
	
	services = gateway.GetServices()
	assert.Len(t, services, 1)
	
	// 测试注销服务
	err = gateway.UnregisterService("test-service")
	require.NoError(t, err)
	
	services = gateway.GetServices()
	assert.Len(t, services, 0)
	
	// 测试注销不存在的服务
	err = gateway.UnregisterService("non-existent")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "服务不存在")
}

func TestGraphQLGateway_RequestParsing(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	gateway := NewGraphQLGateway(config, logger)
	
	// 测试 POST 请求解析
	reqData := Request{
		Query: "{ hello }",
		Variables: map[string]interface{}{
			"name": "world",
		},
		OperationName: "HelloQuery",
	}
	
	jsonData, _ := json.Marshal(reqData)
	req := httptest.NewRequest("POST", "/graphql", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req
	
	parsedReq, err := gateway.parseRequest(c)
	require.NoError(t, err)
	assert.Equal(t, "{ hello }", parsedReq.Query)
	assert.Equal(t, "HelloQuery", parsedReq.OperationName)
	assert.Equal(t, "world", parsedReq.Variables["name"])
	
	// 测试 GET 请求解析
	req = httptest.NewRequest("GET", "/graphql?query={hello}&operationName=HelloQuery", nil)
	c.Request = req
	
	parsedReq, err = gateway.parseRequest(c)
	require.NoError(t, err)
	assert.Equal(t, "{hello}", parsedReq.Query)
	assert.Equal(t, "HelloQuery", parsedReq.OperationName)
}

func TestGraphQLGateway_RequestValidation(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	config.MaxQueryDepth = 3
	config.MaxQueryComplexity = 100
	gateway := NewGraphQLGateway(config, logger)
	
	// 测试正常请求
	req := &Request{
		Query: "{ hello }",
	}
	
	err := gateway.validateRequest(req)
	assert.NoError(t, err)
	
	// 测试空查询
	req = &Request{
		Query: "",
	}

	err = gateway.validateRequest(req)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "查询不能为空")
	
	// 测试查询过长
	req = &Request{
		Query: string(make([]byte, 20000)),
	}
	
	err = gateway.validateRequest(req)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "查询过长")
}

func TestGraphQLGateway_QueryAnalysis(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	gateway := NewGraphQLGateway(config, logger)
	
	// 注册测试服务
	service := &Service{
		ID:      "user-service",
		Name:    "用户服务",
		URL:     "http://localhost:4001/graphql",
		Schema:  "type Query { user: User }",
		Enabled: true,
	}
	gateway.RegisterService(service)
	
	// 测试查询分析
	services := gateway.analyzeQuery("{ user { id name } }")
	assert.Contains(t, services, "user-service")
	
	// 测试不匹配的查询
	services = gateway.analyzeQuery("{ product { id name } }")
	assert.Len(t, services, 1) // 应该返回第一个启用的服务
}

func TestGraphQLGateway_SchemaManagement(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	gateway := NewGraphQLGateway(config, logger)
	
	// 测试默认 Schema
	schema := gateway.GetSchema()
	assert.Contains(t, schema, "type Query")
	assert.Contains(t, schema, "hello: String")
	
	// 注册带 Schema 的服务
	service := &Service{
		ID:     "test-service",
		Name:   "测试服务",
		URL:    "http://localhost:4000/graphql",
		Schema: "type Query { user: User } type User { id: ID! name: String! }",
		Enabled: true,
	}
	
	err := gateway.RegisterService(service)
	require.NoError(t, err)
	
	// 获取聚合 Schema
	schema = gateway.GetSchema()
	// 由于只有一个服务，应该直接返回该服务的 Schema
	assert.Contains(t, schema, "type Query")
}

func TestGraphQLGateway_Stats(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	gateway := NewGraphQLGateway(config, logger)
	
	stats := gateway.GetStats()
	assert.NotNil(t, stats)
	assert.Equal(t, int64(0), stats.TotalRequests)
	assert.Equal(t, int64(0), stats.SuccessRequests)
	assert.Equal(t, int64(0), stats.ErrorRequests)
	
	// 模拟更新统计信息
	response := &Response{
		Data: map[string]interface{}{"hello": "world"},
	}
	gateway.updateStats(time.Now().Add(-100*time.Millisecond), response)
	
	stats = gateway.GetStats()
	assert.Equal(t, int64(1), stats.TotalRequests)
	assert.Equal(t, int64(1), stats.SuccessRequests)
	assert.Equal(t, int64(0), stats.ErrorRequests)
	assert.True(t, stats.AverageLatency > 0)
}

func TestGraphQLGateway_Cache(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	config.CacheEnabled = true
	gateway := NewGraphQLGateway(config, logger)
	
	req := &Request{
		Query: "{ hello }",
		Variables: map[string]interface{}{
			"name": "world",
		},
	}
	
	// 测试缓存未命中
	cached := gateway.getFromCache(req)
	assert.Nil(t, cached)
	
	// 设置缓存
	response := &Response{
		Data: map[string]interface{}{"hello": "world"},
	}
	gateway.setToCache(req, response)
	
	// 测试缓存命中
	cached = gateway.getFromCache(req)
	assert.NotNil(t, cached)
	assert.Equal(t, response.Data, cached.Data)
}

func TestGraphQLGateway_QueryDepthCalculation(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	gateway := NewGraphQLGateway(config, logger)
	
	// 测试简单查询
	depth := gateway.calculateQueryDepth("{ hello }")
	assert.Equal(t, 1, depth)
	
	// 测试嵌套查询
	depth = gateway.calculateQueryDepth("{ user { profile { name } } }")
	assert.Equal(t, 3, depth)
	
	// 测试复杂嵌套
	depth = gateway.calculateQueryDepth("{ a { b { c { d { e } } } } }")
	assert.Equal(t, 5, depth)
}

func TestGraphQLGateway_QueryComplexityCalculation(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	gateway := NewGraphQLGateway(config, logger)

	// 测试简单查询
	complexity := gateway.calculateQueryComplexity("{ hello }")
	assert.True(t, complexity > 0)

	// 测试复杂查询
	longQuery := "{ user { id name email profile { avatar bio } posts { title content comments { author message } } } }"
	complexity2 := gateway.calculateQueryComplexity(longQuery)

	// 调试输出
	t.Logf("简单查询复杂度: %d", complexity)
	t.Logf("复杂查询复杂度: %d", complexity2)

	// 由于复杂查询明显更长，复杂度应该更高
	assert.True(t, complexity2 > complexity, "复杂查询的复杂度应该大于简单查询")
}

func TestGraphQLGateway_ResponseMerging(t *testing.T) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	gateway := NewGraphQLGateway(config, logger)
	
	// 测试空响应合并
	merged := gateway.mergeResponses([]*Response{})
	assert.NotNil(t, merged)
	
	// 测试单个响应
	response1 := &Response{
		Data: map[string]interface{}{"hello": "world"},
	}
	merged = gateway.mergeResponses([]*Response{response1})
	assert.Equal(t, response1, merged)
	
	// 测试多个响应合并
	response2 := &Response{
		Data: map[string]interface{}{"user": "john"},
	}
	merged = gateway.mergeResponses([]*Response{response1, response2})
	
	data, ok := merged.Data.(map[string]interface{})
	require.True(t, ok)
	assert.Equal(t, "world", data["hello"])
	assert.Equal(t, "john", data["user"])
}

func TestDefaultConfig(t *testing.T) {
	config := DefaultConfig()
	
	assert.True(t, config.Enabled)
	assert.Equal(t, "/graphql", config.Path)
	assert.Equal(t, "/graphql/playground", config.PlaygroundPath)
	assert.True(t, config.PlaygroundEnabled)
	assert.True(t, config.IntrospectionEnabled)
	assert.Equal(t, 15, config.MaxQueryDepth)
	assert.Equal(t, 1000, config.MaxQueryComplexity)
	assert.Equal(t, 30*time.Second, config.Timeout)
	assert.True(t, config.CacheEnabled)
	assert.Equal(t, 5*time.Minute, config.CacheTTL)
	assert.True(t, config.EnableMetrics)
	assert.False(t, config.EnableTracing)
	assert.True(t, config.EnableLogging)
	assert.False(t, config.RateLimitEnabled)
	assert.Equal(t, 100, config.RateLimit)
	assert.False(t, config.AuthRequired)
	assert.True(t, config.CorsEnabled)
	assert.Equal(t, []string{"*"}, config.CorsOrigins)
}

// 集成测试辅助函数
func setupTestServer(t *testing.T) (*GraphQLGateway, *httptest.Server) {
	logger := logger.NewTestLogger()
	config := DefaultConfig()
	gateway := NewGraphQLGateway(config, logger)
	
	gin.SetMode(gin.TestMode)
	router := gin.New()
	
	router.POST("/graphql", func(c *gin.Context) {
		gateway.HandleRequest(c)
	})
	
	router.GET("/graphql/playground", func(c *gin.Context) {
		gateway.HandlePlayground(c)
	})
	
	server := httptest.NewServer(router)
	return gateway, server
}

func TestGraphQLGateway_Integration(t *testing.T) {
	gateway, server := setupTestServer(t)
	defer server.Close()
	
	// 注册测试服务
	service := &Service{
		ID:      "test-service",
		Name:    "测试服务",
		URL:     server.URL + "/mock-graphql", // 模拟的 GraphQL 服务
		Schema:  "type Query { hello: String }",
		Enabled: true,
	}
	
	err := gateway.RegisterService(service)
	require.NoError(t, err)
	
	// 验证网关状态
	stats := gateway.GetStats()
	assert.NotNil(t, stats)
	assert.Equal(t, int64(0), stats.TotalRequests)
	
	services := gateway.GetServices()
	assert.Len(t, services, 1)
	assert.Equal(t, "test-service", services[0].ID)
}
