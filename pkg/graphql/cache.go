package graphql

import (
	"sync"
	"time"
)

// MemoryCache 内存缓存实现
type MemoryCache struct {
	data   map[string]*cacheItem
	mutex  sync.RWMutex
	ticker *time.Ticker
	done   chan bool
}

// cacheItem 缓存项
type cacheItem struct {
	value     interface{}
	expiresAt time.Time
}

// NewMemoryCache 创建内存缓存
func NewMemoryCache() *MemoryCache {
	cache := &MemoryCache{
		data:   make(map[string]*cacheItem),
		ticker: time.NewTicker(5 * time.Minute), // 每5分钟清理一次过期项
		done:   make(chan bool),
	}

	// 启动清理协程
	go cache.cleanup()

	return cache
}

// Get 获取缓存值
func (c *MemoryCache) Get(key string) (interface{}, bool) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	item, exists := c.data[key]
	if !exists {
		return nil, false
	}

	// 检查是否过期
	if time.Now().After(item.expiresAt) {
		// 过期了，但不在这里删除，留给清理协程处理
		return nil, false
	}

	return item.value, true
}

// Set 设置缓存值
func (c *MemoryCache) Set(key string, value interface{}, ttl time.Duration) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	expiresAt := time.Now().Add(ttl)
	c.data[key] = &cacheItem{
		value:     value,
		expiresAt: expiresAt,
	}

	return nil
}

// Delete 删除缓存值
func (c *MemoryCache) Delete(key string) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	delete(c.data, key)
	return nil
}

// Clear 清空缓存
func (c *MemoryCache) Clear() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.data = make(map[string]*cacheItem)
	return nil
}

// Size 获取缓存大小
func (c *MemoryCache) Size() int {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	return len(c.data)
}

// Keys 获取所有键
func (c *MemoryCache) Keys() []string {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	keys := make([]string, 0, len(c.data))
	for key := range c.data {
		keys = append(keys, key)
	}

	return keys
}

// Stats 获取缓存统计信息
func (c *MemoryCache) Stats() map[string]interface{} {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	now := time.Now()
	expired := 0
	valid := 0

	for _, item := range c.data {
		if now.After(item.expiresAt) {
			expired++
		} else {
			valid++
		}
	}

	return map[string]interface{}{
		"total_items":   len(c.data),
		"valid_items":   valid,
		"expired_items": expired,
	}
}

// cleanup 清理过期项
func (c *MemoryCache) cleanup() {
	for {
		select {
		case <-c.ticker.C:
			c.removeExpired()
		case <-c.done:
			return
		}
	}
}

// removeExpired 移除过期项
func (c *MemoryCache) removeExpired() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	now := time.Now()
	for key, item := range c.data {
		if now.After(item.expiresAt) {
			delete(c.data, key)
		}
	}
}

// Close 关闭缓存
func (c *MemoryCache) Close() {
	c.ticker.Stop()
	close(c.done)
}

// RedisCache Redis 缓存实现（接口定义）
type RedisCache struct {
	// Redis 客户端配置
	addr     string
	password string
	db       int
	// 这里可以添加 Redis 客户端实例
}

// NewRedisCache 创建 Redis 缓存
func NewRedisCache(addr, password string, db int) *RedisCache {
	return &RedisCache{
		addr:     addr,
		password: password,
		db:       db,
	}
}

// Get Redis 获取缓存值
func (c *RedisCache) Get(key string) (interface{}, bool) {
	// TODO: 实现 Redis 获取逻辑
	return nil, false
}

// Set Redis 设置缓存值
func (c *RedisCache) Set(key string, value interface{}, ttl time.Duration) error {
	// TODO: 实现 Redis 设置逻辑
	return nil
}

// Delete Redis 删除缓存值
func (c *RedisCache) Delete(key string) error {
	// TODO: 实现 Redis 删除逻辑
	return nil
}

// Clear Redis 清空缓存
func (c *RedisCache) Clear() error {
	// TODO: 实现 Redis 清空逻辑
	return nil
}

// CacheConfig 缓存配置
type CacheConfig struct {
	Type     string        `json:"type"`      // memory, redis
	TTL      time.Duration `json:"ttl"`       // 默认TTL
	MaxSize  int           `json:"max_size"`  // 最大缓存项数
	RedisURL string        `json:"redis_url"` // Redis连接URL
}

// CacheManager 缓存管理器
type CacheManager struct {
	cache  Cache
	config CacheConfig
}

// NewCacheManager 创建缓存管理器
func NewCacheManager(config CacheConfig) *CacheManager {
	var cache Cache

	switch config.Type {
	case "redis":
		// TODO: 创建 Redis 缓存
		cache = NewRedisCache("localhost:6379", "", 0)
	default:
		cache = NewMemoryCache()
	}

	return &CacheManager{
		cache:  cache,
		config: config,
	}
}

// GetCache 获取缓存实例
func (cm *CacheManager) GetCache() Cache {
	return cm.cache
}

// GetStats 获取缓存统计信息
func (cm *CacheManager) GetStats() map[string]interface{} {
	stats := map[string]interface{}{
		"type": cm.config.Type,
		"ttl":  cm.config.TTL.String(),
	}

	// 如果是内存缓存，添加详细统计
	if memCache, ok := cm.cache.(*MemoryCache); ok {
		cacheStats := memCache.Stats()
		for key, value := range cacheStats {
			stats[key] = value
		}
	}

	return stats
}

// DefaultCacheConfig 默认缓存配置
func DefaultCacheConfig() CacheConfig {
	return CacheConfig{
		Type:    "memory",
		TTL:     5 * time.Minute,
		MaxSize: 1000,
	}
}

// DevelopmentCacheConfig 开发环境缓存配置
func DevelopmentCacheConfig() CacheConfig {
	config := DefaultCacheConfig()
	config.TTL = 1 * time.Minute // 开发环境使用较短的TTL
	return config
}

// ProductionCacheConfig 生产环境缓存配置
func ProductionCacheConfig() CacheConfig {
	config := DefaultCacheConfig()
	config.Type = "redis"
	config.TTL = 10 * time.Minute
	config.MaxSize = 10000
	config.RedisURL = "redis://localhost:6379"
	return config
}
