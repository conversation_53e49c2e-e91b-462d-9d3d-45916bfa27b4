package circuitbreaker

import (
	"context"
	"sync"
	"time"

	"paas-platform/pkg/logger"
)

// Manager 断路器管理器
type Manager struct {
	breakers map[string]CircuitBreaker
	mutex    sync.RWMutex
	logger   logger.Logger
	config   ManagerConfig
}

// ManagerConfig 管理器配置
type ManagerConfig struct {
	DefaultConfig    Config        `json:"default_config"`     // 默认断路器配置
	EnableMetrics    bool          `json:"enable_metrics"`     // 启用指标收集
	MetricsInterval  time.Duration `json:"metrics_interval"`   // 指标收集间隔
	EnableHealthCheck bool         `json:"enable_health_check"` // 启用健康检查
	HealthCheckInterval time.Duration `json:"health_check_interval"` // 健康检查间隔
	AutoCleanup      bool          `json:"auto_cleanup"`       // 自动清理
	CleanupInterval  time.Duration `json:"cleanup_interval"`   // 清理间隔
	MaxBreakers      int           `json:"max_breakers"`       // 最大断路器数量
}

// BreakerInfo 断路器信息
type BreakerInfo struct {
	Name         string    `json:"name"`          // 名称
	State        State     `json:"state"`         // 状态
	Stats        *Stats    `json:"stats"`         // 统计信息
	Config       Config    `json:"config"`        // 配置
	CreatedAt    time.Time `json:"created_at"`    // 创建时间
	LastUsed     time.Time `json:"last_used"`     // 最后使用时间
	UsageCount   int64     `json:"usage_count"`   // 使用次数
}

// HealthStatus 健康状态
type HealthStatus struct {
	Healthy       bool                    `json:"healthy"`        // 是否健康
	TotalBreakers int                     `json:"total_breakers"` // 总断路器数
	OpenBreakers  int                     `json:"open_breakers"`  // 开启状态断路器数
	Breakers      map[string]*BreakerInfo `json:"breakers"`       // 断路器信息
	CheckTime     time.Time               `json:"check_time"`     // 检查时间
}

// NewManager 创建断路器管理器
func NewManager(config ManagerConfig, logger logger.Logger) *Manager {
	m := &Manager{
		breakers: make(map[string]CircuitBreaker),
		logger:   logger,
		config:   config,
	}

	// 设置默认值
	if m.config.MetricsInterval <= 0 {
		m.config.MetricsInterval = 30 * time.Second
	}
	if m.config.HealthCheckInterval <= 0 {
		m.config.HealthCheckInterval = 60 * time.Second
	}
	if m.config.CleanupInterval <= 0 {
		m.config.CleanupInterval = 300 * time.Second
	}
	if m.config.MaxBreakers <= 0 {
		m.config.MaxBreakers = 1000
	}

	// 启动后台任务
	if m.config.EnableMetrics {
		go m.metricsCollector()
	}
	if m.config.EnableHealthCheck {
		go m.healthChecker()
	}
	if m.config.AutoCleanup {
		go m.cleaner()
	}

	return m
}

// GetOrCreate 获取或创建断路器
func (m *Manager) GetOrCreate(name string, config *Config) CircuitBreaker {
	m.mutex.RLock()
	if breaker, exists := m.breakers[name]; exists {
		m.mutex.RUnlock()
		return breaker
	}
	m.mutex.RUnlock()

	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 双重检查
	if breaker, exists := m.breakers[name]; exists {
		return breaker
	}

	// 检查断路器数量限制
	if len(m.breakers) >= m.config.MaxBreakers {
		m.logger.Warn("断路器数量已达上限", "max", m.config.MaxBreakers, "current", len(m.breakers))
		// 可以考虑清理一些不活跃的断路器
		m.cleanupInactive()
	}

	// 使用提供的配置或默认配置
	var breakerConfig Config
	if config != nil {
		breakerConfig = *config
	} else {
		breakerConfig = m.config.DefaultConfig
	}
	breakerConfig.Name = name

	// 创建断路器
	breaker := NewCircuitBreaker(breakerConfig, m.logger)
	m.breakers[name] = breaker

	m.logger.Info("创建断路器", "name", name, "config", breakerConfig)
	return breaker
}

// Get 获取断路器
func (m *Manager) Get(name string) (CircuitBreaker, bool) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	breaker, exists := m.breakers[name]
	return breaker, exists
}

// Remove 移除断路器
func (m *Manager) Remove(name string) bool {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if _, exists := m.breakers[name]; exists {
		delete(m.breakers, name)
		m.logger.Info("移除断路器", "name", name)
		return true
	}
	return false
}

// List 列出所有断路器
func (m *Manager) List() []string {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	names := make([]string, 0, len(m.breakers))
	for name := range m.breakers {
		names = append(names, name)
	}
	return names
}

// GetStats 获取所有断路器统计信息
func (m *Manager) GetStats() map[string]*Stats {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	stats := make(map[string]*Stats)
	for name, breaker := range m.breakers {
		stats[name] = breaker.GetStats()
	}
	return stats
}

// GetHealthStatus 获取健康状态
func (m *Manager) GetHealthStatus() *HealthStatus {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	status := &HealthStatus{
		Healthy:       true,
		TotalBreakers: len(m.breakers),
		OpenBreakers:  0,
		Breakers:      make(map[string]*BreakerInfo),
		CheckTime:     time.Now(),
	}

	for name, breaker := range m.breakers {
		stats := breaker.GetStats()
		info := &BreakerInfo{
			Name:      name,
			State:     stats.State,
			Stats:     stats,
			CreatedAt: time.Now(), // 简化实现
			LastUsed:  time.Now(), // 简化实现
		}

		if stats.State == StateOpen {
			status.OpenBreakers++
			status.Healthy = false
		}

		status.Breakers[name] = info
	}

	return status
}

// Execute 执行函数（通过指定断路器）
func (m *Manager) Execute(ctx context.Context, breakerName string, fn func() (interface{}, error)) (interface{}, error) {
	breaker := m.GetOrCreate(breakerName, nil)
	return breaker.Execute(ctx, fn)
}

// ExecuteWithFallback 执行函数（带降级）
func (m *Manager) ExecuteWithFallback(ctx context.Context, breakerName string, fn func() (interface{}, error), fallback func() (interface{}, error)) (interface{}, error) {
	breaker := m.GetOrCreate(breakerName, nil)
	return breaker.ExecuteWithFallback(ctx, fn, fallback)
}

// ExecuteWithConfig 使用指定配置执行函数
func (m *Manager) ExecuteWithConfig(ctx context.Context, breakerName string, config Config, fn func() (interface{}, error)) (interface{}, error) {
	breaker := m.GetOrCreate(breakerName, &config)
	return breaker.Execute(ctx, fn)
}

// ResetAll 重置所有断路器
func (m *Manager) ResetAll() {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	for name, breaker := range m.breakers {
		breaker.Reset()
		m.logger.Info("重置断路器", "name", name)
	}
}

// ForceOpenAll 强制开启所有断路器
func (m *Manager) ForceOpenAll() {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	for name, breaker := range m.breakers {
		breaker.ForceOpen()
		m.logger.Info("强制开启断路器", "name", name)
	}
}

// ForceCloseAll 强制关闭所有断路器
func (m *Manager) ForceCloseAll() {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	for name, breaker := range m.breakers {
		breaker.ForceClose()
		m.logger.Info("强制关闭断路器", "name", name)
	}
}

// metricsCollector 指标收集器
func (m *Manager) metricsCollector() {
	ticker := time.NewTicker(m.config.MetricsInterval)
	defer ticker.Stop()

	for range ticker.C {
		m.collectMetrics()
	}
}

// collectMetrics 收集指标
func (m *Manager) collectMetrics() {
	stats := m.GetStats()
	
	totalBreakers := len(stats)
	openBreakers := 0
	halfOpenBreakers := 0
	closedBreakers := 0

	for _, stat := range stats {
		switch stat.State {
		case StateOpen:
			openBreakers++
		case StateHalfOpen:
			halfOpenBreakers++
		case StateClosed:
			closedBreakers++
		}
	}

	m.logger.Debug("断路器指标收集",
		"total", totalBreakers,
		"open", openBreakers,
		"half_open", halfOpenBreakers,
		"closed", closedBreakers)
}

// healthChecker 健康检查器
func (m *Manager) healthChecker() {
	ticker := time.NewTicker(m.config.HealthCheckInterval)
	defer ticker.Stop()

	for range ticker.C {
		m.performHealthCheck()
	}
}

// performHealthCheck 执行健康检查
func (m *Manager) performHealthCheck() {
	status := m.GetHealthStatus()
	
	if !status.Healthy {
		m.logger.Warn("断路器健康检查失败",
			"total_breakers", status.TotalBreakers,
			"open_breakers", status.OpenBreakers)
	} else {
		m.logger.Debug("断路器健康检查通过",
			"total_breakers", status.TotalBreakers)
	}
}

// cleaner 清理器
func (m *Manager) cleaner() {
	ticker := time.NewTicker(m.config.CleanupInterval)
	defer ticker.Stop()

	for range ticker.C {
		m.performCleanup()
	}
}

// performCleanup 执行清理
func (m *Manager) performCleanup() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 简化实现：这里可以添加更复杂的清理逻辑
	// 例如清理长时间未使用的断路器
	m.logger.Debug("执行断路器清理", "total", len(m.breakers))
}

// cleanupInactive 清理不活跃的断路器
func (m *Manager) cleanupInactive() {
	// 简化实现：实际应该根据使用时间等因素清理
	if len(m.breakers) > 0 {
		// 清理一个断路器为新的让路
		for name := range m.breakers {
			delete(m.breakers, name)
			m.logger.Info("清理不活跃断路器", "name", name)
			break
		}
	}
}

// DefaultManagerConfig 默认管理器配置
func DefaultManagerConfig() ManagerConfig {
	return ManagerConfig{
		DefaultConfig: Config{
			MaxRequests:         1,
			Interval:            60 * time.Second,
			Timeout:             60 * time.Second,
			FailureThreshold:    5,
			SuccessThreshold:    1,
			MinRequestThreshold: 10,
			FailureRate:         0.5,
			SlowCallThreshold:   5 * time.Second,
			SlowCallRate:        0.5,
		},
		EnableMetrics:       true,
		MetricsInterval:     30 * time.Second,
		EnableHealthCheck:   true,
		HealthCheckInterval: 60 * time.Second,
		AutoCleanup:         true,
		CleanupInterval:     300 * time.Second,
		MaxBreakers:         1000,
	}
}

// DevelopmentManagerConfig 开发环境管理器配置
func DevelopmentManagerConfig() ManagerConfig {
	config := DefaultManagerConfig()
	config.DefaultConfig.Timeout = 30 * time.Second
	config.DefaultConfig.FailureThreshold = 3
	config.DefaultConfig.MinRequestThreshold = 5
	config.MetricsInterval = 10 * time.Second
	config.HealthCheckInterval = 30 * time.Second
	return config
}

// ProductionManagerConfig 生产环境管理器配置
func ProductionManagerConfig() ManagerConfig {
	config := DefaultManagerConfig()
	config.DefaultConfig.Timeout = 120 * time.Second
	config.DefaultConfig.FailureThreshold = 10
	config.DefaultConfig.MinRequestThreshold = 20
	config.MetricsInterval = 60 * time.Second
	config.HealthCheckInterval = 120 * time.Second
	return config
}
