package circuitbreaker

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"paas-platform/pkg/logger"
)

func TestCircuitBreaker_Execute(t *testing.T) {
	logger := logger.NewTestLogger()
	config := Config{
		Name:                "test-cb",
		MaxRequests:         1,
		Interval:            100 * time.Millisecond,
		Timeout:             200 * time.Millisecond,
		FailureThreshold:    3,
		SuccessThreshold:    1,
		MinRequestThreshold: 2,
		FailureRate:         0.5,
	}

	cb := NewCircuitBreaker(config, logger)

	// 测试成功执行
	result, err := cb.Execute(context.Background(), func() (interface{}, error) {
		return "success", nil
	})
	require.NoError(t, err)
	assert.Equal(t, "success", result)
	assert.Equal(t, StateClosed, cb.GetState())

	// 测试失败执行
	_, err = cb.Execute(context.Background(), func() (interface{}, error) {
		return nil, errors.New("test error")
	})
	assert.Error(t, err)
	assert.Equal(t, StateClosed, cb.GetState()) // 还未达到熔断条件

	// 连续失败直到熔断
	for i := 0; i < 5; i++ {
		cb.Execute(context.Background(), func() (interface{}, error) {
			return nil, errors.New("test error")
		})
	}

	// 检查是否熔断
	assert.Equal(t, StateOpen, cb.GetState())

	// 熔断状态下执行应该立即失败
	_, err = cb.Execute(context.Background(), func() (interface{}, error) {
		return "should not execute", nil
	})
	assert.Error(t, err)
	assert.Equal(t, ErrOpenState, err)
}

func TestCircuitBreaker_ExecuteWithFallback(t *testing.T) {
	logger := logger.NewTestLogger()
	config := Config{
		Name:                "test-cb-fallback",
		MaxRequests:         1,
		Interval:            100 * time.Millisecond,
		Timeout:             200 * time.Millisecond,
		FailureThreshold:    2,
		SuccessThreshold:    1,
		MinRequestThreshold: 1,
		FailureRate:         0.5,
	}

	cb := NewCircuitBreaker(config, logger)

	// 先让断路器熔断
	for i := 0; i < 3; i++ {
		cb.Execute(context.Background(), func() (interface{}, error) {
			return nil, errors.New("test error")
		})
	}

	assert.Equal(t, StateOpen, cb.GetState())

	// 测试降级执行
	result, err := cb.ExecuteWithFallback(
		context.Background(),
		func() (interface{}, error) {
			return "primary", nil
		},
		func() (interface{}, error) {
			return "fallback", nil
		},
	)

	require.NoError(t, err)
	assert.Equal(t, "fallback", result)
}

func TestCircuitBreaker_StateTransitions(t *testing.T) {
	logger := logger.NewTestLogger()
	config := Config{
		Name:                "test-cb-states",
		MaxRequests:         2,
		Interval:            50 * time.Millisecond,
		Timeout:             100 * time.Millisecond,
		FailureThreshold:    2,
		SuccessThreshold:    2,
		MinRequestThreshold: 1,
		FailureRate:         0.5,
	}

	cb := NewCircuitBreaker(config, logger)

	// 初始状态应该是关闭
	assert.Equal(t, StateClosed, cb.GetState())

	// 连续失败导致熔断
	for i := 0; i < 3; i++ {
		cb.Execute(context.Background(), func() (interface{}, error) {
			return nil, errors.New("test error")
		})
	}
	assert.Equal(t, StateOpen, cb.GetState())

	// 等待超时，进入半开状态
	time.Sleep(150 * time.Millisecond)
	assert.Equal(t, StateHalfOpen, cb.GetState())

	// 半开状态下成功执行
	cb.Execute(context.Background(), func() (interface{}, error) {
		return "success", nil
	})

	// 再次成功执行，应该回到关闭状态
	cb.Execute(context.Background(), func() (interface{}, error) {
		return "success", nil
	})
	assert.Equal(t, StateClosed, cb.GetState())
}

func TestCircuitBreaker_HalfOpenMaxRequests(t *testing.T) {
	logger := logger.NewTestLogger()
	config := Config{
		Name:                "test-cb-half-open",
		MaxRequests:         2,
		Interval:            50 * time.Millisecond,
		Timeout:             100 * time.Millisecond,
		FailureThreshold:    2,
		SuccessThreshold:    1,
		MinRequestThreshold: 1,
		FailureRate:         0.5,
	}

	cb := NewCircuitBreaker(config, logger)

	// 让断路器熔断
	for i := 0; i < 3; i++ {
		cb.Execute(context.Background(), func() (interface{}, error) {
			return nil, errors.New("test error")
		})
	}
	assert.Equal(t, StateOpen, cb.GetState())

	// 等待进入半开状态
	time.Sleep(150 * time.Millisecond)
	assert.Equal(t, StateHalfOpen, cb.GetState())

	// 半开状态下执行最大请求数
	for i := 0; i < int(config.MaxRequests); i++ {
		_, err := cb.Execute(context.Background(), func() (interface{}, error) {
			return "success", nil
		})
		assert.NoError(t, err)
	}

	// 超过最大请求数应该被拒绝
	_, err := cb.Execute(context.Background(), func() (interface{}, error) {
		return "should not execute", nil
	})
	assert.Error(t, err)
	assert.Equal(t, ErrTooManyRequests, err)
}

func TestCircuitBreaker_Reset(t *testing.T) {
	logger := logger.NewTestLogger()
	config := Config{
		Name:                "test-cb-reset",
		MaxRequests:         1,
		Interval:            100 * time.Millisecond,
		Timeout:             200 * time.Millisecond,
		FailureThreshold:    2,
		SuccessThreshold:    1,
		MinRequestThreshold: 1,
		FailureRate:         0.5,
	}

	cb := NewCircuitBreaker(config, logger)

	// 让断路器熔断
	for i := 0; i < 3; i++ {
		cb.Execute(context.Background(), func() (interface{}, error) {
			return nil, errors.New("test error")
		})
	}
	assert.Equal(t, StateOpen, cb.GetState())

	// 重置断路器
	cb.Reset()
	assert.Equal(t, StateClosed, cb.GetState())

	// 重置后应该能正常执行
	result, err := cb.Execute(context.Background(), func() (interface{}, error) {
		return "success after reset", nil
	})
	require.NoError(t, err)
	assert.Equal(t, "success after reset", result)
}

func TestCircuitBreaker_ForceStates(t *testing.T) {
	logger := logger.NewTestLogger()
	config := Config{
		Name:                "test-cb-force",
		MaxRequests:         1,
		Interval:            100 * time.Millisecond,
		Timeout:             200 * time.Millisecond,
		FailureThreshold:    5,
		SuccessThreshold:    1,
		MinRequestThreshold: 1,
		FailureRate:         0.5,
	}

	cb := NewCircuitBreaker(config, logger)

	// 初始状态
	assert.Equal(t, StateClosed, cb.GetState())

	// 强制开启
	cb.ForceOpen()
	assert.Equal(t, StateOpen, cb.GetState())

	// 开启状态下执行应该失败
	_, err := cb.Execute(context.Background(), func() (interface{}, error) {
		return "should not execute", nil
	})
	assert.Error(t, err)
	assert.Equal(t, ErrOpenState, err)

	// 强制关闭
	cb.ForceClose()
	assert.Equal(t, StateClosed, cb.GetState())

	// 关闭状态下应该能正常执行
	result, err := cb.Execute(context.Background(), func() (interface{}, error) {
		return "success after force close", nil
	})
	require.NoError(t, err)
	assert.Equal(t, "success after force close", result)
}

func TestCircuitBreaker_Stats(t *testing.T) {
	logger := logger.NewTestLogger()
	config := Config{
		Name:                "test-cb-stats",
		MaxRequests:         1,
		Interval:            100 * time.Millisecond,
		Timeout:             200 * time.Millisecond,
		FailureThreshold:    3,
		SuccessThreshold:    1,
		MinRequestThreshold: 1,
		FailureRate:         0.5,
	}

	cb := NewCircuitBreaker(config, logger)

	// 执行一些成功和失败的请求
	cb.Execute(context.Background(), func() (interface{}, error) {
		return "success", nil
	})

	cb.Execute(context.Background(), func() (interface{}, error) {
		return nil, errors.New("error 1")
	})

	cb.Execute(context.Background(), func() (interface{}, error) {
		return "success", nil
	})

	cb.Execute(context.Background(), func() (interface{}, error) {
		return nil, errors.New("error 2")
	})

	// 获取统计信息
	stats := cb.GetStats()
	assert.Equal(t, "test-cb-stats", stats.Name)
	assert.Equal(t, StateClosed, stats.State)
	assert.Equal(t, uint32(4), stats.Counts.Requests)
	assert.Equal(t, uint32(2), stats.Counts.TotalSuccesses)
	assert.Equal(t, uint32(2), stats.Counts.TotalFailures)
	assert.Equal(t, uint32(1), stats.Counts.ConsecutiveSuccesses)
	assert.Equal(t, uint32(1), stats.Counts.ConsecutiveFailures)
}

func TestCircuitBreaker_SlowCalls(t *testing.T) {
	logger := logger.NewTestLogger()
	config := Config{
		Name:                "test-cb-slow",
		MaxRequests:         1,
		Interval:            100 * time.Millisecond,
		Timeout:             200 * time.Millisecond,
		FailureThreshold:    10, // 设置高一点，主要测试慢调用
		SuccessThreshold:    1,
		MinRequestThreshold: 2,
		SlowCallThreshold:   50 * time.Millisecond,
		SlowCallRate:        0.5,
	}

	cb := NewCircuitBreaker(config, logger)

	// 执行慢调用
	cb.Execute(context.Background(), func() (interface{}, error) {
		time.Sleep(60 * time.Millisecond) // 超过慢调用阈值
		return "slow success", nil
	})

	// 执行快调用
	cb.Execute(context.Background(), func() (interface{}, error) {
		return "fast success", nil
	})

	// 再执行一个慢调用，应该触发熔断
	cb.Execute(context.Background(), func() (interface{}, error) {
		time.Sleep(60 * time.Millisecond)
		return "slow success", nil
	})

	// 检查统计信息
	stats := cb.GetStats()
	assert.Equal(t, uint32(2), stats.Counts.SlowCalls) // 应该有2个慢调用

	// 由于慢调用率达到阈值，应该熔断
	assert.Equal(t, StateOpen, stats.State)
}

func TestCircuitBreaker_CustomConditions(t *testing.T) {
	logger := logger.NewTestLogger()
	
	// 自定义熔断条件：连续失败3次就熔断
	customReadyToTrip := func(counts Counts) bool {
		return counts.ConsecutiveFailures >= 3
	}

	// 自定义成功判断：只有特定错误才算失败
	customIsSuccessful := func(err error) bool {
		if err == nil {
			return true
		}
		// 只有 "critical error" 才算失败
		return err.Error() != "critical error"
	}

	config := Config{
		Name:                "test-cb-custom",
		MaxRequests:         1,
		Interval:            100 * time.Millisecond,
		Timeout:             200 * time.Millisecond,
		ReadyToTrip:         customReadyToTrip,
		IsSuccessful:        customIsSuccessful,
	}

	cb := NewCircuitBreaker(config, logger)

	// 执行非关键错误，不应该算失败
	cb.Execute(context.Background(), func() (interface{}, error) {
		return nil, errors.New("non-critical error")
	})
	assert.Equal(t, StateClosed, cb.GetState())

	// 执行关键错误
	for i := 0; i < 3; i++ {
		cb.Execute(context.Background(), func() (interface{}, error) {
			return nil, errors.New("critical error")
		})
	}

	// 应该熔断
	assert.Equal(t, StateOpen, cb.GetState())
}

func TestCircuitBreaker_ContextCancellation(t *testing.T) {
	logger := logger.NewTestLogger()
	config := Config{
		Name:                "test-cb-context",
		MaxRequests:         1,
		Interval:            100 * time.Millisecond,
		Timeout:             200 * time.Millisecond,
		FailureThreshold:    5,
		SuccessThreshold:    1,
		MinRequestThreshold: 1,
		FailureRate:         0.5,
	}

	cb := NewCircuitBreaker(config, logger)

	// 测试上下文取消
	ctx, cancel := context.WithCancel(context.Background())
	cancel() // 立即取消

	_, err := cb.Execute(ctx, func() (interface{}, error) {
		time.Sleep(100 * time.Millisecond)
		return "should not complete", nil
	})

	// 应该返回上下文取消错误
	assert.Error(t, err)
	// 注意：这里的错误可能是上下文错误，也可能是函数执行的错误，取决于实现
}

func TestCircuitBreaker_Panic(t *testing.T) {
	logger := logger.NewTestLogger()
	config := Config{
		Name:                "test-cb-panic",
		MaxRequests:         1,
		Interval:            100 * time.Millisecond,
		Timeout:             200 * time.Millisecond,
		FailureThreshold:    5,
		SuccessThreshold:    1,
		MinRequestThreshold: 1,
		FailureRate:         0.5,
	}

	cb := NewCircuitBreaker(config, logger)

	// 测试 panic 处理
	assert.Panics(t, func() {
		cb.Execute(context.Background(), func() (interface{}, error) {
			panic("test panic")
		})
	})

	// panic 后断路器应该仍然可用
	result, err := cb.Execute(context.Background(), func() (interface{}, error) {
		return "success after panic", nil
	})
	require.NoError(t, err)
	assert.Equal(t, "success after panic", result)
}
