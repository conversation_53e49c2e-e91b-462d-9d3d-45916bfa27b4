package circuitbreaker

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"paas-platform/pkg/logger"
)

// CircuitBreaker 断路器接口
type CircuitBreaker interface {
	Execute(ctx context.Context, fn func() (interface{}, error)) (interface{}, error)
	ExecuteWithFallback(ctx context.Context, fn func() (interface{}, error), fallback func() (interface{}, error)) (interface{}, error)
	GetState() State
	GetStats() *Stats
	Reset()
	ForceOpen()
	ForceClose()
}

// State 断路器状态
type State int32

const (
	StateClosed   State = iota // 关闭状态（正常）
	StateOpen                  // 开启状态（熔断）
	StateHalfOpen              // 半开状态（探测）
)

func (s State) String() string {
	switch s {
	case StateClosed:
		return "CLOSED"
	case StateOpen:
		return "OPEN"
	case StateHalfOpen:
		return "HALF_OPEN"
	default:
		return "UNKNOWN"
	}
}

// Config 断路器配置
type Config struct {
	Name                string        `json:"name"`                  // 断路器名称
	MaxRequests         uint32        `json:"max_requests"`          // 半开状态最大请求数
	Interval            time.Duration `json:"interval"`              // 统计时间窗口
	Timeout             time.Duration `json:"timeout"`               // 开启状态超时时间
	ReadyToTrip         ReadyToTrip   `json:"-"`                     // 熔断条件函数
	OnStateChange       OnStateChange `json:"-"`                     // 状态变化回调
	IsSuccessful        IsSuccessful  `json:"-"`                     // 成功判断函数
	ShouldTrip          bool          `json:"should_trip"`           // 是否应该熔断
	FailureThreshold    uint32        `json:"failure_threshold"`     // 失败阈值
	SuccessThreshold    uint32        `json:"success_threshold"`     // 成功阈值
	MinRequestThreshold uint32        `json:"min_request_threshold"` // 最小请求阈值
	FailureRate         float64       `json:"failure_rate"`          // 失败率阈值
	SlowCallThreshold   time.Duration `json:"slow_call_threshold"`   // 慢调用阈值
	SlowCallRate        float64       `json:"slow_call_rate"`        // 慢调用率阈值
}

// ReadyToTrip 熔断条件函数类型
type ReadyToTrip func(counts Counts) bool

// OnStateChange 状态变化回调函数类型
type OnStateChange func(name string, from State, to State)

// IsSuccessful 成功判断函数类型
type IsSuccessful func(err error) bool

// Counts 统计计数
type Counts struct {
	Requests             uint32        `json:"requests"`               // 总请求数
	TotalSuccesses       uint32        `json:"total_successes"`        // 总成功数
	TotalFailures        uint32        `json:"total_failures"`         // 总失败数
	ConsecutiveSuccesses uint32        `json:"consecutive_successes"`  // 连续成功数
	ConsecutiveFailures  uint32        `json:"consecutive_failures"`   // 连续失败数
	SlowCalls            uint32        `json:"slow_calls"`             // 慢调用数
	TotalDuration        time.Duration `json:"total_duration"`         // 总耗时
}

// Stats 断路器统计信息
type Stats struct {
	Name         string        `json:"name"`          // 断路器名称
	State        State         `json:"state"`         // 当前状态
	Counts       Counts        `json:"counts"`        // 统计计数
	StateChanged time.Time     `json:"state_changed"` // 状态变化时间
	Generation   uint64        `json:"generation"`    // 代数（重置次数）
	Expiry       time.Time     `json:"expiry"`        // 过期时间
	LastError    string        `json:"last_error"`    // 最后错误
	LastSuccess  time.Time     `json:"last_success"`  // 最后成功时间
	LastFailure  time.Time     `json:"last_failure"`  // 最后失败时间
}

// circuitBreaker 断路器实现
type circuitBreaker struct {
	name       string
	config     Config
	mutex      sync.RWMutex
	state      State
	generation uint64
	counts     Counts
	expiry     time.Time
	logger     logger.Logger
	lastError  error
}

// NewCircuitBreaker 创建断路器
func NewCircuitBreaker(config Config, logger logger.Logger) CircuitBreaker {
	cb := &circuitBreaker{
		name:   config.Name,
		config: config,
		state:  StateClosed,
		expiry: time.Now().Add(config.Interval),
		logger: logger,
	}

	// 设置默认值
	if cb.config.MaxRequests == 0 {
		cb.config.MaxRequests = 1
	}
	if cb.config.Interval == 0 {
		cb.config.Interval = 60 * time.Second
	}
	if cb.config.Timeout == 0 {
		cb.config.Timeout = 60 * time.Second
	}
	if cb.config.ReadyToTrip == nil {
		cb.config.ReadyToTrip = cb.defaultReadyToTrip
	}
	if cb.config.IsSuccessful == nil {
		cb.config.IsSuccessful = cb.defaultIsSuccessful
	}

	return cb
}

// Execute 执行函数
func (cb *circuitBreaker) Execute(ctx context.Context, fn func() (interface{}, error)) (interface{}, error) {
	return cb.ExecuteWithFallback(ctx, fn, nil)
}

// ExecuteWithFallback 执行函数（带降级）
func (cb *circuitBreaker) ExecuteWithFallback(ctx context.Context, fn func() (interface{}, error), fallback func() (interface{}, error)) (interface{}, error) {
	generation, err := cb.beforeRequest()
	if err != nil {
		if fallback != nil {
			cb.logger.Warn("断路器熔断，执行降级逻辑", "name", cb.name, "state", cb.state.String())
			return fallback()
		}
		return nil, err
	}

	defer func() {
		if r := recover(); r != nil {
			cb.afterRequest(generation, false, time.Duration(0), fmt.Errorf("panic: %v", r))
			panic(r)
		}
	}()

	start := time.Now()
	result, err := fn()
	duration := time.Since(start)

	cb.afterRequest(generation, cb.config.IsSuccessful(err), duration, err)

	return result, err
}

// GetState 获取当前状态
func (cb *circuitBreaker) GetState() State {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	now := time.Now()
	state, _ := cb.currentState(now)
	return state
}

// GetStats 获取统计信息
func (cb *circuitBreaker) GetStats() *Stats {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	now := time.Now()
	state, _ := cb.currentState(now)

	var lastError string
	if cb.lastError != nil {
		lastError = cb.lastError.Error()
	}

	var lastSuccess, lastFailure time.Time
	if cb.counts.TotalSuccesses > 0 {
		lastSuccess = time.Now() // 简化实现，实际应该记录具体时间
	}
	if cb.counts.TotalFailures > 0 {
		lastFailure = time.Now() // 简化实现，实际应该记录具体时间
	}

	return &Stats{
		Name:         cb.name,
		State:        state,
		Counts:       cb.counts,
		StateChanged: time.Now(), // 简化实现
		Generation:   cb.generation,
		Expiry:       cb.expiry,
		LastError:    lastError,
		LastSuccess:  lastSuccess,
		LastFailure:  lastFailure,
	}
}

// Reset 重置断路器
func (cb *circuitBreaker) Reset() {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	cb.state = StateClosed
	cb.toNewGeneration(time.Now())
}

// ForceOpen 强制开启
func (cb *circuitBreaker) ForceOpen() {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	cb.setState(StateOpen, time.Now())
}

// ForceClose 强制关闭
func (cb *circuitBreaker) ForceClose() {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	cb.setState(StateClosed, time.Now())
}

// beforeRequest 请求前检查
func (cb *circuitBreaker) beforeRequest() (uint64, error) {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	now := time.Now()
	state, generation := cb.currentState(now)

	if state == StateOpen {
		return generation, ErrOpenState
	} else if state == StateHalfOpen && cb.counts.Requests >= cb.config.MaxRequests {
		return generation, ErrTooManyRequests
	}

	cb.counts.onRequest()
	return generation, nil
}

// afterRequest 请求后处理
func (cb *circuitBreaker) afterRequest(before uint64, success bool, duration time.Duration, err error) {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	now := time.Now()
	state, generation := cb.currentState(now)
	if generation != before {
		return
	}

	if success {
		cb.onSuccess(state, now)
	} else {
		cb.onFailure(state, now, err)
	}

	// 检查慢调用
	if cb.config.SlowCallThreshold > 0 && duration > cb.config.SlowCallThreshold {
		cb.counts.onSlowCall()
	}

	cb.counts.TotalDuration += duration
}

// currentState 获取当前状态
func (cb *circuitBreaker) currentState(now time.Time) (State, uint64) {
	switch cb.state {
	case StateClosed:
		if !cb.expiry.IsZero() && cb.expiry.Before(now) {
			cb.toNewGeneration(now)
		}
	case StateOpen:
		if !cb.expiry.IsZero() && cb.expiry.Before(now) {
			cb.setState(StateHalfOpen, now)
		}
	}
	return cb.state, cb.generation
}

// onSuccess 成功处理
func (cb *circuitBreaker) onSuccess(state State, now time.Time) {
	cb.counts.onSuccess()

	if state == StateHalfOpen && cb.counts.ConsecutiveSuccesses >= cb.config.SuccessThreshold {
		cb.setState(StateClosed, now)
	}
}

// onFailure 失败处理
func (cb *circuitBreaker) onFailure(state State, now time.Time, err error) {
	cb.counts.onFailure()
	cb.lastError = err

	switch state {
	case StateClosed:
		if cb.config.ReadyToTrip(cb.counts) {
			cb.setState(StateOpen, now)
		}
	case StateHalfOpen:
		cb.setState(StateOpen, now)
	}
}

// setState 设置状态
func (cb *circuitBreaker) setState(state State, now time.Time) {
	if cb.state == state {
		return
	}

	prev := cb.state
	cb.state = state

	cb.toNewGeneration(now)

	if cb.config.OnStateChange != nil {
		cb.config.OnStateChange(cb.name, prev, state)
	}

	cb.logger.Info("断路器状态变化",
		"name", cb.name,
		"from", prev.String(),
		"to", state.String(),
		"counts", cb.counts)
}

// toNewGeneration 进入新代
func (cb *circuitBreaker) toNewGeneration(now time.Time) {
	cb.generation++
	cb.counts.clear()

	var zero time.Time
	switch cb.state {
	case StateClosed:
		if cb.config.Interval == 0 {
			cb.expiry = zero
		} else {
			cb.expiry = now.Add(cb.config.Interval)
		}
	case StateOpen:
		cb.expiry = now.Add(cb.config.Timeout)
	default: // StateHalfOpen
		cb.expiry = zero
	}
}

// defaultReadyToTrip 默认熔断条件
func (cb *circuitBreaker) defaultReadyToTrip(counts Counts) bool {
	// 请求数不足最小阈值
	if counts.Requests < cb.config.MinRequestThreshold {
		return false
	}

	// 检查失败率
	if cb.config.FailureRate > 0 {
		failureRate := float64(counts.TotalFailures) / float64(counts.Requests)
		if failureRate >= cb.config.FailureRate {
			return true
		}
	}

	// 检查慢调用率
	if cb.config.SlowCallRate > 0 && counts.Requests > 0 {
		slowCallRate := float64(counts.SlowCalls) / float64(counts.Requests)
		if slowCallRate >= cb.config.SlowCallRate {
			return true
		}
	}

	// 检查连续失败数
	if cb.config.FailureThreshold > 0 {
		return counts.ConsecutiveFailures >= cb.config.FailureThreshold
	}

	return false
}

// defaultIsSuccessful 默认成功判断
func (cb *circuitBreaker) defaultIsSuccessful(err error) bool {
	return err == nil
}

// onRequest 请求计数
func (c *Counts) onRequest() {
	c.Requests++
}

// onSuccess 成功计数
func (c *Counts) onSuccess() {
	c.TotalSuccesses++
	c.ConsecutiveSuccesses++
	c.ConsecutiveFailures = 0
}

// onFailure 失败计数
func (c *Counts) onFailure() {
	c.TotalFailures++
	c.ConsecutiveFailures++
	c.ConsecutiveSuccesses = 0
}

// onSlowCall 慢调用计数
func (c *Counts) onSlowCall() {
	c.SlowCalls++
}

// clear 清空计数
func (c *Counts) clear() {
	c.Requests = 0
	c.TotalSuccesses = 0
	c.TotalFailures = 0
	c.ConsecutiveSuccesses = 0
	c.ConsecutiveFailures = 0
	c.SlowCalls = 0
	c.TotalDuration = 0
}

// 错误定义
var (
	ErrOpenState        = errors.New("断路器处于开启状态")
	ErrTooManyRequests  = errors.New("半开状态请求过多")
	ErrTimeout          = errors.New("请求超时")
)
