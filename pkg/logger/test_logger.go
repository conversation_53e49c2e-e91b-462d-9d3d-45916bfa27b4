package logger

import (
	"fmt"
	"log"
	"os"
)

// TestLogger 测试用日志器
type TestLogger struct {
	verbose bool
	fields  []interface{}
}

// NewTestLogger 创建测试日志器
func NewTestLogger() Logger {
	return &TestLogger{
		verbose: os.Getenv("TEST_VERBOSE") == "true",
		fields:  make([]interface{}, 0),
	}
}

// Debug 调试日志
func (l *TestLogger) Debug(msg string, fields ...interface{}) {
	if l.verbose {
		allFields := append(l.fields, fields...)
		l.log("DEBUG", msg, allFields...)
	}
}

// Info 信息日志
func (l *TestLogger) Info(msg string, fields ...interface{}) {
	if l.verbose {
		allFields := append(l.fields, fields...)
		l.log("INFO", msg, allFields...)
	}
}

// Warn 警告日志
func (l *TestLogger) Warn(msg string, fields ...interface{}) {
	if l.verbose {
		allFields := append(l.fields, fields...)
		l.log("WARN", msg, allFields...)
	}
}

// Error 错误日志
func (l *TestLogger) Error(msg string, fields ...interface{}) {
	allFields := append(l.fields, fields...)
	l.log("ERROR", msg, allFields...)
}

// Fatal 致命错误日志
func (l *TestLogger) Fatal(msg string, fields ...interface{}) {
	allFields := append(l.fields, fields...)
	l.log("FATAL", msg, allFields...)
	os.Exit(1)
}

// With 添加字段
func (l *TestLogger) With(fields ...interface{}) Logger {
	newFields := make([]interface{}, len(l.fields)+len(fields))
	copy(newFields, l.fields)
	copy(newFields[len(l.fields):], fields)

	return &TestLogger{
		verbose: l.verbose,
		fields:  newFields,
	}
}

// log 内部日志方法
func (l *TestLogger) log(level, msg string, fields ...interface{}) {
	output := fmt.Sprintf("[%s] %s", level, msg)
	
	// 格式化字段
	if len(fields) > 0 {
		output += " |"
		for i := 0; i < len(fields); i += 2 {
			if i+1 < len(fields) {
				output += fmt.Sprintf(" %v=%v", fields[i], fields[i+1])
			}
		}
	}
	
	log.Println(output)
}
