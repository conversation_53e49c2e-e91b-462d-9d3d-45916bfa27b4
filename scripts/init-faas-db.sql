-- FaaS 服务数据库初始化脚本
-- 用于 PostgreSQL 数据库

-- 创建 FaaS 数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS paas_faas;

-- 使用 FaaS 数据库
\c paas_faas;

-- 创建函数定义表
CREATE TABLE IF NOT EXISTS faas_functions (
    id SERIAL PRIMARY KEY,
    function_id VARCHAR(255) NOT NULL UNIQUE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    runtime VARCHAR(50) NOT NULL,
    handler VARCHAR(255),
    code TEXT NOT NULL,
    version VARCHAR(50) DEFAULT '1.0.0',
    status VARCHAR(20) DEFAULT 'active',
    
    -- 配置信息
    timeout INTEGER DEFAULT 30,
    memory_limit VARCHAR(20),
    cpu_limit VARCHAR(20),
    
    -- 环境变量和标签 (JSON 格式)
    environment TEXT,
    tags TEXT,
    
    -- 元数据
    created_by <PERSON><PERSON><PERSON><PERSON>(255),
    updated_by VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);

-- 创建函数执行记录表
CREATE TABLE IF NOT EXISTS faas_function_executions (
    id SERIAL PRIMARY KEY,
    request_id VARCHAR(255) NOT NULL UNIQUE,
    function_id VARCHAR(255) NOT NULL,
    
    -- 执行信息
    status VARCHAR(20) NOT NULL,
    runtime VARCHAR(50) NOT NULL,
    container_id VARCHAR(255),
    
    -- 执行参数和结果 (JSON 格式)
    parameters TEXT,
    result TEXT,
    error TEXT,
    logs TEXT,
    
    -- 性能指标
    execution_time BIGINT DEFAULT 0,
    memory_usage BIGINT DEFAULT 0,
    cpu_usage DECIMAL(5,2) DEFAULT 0.0,
    
    -- 时间戳
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束
    FOREIGN KEY (function_id) REFERENCES faas_functions(function_id) ON DELETE CASCADE
);

-- 创建函数版本表
CREATE TABLE IF NOT EXISTS faas_function_versions (
    id SERIAL PRIMARY KEY,
    function_id VARCHAR(255) NOT NULL,
    version VARCHAR(50) NOT NULL,
    code TEXT NOT NULL,
    handler VARCHAR(255),
    
    -- 配置信息
    timeout INTEGER,
    memory_limit VARCHAR(20),
    cpu_limit VARCHAR(20),
    environment TEXT,
    
    -- 版本状态
    status VARCHAR(20) DEFAULT 'active',
    is_default BOOLEAN DEFAULT FALSE,
    
    -- 变更信息
    change_log TEXT,
    created_by VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束
    FOREIGN KEY (function_id) REFERENCES faas_functions(function_id) ON DELETE CASCADE,
    
    -- 唯一约束
    UNIQUE(function_id, version)
);

-- 创建函数指标表
CREATE TABLE IF NOT EXISTS faas_function_metrics (
    id SERIAL PRIMARY KEY,
    function_id VARCHAR(255) NOT NULL,
    date DATE NOT NULL,
    
    -- 执行统计
    total_executions BIGINT DEFAULT 0,
    successful_executions BIGINT DEFAULT 0,
    failed_executions BIGINT DEFAULT 0,
    timeout_executions BIGINT DEFAULT 0,
    
    -- 性能统计
    avg_execution_time BIGINT DEFAULT 0,
    max_execution_time BIGINT DEFAULT 0,
    min_execution_time BIGINT DEFAULT 0,
    avg_memory_usage BIGINT DEFAULT 0,
    max_memory_usage BIGINT DEFAULT 0,
    avg_cpu_usage DECIMAL(5,2) DEFAULT 0.0,
    
    -- 错误统计
    error_rate DECIMAL(5,2) DEFAULT 0.0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束
    FOREIGN KEY (function_id) REFERENCES faas_functions(function_id) ON DELETE CASCADE,
    
    -- 唯一约束
    UNIQUE(function_id, date)
);

-- 创建函数触发器表
CREATE TABLE IF NOT EXISTS faas_function_triggers (
    id SERIAL PRIMARY KEY,
    trigger_id VARCHAR(255) NOT NULL UNIQUE,
    function_id VARCHAR(255) NOT NULL,
    
    -- 触发器信息
    name VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    
    -- 触发器配置 (JSON 格式)
    config TEXT,
    
    -- 统计信息
    trigger_count BIGINT DEFAULT 0,
    last_triggered TIMESTAMP,
    
    created_by VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    
    -- 外键约束
    FOREIGN KEY (function_id) REFERENCES faas_functions(function_id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_faas_functions_function_id ON faas_functions(function_id);
CREATE INDEX IF NOT EXISTS idx_faas_functions_status ON faas_functions(status);
CREATE INDEX IF NOT EXISTS idx_faas_functions_runtime ON faas_functions(runtime);
CREATE INDEX IF NOT EXISTS idx_faas_functions_created_at ON faas_functions(created_at);
CREATE INDEX IF NOT EXISTS idx_faas_functions_deleted_at ON faas_functions(deleted_at);

CREATE INDEX IF NOT EXISTS idx_faas_executions_request_id ON faas_function_executions(request_id);
CREATE INDEX IF NOT EXISTS idx_faas_executions_function_id ON faas_function_executions(function_id);
CREATE INDEX IF NOT EXISTS idx_faas_executions_status ON faas_function_executions(status);
CREATE INDEX IF NOT EXISTS idx_faas_executions_created_at ON faas_function_executions(created_at);
CREATE INDEX IF NOT EXISTS idx_faas_executions_start_time ON faas_function_executions(start_time);

CREATE INDEX IF NOT EXISTS idx_faas_versions_function_id ON faas_function_versions(function_id);
CREATE INDEX IF NOT EXISTS idx_faas_versions_version ON faas_function_versions(version);
CREATE INDEX IF NOT EXISTS idx_faas_versions_is_default ON faas_function_versions(is_default);

CREATE INDEX IF NOT EXISTS idx_faas_metrics_function_id ON faas_function_metrics(function_id);
CREATE INDEX IF NOT EXISTS idx_faas_metrics_date ON faas_function_metrics(date);

CREATE INDEX IF NOT EXISTS idx_faas_triggers_trigger_id ON faas_function_triggers(trigger_id);
CREATE INDEX IF NOT EXISTS idx_faas_triggers_function_id ON faas_function_triggers(function_id);
CREATE INDEX IF NOT EXISTS idx_faas_triggers_type ON faas_function_triggers(type);
CREATE INDEX IF NOT EXISTS idx_faas_triggers_status ON faas_function_triggers(status);
CREATE INDEX IF NOT EXISTS idx_faas_triggers_deleted_at ON faas_function_triggers(deleted_at);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表创建更新时间触发器
CREATE TRIGGER update_faas_functions_updated_at 
    BEFORE UPDATE ON faas_functions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_faas_executions_updated_at 
    BEFORE UPDATE ON faas_function_executions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_faas_metrics_updated_at 
    BEFORE UPDATE ON faas_function_metrics 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_faas_triggers_updated_at 
    BEFORE UPDATE ON faas_function_triggers 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入示例数据（可选）
INSERT INTO faas_functions (function_id, name, description, runtime, handler, code, created_by) VALUES
('hello-world-js', 'Hello World (JavaScript)', 'A simple hello world function in JavaScript', 'nodejs', 'main', 
'function main(params) {
    const name = params.name || "World";
    return {
        message: `Hello, ${name}!`,
        timestamp: new Date().toISOString()
    };
}', 'system'),

('hello-world-py', 'Hello World (Python)', 'A simple hello world function in Python', 'python', 'main',
'def main(params):
    name = params.get("name", "World")
    import datetime
    return {
        "message": f"Hello, {name}!",
        "timestamp": datetime.datetime.now().isoformat()
    }', 'system'),

('fibonacci-go', 'Fibonacci (Go)', 'Calculate Fibonacci number in Go', 'go', 'main',
'func main(params map[string]interface{}) (interface{}, error) {
    n, ok := params["n"].(float64)
    if !ok {
        return nil, fmt.Errorf("parameter n is required and must be a number")
    }
    
    result := fibonacci(int(n))
    return map[string]interface{}{
        "input": int(n),
        "result": result,
    }, nil
}

func fibonacci(n int) int {
    if n <= 1 {
        return n
    }
    return fibonacci(n-1) + fibonacci(n-2)
}', 'system');

-- 提交事务
COMMIT;

-- 显示创建的表
\dt faas_*;
