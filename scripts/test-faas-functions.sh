#!/bin/bash

# FaaS 功能验证脚本
# 用于测试 Function as a Service 执行器的各种功能

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
SERVICE_URL="http://localhost:8087"
TIMEOUT=60
VERBOSE=false

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    if [[ "$VERBOSE" == "true" ]]; then
        echo -e "${BLUE}[DEBUG]${NC} $1"
    fi
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
FaaS 功能验证脚本

用法: $0 [选项]

选项:
    -h, --help          显示此帮助信息
    -u, --url URL       指定服务 URL (默认: $SERVICE_URL)
    -t, --timeout SEC   设置超时时间 (默认: $TIMEOUT 秒)
    -v, --verbose       详细输出
    --nodejs            仅测试 Node.js 运行时
    --python            仅测试 Python 运行时
    --go                仅测试 Go 运行时
    --java              仅测试 Java 运行时
    --async             测试异步执行
    --error             测试错误处理
    --all               测试所有功能（默认）

示例:
    $0                  # 测试所有功能
    $0 --nodejs         # 仅测试 Node.js
    $0 --async          # 测试异步执行
    $0 -v --all         # 详细输出测试所有功能

EOF
}

# 执行函数测试
execute_function() {
    local test_name="$1"
    local request_data="$2"
    local expected_status="$3"
    local async_mode="$4"
    
    log_info "执行测试: $test_name"
    
    local endpoint="/api/v1/faas/functions/execute"
    if [[ "$async_mode" == "true" ]]; then
        endpoint="/api/v1/faas/functions/async-execute"
    fi
    
    log_debug "请求数据: $request_data"
    
    # 发送请求
    local response
    response=$(curl -s --max-time "$TIMEOUT" \
        -H "Content-Type: application/json" \
        -d "$request_data" \
        "$SERVICE_URL$endpoint" 2>/dev/null)
    
    if [[ $? -ne 0 ]]; then
        log_error "请求失败: $test_name"
        return 1
    fi
    
    log_debug "响应数据: $response"
    
    # 解析响应
    local status
    if [[ "$async_mode" == "true" ]]; then
        status=$(echo "$response" | jq -r '.status' 2>/dev/null)
        expected_status="accepted"
    else
        status=$(echo "$response" | jq -r '.status' 2>/dev/null)
    fi
    
    if [[ "$status" == "$expected_status" ]]; then
        log_success "测试通过: $test_name"
        
        if [[ "$VERBOSE" == "true" && "$async_mode" != "true" ]]; then
            local result execution_time
            result=$(echo "$response" | jq -r '.result' 2>/dev/null)
            execution_time=$(echo "$response" | jq -r '.execution_time' 2>/dev/null)
            log_info "执行结果: $result"
            log_info "执行时间: $execution_time"
        fi
        
        return 0
    else
        log_error "测试失败: $test_name (期望状态: $expected_status, 实际状态: $status)"
        
        if [[ "$VERBOSE" == "true" ]]; then
            local error_msg
            error_msg=$(echo "$response" | jq -r '.error // .message' 2>/dev/null)
            log_error "错误信息: $error_msg"
        fi
        
        return 1
    fi
}

# 测试 Node.js 函数
test_nodejs_functions() {
    log_info "测试 Node.js 运行时..."
    
    local tests_passed=0
    local total_tests=3
    
    # 测试 1: 简单 Hello World
    local hello_request
    hello_request=$(cat << 'EOF'
{
    "function_id": "test-nodejs-hello",
    "function_name": "Node.js Hello World",
    "runtime": "nodejs",
    "handler": "main",
    "code": "function main(params) { const name = params.name || 'World'; return { message: `Hello, ${name}!`, runtime: 'nodejs', timestamp: new Date().toISOString() }; }",
    "parameters": {
        "name": "FaaS"
    },
    "timeout": "30s"
}
EOF
    )
    
    if execute_function "Node.js Hello World" "$hello_request" "success"; then
        ((tests_passed++))
    fi
    
    # 测试 2: 数学计算
    local math_request
    math_request=$(cat << 'EOF'
{
    "function_id": "test-nodejs-math",
    "function_name": "Node.js Math Function",
    "runtime": "nodejs",
    "handler": "main",
    "code": "function main(params) { const { a, b } = params; return { sum: a + b, product: a * b, difference: a - b, quotient: a / b, runtime: 'nodejs' }; }",
    "parameters": {
        "a": 10,
        "b": 5
    },
    "timeout": "30s"
}
EOF
    )
    
    if execute_function "Node.js Math Function" "$math_request" "success"; then
        ((tests_passed++))
    fi
    
    # 测试 3: 异步操作
    local async_request
    async_request=$(cat << 'EOF'
{
    "function_id": "test-nodejs-async",
    "function_name": "Node.js Async Function",
    "runtime": "nodejs",
    "handler": "main",
    "code": "function main(params) { return new Promise((resolve) => { setTimeout(() => { resolve({ message: 'Async operation completed', delay: params.delay || 1000, runtime: 'nodejs' }); }, params.delay || 1000); }); }",
    "parameters": {
        "delay": 1000
    },
    "timeout": "30s"
}
EOF
    )
    
    if execute_function "Node.js Async Function" "$async_request" "success"; then
        ((tests_passed++))
    fi
    
    log_info "Node.js 测试结果: $tests_passed/$total_tests"
    return $((total_tests - tests_passed))
}

# 测试 Python 函数
test_python_functions() {
    log_info "测试 Python 运行时..."
    
    local tests_passed=0
    local total_tests=3
    
    # 测试 1: 简单 Hello World
    local hello_request
    hello_request=$(cat << 'EOF'
{
    "function_id": "test-python-hello",
    "function_name": "Python Hello World",
    "runtime": "python",
    "handler": "main",
    "code": "def main(params):\n    import datetime\n    name = params.get('name', 'World')\n    return {\n        'message': f'Hello, {name}!',\n        'runtime': 'python',\n        'timestamp': datetime.datetime.now().isoformat()\n    }",
    "parameters": {
        "name": "FaaS"
    },
    "timeout": "30s"
}
EOF
    )
    
    if execute_function "Python Hello World" "$hello_request" "success"; then
        ((tests_passed++))
    fi
    
    # 测试 2: 数据处理
    local data_request
    data_request=$(cat << 'EOF'
{
    "function_id": "test-python-data",
    "function_name": "Python Data Processing",
    "runtime": "python",
    "handler": "main",
    "code": "def main(params):\n    import json\n    data = params.get('data', [])\n    return {\n        'count': len(data),\n        'sum': sum(data) if all(isinstance(x, (int, float)) for x in data) else 0,\n        'avg': sum(data) / len(data) if data and all(isinstance(x, (int, float)) for x in data) else 0,\n        'runtime': 'python'\n    }",
    "parameters": {
        "data": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    },
    "timeout": "30s"
}
EOF
    )
    
    if execute_function "Python Data Processing" "$data_request" "success"; then
        ((tests_passed++))
    fi
    
    # 测试 3: 字符串处理
    local string_request
    string_request=$(cat << 'EOF'
{
    "function_id": "test-python-string",
    "function_name": "Python String Processing",
    "runtime": "python",
    "handler": "main",
    "code": "def main(params):\n    import re\n    text = params.get('text', '')\n    return {\n        'length': len(text),\n        'words': len(text.split()),\n        'uppercase': text.upper(),\n        'lowercase': text.lower(),\n        'reversed': text[::-1],\n        'runtime': 'python'\n    }",
    "parameters": {
        "text": "Hello FaaS World"
    },
    "timeout": "30s"
}
EOF
    )
    
    if execute_function "Python String Processing" "$string_request" "success"; then
        ((tests_passed++))
    fi
    
    log_info "Python 测试结果: $tests_passed/$total_tests"
    return $((total_tests - tests_passed))
}

# 测试异步执行
test_async_execution() {
    log_info "测试异步执行功能..."
    
    local async_request
    async_request=$(cat << 'EOF'
{
    "function_id": "test-async-execution",
    "function_name": "Async Execution Test",
    "runtime": "nodejs",
    "handler": "main",
    "code": "function main(params) { const delay = params.delay || 2000; const start = Date.now(); while (Date.now() - start < delay) {} return { message: 'Long running task completed', delay: delay, timestamp: new Date().toISOString() }; }",
    "parameters": {
        "delay": 3000
    },
    "timeout": "30s"
}
EOF
    )
    
    if execute_function "Async Execution Test" "$async_request" "accepted" "true"; then
        log_success "异步执行测试通过"
        return 0
    else
        log_error "异步执行测试失败"
        return 1
    fi
}

# 测试错误处理
test_error_handling() {
    log_info "测试错误处理功能..."
    
    local tests_passed=0
    local total_tests=2
    
    # 测试 1: 运行时错误
    local runtime_error_request
    runtime_error_request=$(cat << 'EOF'
{
    "function_id": "test-runtime-error",
    "function_name": "Runtime Error Test",
    "runtime": "nodejs",
    "handler": "main",
    "code": "function main(params) { throw new Error('This is a test runtime error'); }",
    "parameters": {},
    "timeout": "30s"
}
EOF
    )
    
    # 对于错误测试，我们期望得到错误响应
    local response
    response=$(curl -s --max-time "$TIMEOUT" \
        -H "Content-Type: application/json" \
        -d "$runtime_error_request" \
        "$SERVICE_URL/api/v1/faas/functions/execute" 2>/dev/null)
    
    local status
    status=$(echo "$response" | jq -r '.status' 2>/dev/null)
    
    if [[ "$status" == "error" ]] || [[ "$status" == "failed" ]]; then
        log_success "运行时错误处理测试通过"
        ((tests_passed++))
    else
        log_error "运行时错误处理测试失败"
    fi
    
    # 测试 2: 无效请求
    local invalid_request='{"invalid": "request"}'
    
    response=$(curl -s --max-time "$TIMEOUT" \
        -H "Content-Type: application/json" \
        -d "$invalid_request" \
        "$SERVICE_URL/api/v1/faas/functions/execute" 2>/dev/null)
    
    # 检查 HTTP 状态码
    local http_status
    http_status=$(curl -s -o /dev/null -w "%{http_code}" --max-time "$TIMEOUT" \
        -H "Content-Type: application/json" \
        -d "$invalid_request" \
        "$SERVICE_URL/api/v1/faas/functions/execute" 2>/dev/null)
    
    if [[ "$http_status" == "400" ]]; then
        log_success "无效请求处理测试通过"
        ((tests_passed++))
    else
        log_error "无效请求处理测试失败 (HTTP状态码: $http_status)"
    fi
    
    log_info "错误处理测试结果: $tests_passed/$total_tests"
    return $((total_tests - tests_passed))
}

# 执行所有测试
run_all_tests() {
    log_info "执行所有功能测试..."
    
    local total_failed=0
    
    # 测试各运行时
    test_nodejs_functions
    total_failed=$((total_failed + $?))
    
    test_python_functions
    total_failed=$((total_failed + $?))
    
    # 测试异步执行
    test_async_execution
    total_failed=$((total_failed + $?))
    
    # 测试错误处理
    test_error_handling
    total_failed=$((total_failed + $?))
    
    echo ""
    if [[ $total_failed -eq 0 ]]; then
        log_success "所有功能测试通过"
        return 0
    else
        log_error "部分功能测试失败 (失败数: $total_failed)"
        return 1
    fi
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -u|--url)
                SERVICE_URL="$2"
                shift 2
                ;;
            -t|--timeout)
                TIMEOUT="$2"
                shift 2
                ;;
            -v|--verbose)
                VERBOSE="true"
                shift
                ;;
            --nodejs)
                TEST_TYPE="nodejs"
                shift
                ;;
            --python)
                TEST_TYPE="python"
                shift
                ;;
            --go)
                TEST_TYPE="go"
                shift
                ;;
            --java)
                TEST_TYPE="java"
                shift
                ;;
            --async)
                TEST_TYPE="async"
                shift
                ;;
            --error)
                TEST_TYPE="error"
                shift
                ;;
            --all)
                TEST_TYPE="all"
                shift
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 主函数
main() {
    # 默认值
    TEST_TYPE="all"
    
    # 解析参数
    parse_args "$@"
    
    # 检查依赖
    if ! command -v curl &> /dev/null; then
        log_error "curl 未安装或不在 PATH 中"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_warn "jq 未安装，JSON 解析功能受限"
    fi
    
    # 显示测试信息
    log_info "FaaS 功能验证测试"
    log_info "服务 URL: $SERVICE_URL"
    log_info "超时时间: $TIMEOUT 秒"
    log_info "测试类型: $TEST_TYPE"
    echo ""
    
    # 执行测试
    case $TEST_TYPE in
        nodejs)
            test_nodejs_functions
            exit $?
            ;;
        python)
            test_python_functions
            exit $?
            ;;
        async)
            test_async_execution
            exit $?
            ;;
        error)
            test_error_handling
            exit $?
            ;;
        all)
            run_all_tests
            exit $?
            ;;
        *)
            log_error "未知测试类型: $TEST_TYPE"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
