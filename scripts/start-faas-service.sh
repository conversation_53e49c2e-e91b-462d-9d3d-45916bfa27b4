#!/bin/bash

# FaaS 服务启动脚本
# 用于启动 Function as a Service 执行器服务

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
SERVICE_NAME="faas-service"
CONFIG_FILE="${PROJECT_ROOT}/configs/faas-service.yaml"
DEV_CONFIG_FILE="${PROJECT_ROOT}/configs/faas-service.dev.yaml"
LOG_FILE="${PROJECT_ROOT}/logs/faas-service.log"
PID_FILE="${PROJECT_ROOT}/tmp/faas-service.pid"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
FaaS 服务启动脚本

用法: $0 [选项]

选项:
    -h, --help          显示此帮助信息
    -d, --dev           使用开发环境配置
    -c, --config FILE   指定配置文件路径
    -b, --build         构建服务后再启动
    -f, --foreground    前台运行（不后台化）
    --docker            使用 Docker 启动
    --stop              停止服务
    --restart           重启服务
    --status            查看服务状态
    --logs              查看服务日志

示例:
    $0                  # 使用默认配置启动服务
    $0 -d               # 使用开发环境配置启动
    $0 -c custom.yaml   # 使用自定义配置文件
    $0 --docker         # 使用 Docker 启动
    $0 --stop           # 停止服务
    $0 --status         # 查看服务状态

EOF
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查 Go
    if ! command -v go &> /dev/null; then
        log_error "Go 未安装或不在 PATH 中"
        exit 1
    fi
    
    # 检查 Docker（如果使用 Docker 模式）
    if [[ "$USE_DOCKER" == "true" ]]; then
        if ! command -v docker &> /dev/null; then
            log_error "Docker 未安装或不在 PATH 中"
            exit 1
        fi
        
        if ! docker info &> /dev/null; then
            log_error "Docker 守护进程未运行"
            exit 1
        fi
    fi
    
    log_info "依赖检查完成"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p "${PROJECT_ROOT}/logs"
    mkdir -p "${PROJECT_ROOT}/tmp"
    mkdir -p "${PROJECT_ROOT}/data/faas/workspaces"
    mkdir -p "${PROJECT_ROOT}/data/faas/artifacts"
    
    log_info "目录创建完成"
}

# 构建服务
build_service() {
    log_info "构建 FaaS 服务..."
    
    cd "$PROJECT_ROOT"
    
    # 设置构建变量
    VERSION=$(git describe --tags --always --dirty 2>/dev/null || echo "dev")
    BUILD_TIME=$(date -u '+%Y-%m-%d_%H:%M:%S')
    GIT_COMMIT=$(git rev-parse HEAD 2>/dev/null || echo "unknown")
    
    # 构建二进制文件
    go build \
        -ldflags="-s -w -X main.version=${VERSION} -X main.buildTime=${BUILD_TIME} -X main.gitCommit=${GIT_COMMIT}" \
        -o "${PROJECT_ROOT}/bin/${SERVICE_NAME}" \
        "${PROJECT_ROOT}/cmd/${SERVICE_NAME}"
    
    if [[ $? -eq 0 ]]; then
        log_info "FaaS 服务构建成功"
    else
        log_error "FaaS 服务构建失败"
        exit 1
    fi
}

# 构建 Docker 镜像
build_docker_image() {
    log_info "构建 Docker 镜像..."
    
    cd "$PROJECT_ROOT"
    
    # 设置构建参数
    VERSION=$(git describe --tags --always --dirty 2>/dev/null || echo "dev")
    BUILD_TIME=$(date -u '+%Y-%m-%d_%H:%M:%S')
    GIT_COMMIT=$(git rev-parse HEAD 2>/dev/null || echo "unknown")
    
    # 构建镜像
    docker build \
        --build-arg VERSION="$VERSION" \
        --build-arg BUILD_TIME="$BUILD_TIME" \
        --build-arg GIT_COMMIT="$GIT_COMMIT" \
        -f docker/Dockerfile.faas-executor \
        -t "paas-platform/faas-executor:$VERSION" \
        -t "paas-platform/faas-executor:latest" \
        .
    
    if [[ $? -eq 0 ]]; then
        log_info "Docker 镜像构建成功"
    else
        log_error "Docker 镜像构建失败"
        exit 1
    fi
}

# 启动服务
start_service() {
    log_info "启动 FaaS 服务..."
    
    # 检查服务是否已经运行
    if [[ -f "$PID_FILE" ]]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            log_warn "FaaS 服务已经在运行 (PID: $pid)"
            return 0
        else
            log_warn "发现过期的 PID 文件，正在清理..."
            rm -f "$PID_FILE"
        fi
    fi
    
    # 确定配置文件
    local config_arg=""
    if [[ -n "$CONFIG_FILE_PATH" ]]; then
        config_arg="--config=$CONFIG_FILE_PATH"
    elif [[ "$USE_DEV_CONFIG" == "true" ]]; then
        config_arg="--config=$DEV_CONFIG_FILE"
    elif [[ -f "$CONFIG_FILE" ]]; then
        config_arg="--config=$CONFIG_FILE"
    fi
    
    # 启动服务
    if [[ "$USE_DOCKER" == "true" ]]; then
        start_docker_service
    else
        start_binary_service "$config_arg"
    fi
}

# 启动二进制服务
start_binary_service() {
    local config_arg="$1"
    
    cd "$PROJECT_ROOT"
    
    if [[ "$FOREGROUND" == "true" ]]; then
        # 前台运行
        log_info "在前台启动 FaaS 服务..."
        exec "./bin/${SERVICE_NAME}" $config_arg
    else
        # 后台运行
        log_info "在后台启动 FaaS 服务..."
        nohup "./bin/${SERVICE_NAME}" $config_arg > "$LOG_FILE" 2>&1 &
        local pid=$!
        echo $pid > "$PID_FILE"
        
        # 等待服务启动
        sleep 2
        if kill -0 "$pid" 2>/dev/null; then
            log_info "FaaS 服务启动成功 (PID: $pid)"
            log_info "日志文件: $LOG_FILE"
            log_info "配置文件: ${CONFIG_FILE_PATH:-$CONFIG_FILE}"
        else
            log_error "FaaS 服务启动失败"
            rm -f "$PID_FILE"
            exit 1
        fi
    fi
}

# 启动 Docker 服务
start_docker_service() {
    log_info "使用 Docker 启动 FaaS 服务..."
    
    # 停止现有容器
    docker stop faas-executor 2>/dev/null || true
    docker rm faas-executor 2>/dev/null || true
    
    # 启动新容器
    docker run -d \
        --name faas-executor \
        --restart unless-stopped \
        -p 8087:8087 \
        -v /var/run/docker.sock:/var/run/docker.sock \
        -v "${PROJECT_ROOT}/configs:/app/configs:ro" \
        -v "${PROJECT_ROOT}/data:/app/data" \
        -v "${PROJECT_ROOT}/logs:/app/logs" \
        -e FAAS_SERVICE_CONFIG_FILE="/app/configs/faas-service.yaml" \
        paas-platform/faas-executor:latest
    
    if [[ $? -eq 0 ]]; then
        log_info "FaaS 服务 Docker 容器启动成功"
        log_info "容器名称: faas-executor"
        log_info "端口映射: 8087:8087"
    else
        log_error "FaaS 服务 Docker 容器启动失败"
        exit 1
    fi
}

# 停止服务
stop_service() {
    log_info "停止 FaaS 服务..."
    
    if [[ "$USE_DOCKER" == "true" ]]; then
        docker stop faas-executor 2>/dev/null || true
        docker rm faas-executor 2>/dev/null || true
        log_info "FaaS 服务 Docker 容器已停止"
    else
        if [[ -f "$PID_FILE" ]]; then
            local pid=$(cat "$PID_FILE")
            if kill -0 "$pid" 2>/dev/null; then
                kill "$pid"
                sleep 2
                if kill -0 "$pid" 2>/dev/null; then
                    log_warn "服务未响应 SIGTERM，发送 SIGKILL..."
                    kill -9 "$pid"
                fi
                log_info "FaaS 服务已停止 (PID: $pid)"
            else
                log_warn "PID 文件存在但进程不存在"
            fi
            rm -f "$PID_FILE"
        else
            log_warn "未找到 PID 文件，服务可能未运行"
        fi
    fi
}

# 重启服务
restart_service() {
    log_info "重启 FaaS 服务..."
    stop_service
    sleep 1
    start_service
}

# 查看服务状态
show_status() {
    log_info "检查 FaaS 服务状态..."
    
    if [[ "$USE_DOCKER" == "true" ]]; then
        if docker ps | grep -q faas-executor; then
            log_info "FaaS 服务 Docker 容器正在运行"
            docker ps | grep faas-executor
        else
            log_warn "FaaS 服务 Docker 容器未运行"
        fi
    else
        if [[ -f "$PID_FILE" ]]; then
            local pid=$(cat "$PID_FILE")
            if kill -0 "$pid" 2>/dev/null; then
                log_info "FaaS 服务正在运行 (PID: $pid)"
                
                # 显示进程信息
                ps -p "$pid" -o pid,ppid,cmd,etime,pcpu,pmem
                
                # 检查端口监听
                if command -v netstat &> /dev/null; then
                    log_info "端口监听状态:"
                    netstat -tlnp | grep ":8087" || log_warn "端口 8087 未监听"
                fi
            else
                log_warn "PID 文件存在但进程不存在"
                rm -f "$PID_FILE"
            fi
        else
            log_warn "FaaS 服务未运行（未找到 PID 文件）"
        fi
    fi
    
    # 检查服务健康状态
    log_info "检查服务健康状态..."
    if curl -s -f http://localhost:8087/health > /dev/null; then
        log_info "FaaS 服务健康检查通过"
    else
        log_warn "FaaS 服务健康检查失败"
    fi
}

# 查看日志
show_logs() {
    if [[ "$USE_DOCKER" == "true" ]]; then
        log_info "显示 Docker 容器日志..."
        docker logs -f faas-executor
    else
        if [[ -f "$LOG_FILE" ]]; then
            log_info "显示服务日志: $LOG_FILE"
            tail -f "$LOG_FILE"
        else
            log_warn "日志文件不存在: $LOG_FILE"
        fi
    fi
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -d|--dev)
                USE_DEV_CONFIG="true"
                shift
                ;;
            -c|--config)
                CONFIG_FILE_PATH="$2"
                shift 2
                ;;
            -b|--build)
                BUILD_SERVICE="true"
                shift
                ;;
            -f|--foreground)
                FOREGROUND="true"
                shift
                ;;
            --docker)
                USE_DOCKER="true"
                shift
                ;;
            --stop)
                ACTION="stop"
                shift
                ;;
            --restart)
                ACTION="restart"
                shift
                ;;
            --status)
                ACTION="status"
                shift
                ;;
            --logs)
                ACTION="logs"
                shift
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 主函数
main() {
    # 默认值
    USE_DEV_CONFIG="false"
    BUILD_SERVICE="false"
    FOREGROUND="false"
    USE_DOCKER="false"
    ACTION="start"
    CONFIG_FILE_PATH=""
    
    # 解析参数
    parse_args "$@"
    
    # 显示启动信息
    log_info "FaaS 服务管理脚本"
    log_info "项目根目录: $PROJECT_ROOT"
    log_info "操作: $ACTION"
    
    # 执行操作
    case $ACTION in
        start)
            check_dependencies
            create_directories
            
            if [[ "$BUILD_SERVICE" == "true" ]]; then
                if [[ "$USE_DOCKER" == "true" ]]; then
                    build_docker_image
                else
                    build_service
                fi
            fi
            
            start_service
            ;;
        stop)
            stop_service
            ;;
        restart)
            restart_service
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs
            ;;
        *)
            log_error "未知操作: $ACTION"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
