#!/bin/bash

# PaaS 平台 IDP 优先认证部署脚本
# 此脚本用于部署和配置 IDP 优先认证功能
# 普通用户仅支持 IDP 认证，管理员保留本地认证

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查部署依赖..."
    
    # 检查 MySQL 客户端
    if ! command -v mysql &> /dev/null; then
        log_error "MySQL 客户端未安装，请先安装 MySQL 客户端"
        exit 1
    fi
    
    # 检查 Go 环境
    if ! command -v go &> /dev/null; then
        log_error "Go 环境未安装，请先安装 Go 1.21+"
        exit 1
    fi
    
    # 检查 Docker（可选）
    if command -v docker &> /dev/null; then
        log_info "检测到 Docker 环境"
        DOCKER_AVAILABLE=true
    else
        log_warning "未检测到 Docker 环境，将使用本地部署"
        DOCKER_AVAILABLE=false
    fi
    
    log_success "依赖检查完成"
}

# 备份数据库
backup_database() {
    log_info "备份现有数据库..."
    
    local backup_file="backup_$(date +%Y%m%d_%H%M%S).sql"
    
    if [ -z "$DB_HOST" ] || [ -z "$DB_USER" ] || [ -z "$DB_NAME" ]; then
        log_error "数据库连接信息不完整，请设置 DB_HOST, DB_USER, DB_NAME 环境变量"
        exit 1
    fi
    
    mysqldump -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" > "$backup_file"
    
    if [ $? -eq 0 ]; then
        log_success "数据库备份完成: $backup_file"
    else
        log_error "数据库备份失败"
        exit 1
    fi
}

# 执行数据库迁移
migrate_database() {
    log_info "执行数据库迁移..."
    
    if [ ! -f "scripts/migrate-idp-tables.sql" ]; then
        log_error "迁移脚本不存在: scripts/migrate-idp-tables.sql"
        exit 1
    fi
    
    mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < scripts/migrate-idp-tables.sql
    
    if [ $? -eq 0 ]; then
        log_success "数据库迁移完成"
    else
        log_error "数据库迁移失败"
        exit 1
    fi
}

# 构建应用
build_application() {
    log_info "构建应用程序..."
    
    # 构建用户服务
    log_info "构建用户服务..."
    go build -o bin/user-service cmd/user-service/main.go
    
    if [ $? -eq 0 ]; then
        log_success "用户服务构建完成"
    else
        log_error "用户服务构建失败"
        exit 1
    fi
    
    # 构建 API 网关
    log_info "构建 API 网关..."
    go build -o bin/api-gateway cmd/api-gateway/main.go
    
    if [ $? -eq 0 ]; then
        log_success "API 网关构建完成"
    else
        log_error "API 网关构建失败"
        exit 1
    fi
}

# 配置 IDP 提供商
configure_idp_providers() {
    log_info "配置 IDP 提供商..."
    
    if [ ! -f "configs/idp-providers.yaml" ]; then
        if [ -f "configs/idp-providers.example.yaml" ]; then
            cp configs/idp-providers.example.yaml configs/idp-providers.yaml
            log_warning "已复制示例配置文件，请编辑 configs/idp-providers.yaml 配置您的 IDP 提供商"
        else
            log_error "IDP 提供商配置文件不存在"
            exit 1
        fi
    fi
    
    log_success "IDP 提供商配置完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 检查端口是否被占用
    if lsof -Pi :8083 -sTCP:LISTEN -t >/dev/null ; then
        log_warning "端口 8083 已被占用，正在停止现有服务..."
        pkill -f "user-service" || true
        sleep 2
    fi
    
    if lsof -Pi :8080 -sTCP:LISTEN -t >/dev/null ; then
        log_warning "端口 8080 已被占用，正在停止现有服务..."
        pkill -f "api-gateway" || true
        sleep 2
    fi
    
    # 启动用户服务
    log_info "启动用户服务..."
    nohup ./bin/user-service --config=configs/user-service.yaml > logs/user-service.log 2>&1 &
    USER_SERVICE_PID=$!
    
    # 等待服务启动
    sleep 3
    
    if ps -p $USER_SERVICE_PID > /dev/null; then
        log_success "用户服务启动成功 (PID: $USER_SERVICE_PID)"
    else
        log_error "用户服务启动失败"
        exit 1
    fi
    
    # 启动 API 网关
    log_info "启动 API 网关..."
    nohup ./bin/api-gateway --config=configs/api-gateway.yaml > logs/api-gateway.log 2>&1 &
    API_GATEWAY_PID=$!
    
    # 等待服务启动
    sleep 3
    
    if ps -p $API_GATEWAY_PID > /dev/null; then
        log_success "API 网关启动成功 (PID: $API_GATEWAY_PID)"
    else
        log_error "API 网关启动失败"
        exit 1
    fi
    
    # 保存 PID 文件
    echo $USER_SERVICE_PID > user-service.pid
    echo $API_GATEWAY_PID > api-gateway.pid
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查用户服务
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s http://localhost:8083/health > /dev/null; then
            log_success "用户服务健康检查通过"
            break
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            log_error "用户服务健康检查失败"
            exit 1
        fi
        
        log_info "等待用户服务启动... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    # 检查 API 网关
    attempt=1
    while [ $attempt -le $max_attempts ]; do
        if curl -s http://localhost:8080/health > /dev/null; then
            log_success "API 网关健康检查通过"
            break
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            log_error "API 网关健康检查失败"
            exit 1
        fi
        
        log_info "等待 API 网关启动... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
}

# 运行测试
run_tests() {
    log_info "运行测试..."
    
    # 运行单元测试
    log_info "运行单元测试..."
    go test ./internal/auth -v
    
    if [ $? -eq 0 ]; then
        log_success "单元测试通过"
    else
        log_warning "单元测试失败，但继续部署"
    fi
    
    # 运行集成测试
    log_info "运行集成测试..."
    go test ./tests/integration -v
    
    if [ $? -eq 0 ]; then
        log_success "集成测试通过"
    else
        log_warning "集成测试失败，但继续部署"
    fi
}

# 显示部署信息
show_deployment_info() {
    log_success "IDP 优先认证部署完成！"
    echo
    echo "服务信息："
    echo "  用户服务: http://localhost:8083"
    echo "  API 网关: http://localhost:8080"
    echo "  前端应用: http://localhost:3000 (如果已启动)"
    echo
    echo "登录方式："
    echo "  普通用户: http://localhost:3000/login (仅支持 IDP 认证)"
    echo "  系统管理员: http://localhost:3000/admin/login (本地认证)"
    echo
    echo "重要文件："
    echo "  配置文件: configs/user-service.yaml"
    echo "  IDP 配置: configs/idp-providers.yaml"
    echo "  日志文件: logs/user-service.log, logs/api-gateway.log"
    echo "  PID 文件: user-service.pid, api-gateway.pid"
    echo
    echo "下一步："
    echo "  1. 编辑 configs/idp-providers.yaml 配置您的 IDP 提供商"
    echo "  2. 重启服务以应用配置更改"
    echo "  3. 使用管理员账号登录配置 IDP 提供商"
    echo "  4. 测试 IDP 登录功能"
    echo "  5. 为普通用户配置 IDP 认证"
    echo
    echo "重要提醒："
    echo "  - 普通用户只能通过 IDP 认证登录"
    echo "  - 系统管理员可使用本地账号登录"
    echo "  - 请确保至少配置一个可用的 IDP 提供商"
    echo
    echo "停止服务："
    echo "  ./scripts/stop-services.sh"
}

# 主函数
main() {
    log_info "开始部署 PaaS 平台 IDP 混合认证功能..."
    
    # 创建必要的目录
    mkdir -p bin logs
    
    # 检查依赖
    check_dependencies
    
    # 备份数据库
    if [ "$SKIP_BACKUP" != "true" ]; then
        backup_database
    fi
    
    # 执行数据库迁移
    migrate_database
    
    # 构建应用
    build_application
    
    # 配置 IDP 提供商
    configure_idp_providers
    
    # 启动服务
    start_services
    
    # 健康检查
    health_check
    
    # 运行测试
    if [ "$SKIP_TESTS" != "true" ]; then
        run_tests
    fi
    
    # 显示部署信息
    show_deployment_info
}

# 脚本入口
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
