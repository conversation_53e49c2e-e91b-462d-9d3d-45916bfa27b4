# FaaS 服务配置文件 - 开发环境
# Function as a Service 执行器配置

# 服务器配置
server:
  port: 8087                          # 服务端口
  host: "0.0.0.0"                     # 监听地址
  mode: debug                         # 运行模式: debug, release, test
  read_timeout: 120s                  # 读取超时 - 开发环境更长
  write_timeout: 120s                 # 写入超时
  idle_timeout: 300s                  # 空闲超时

# 数据库配置 - 开发环境使用 SQLite
database:
  driver: sqlite                      # 数据库驱动: postgres, sqlite
  dsn: "./data/faas-service.db"       # SQLite 数据库文件
  max_open_conns: 10                  # 最大连接数 - 开发环境较少
  max_idle_conns: 5                   # 最大空闲连接数
  conn_max_lifetime: 1800             # 连接最大生存时间(秒)
  log_level: info                     # 数据库日志级别: silent, error, warn, info

# Redis 配置
redis:
  addr: "localhost:6379"              # Redis 地址
  password: ""                        # Redis 密码 - 开发环境无密码
  db: 1                               # 数据库编号 - 使用不同的 DB
  pool_size: 10                       # 连接池大小 - 开发环境较小
  min_idle_conns: 2                   # 最小空闲连接数
  dial_timeout: 10s                   # 连接超时 - 开发环境更宽松
  read_timeout: 5s                    # 读取超时
  write_timeout: 5s                   # 写入超时

# FaaS 执行器配置 - 开发环境优化
executor:
  # 并发控制 - 开发环境较少
  max_concurrent_executions: 20       # 最大并发执行数
  default_timeout: 60s                # 默认超时时间 - 开发环境更长
  max_execution_time: 600s            # 最大执行时间 - 开发环境更长
  
  # 容器池配置 - 开发环境较小
  container_pool_size: 5              # 容器池大小
  prewarm_containers: 2               # 预热容器数量
  cleanup_interval: 120s              # 清理间隔 - 更频繁
  max_idle_time: 300s                 # 容器最大空闲时间 - 更短
  
  # 资源限制 - 开发环境更宽松
  resource_limits:
    cpu_limit: "2.0"                  # CPU 限制
    memory_limit: "2Gi"               # 内存限制
    disk_limit: "5Gi"                 # 磁盘限制
    
  # 运行时配置
  runtimes:
    nodejs:
      image: "node:18-alpine"
      default_handler: "main"
      file_extension: "js"
    python:
      image: "python:3.11-alpine"
      default_handler: "main"
      file_extension: "py"
    go:
      image: "golang:1.21-alpine"
      default_handler: "main"
      file_extension: "go"
    java:
      image: "openjdk:11-jre-slim"
      default_handler: "handle"
      file_extension: "java"

# Docker 配置
docker:
  host: "unix:///var/run/docker.sock" # Docker 守护进程地址
  api_version: "1.41"                 # Docker API 版本
  timeout: 120s                       # 操作超时时间 - 开发环境更长
  registry: "localhost:5000"          # 私有镜像仓库地址

# 日志配置 - 开发环境详细日志
logging:
  level: debug                        # 日志级别: debug, info, warn, error
  format: text                        # 日志格式: json, text - 开发环境使用文本格式
  output: stdout                      # 输出目标: stdout, file
  file_path: "./logs/faas-service-dev.log" # 日志文件路径
  max_size: 50                        # 日志文件最大大小(MB)
  max_backups: 5                      # 保留的日志文件数量
  max_age: 7                          # 日志文件保留天数 - 开发环境较短
  compress: false                     # 是否压缩旧日志文件 - 开发环境不压缩

# 监控配置 - 开发环境简化
monitoring:
  enabled: true                       # 是否启用监控
  prometheus_addr: "localhost:9090"   # Prometheus 地址
  metrics_path: "/metrics"            # 指标路径
  push_gateway: ""                    # Push Gateway 地址 - 开发环境不使用
  scrape_interval: 30s                # 抓取间隔 - 开发环境较长
  
  # 健康检查配置
  health_check:
    enabled: true
    path: "/health"
    interval: 60s                     # 开发环境检查间隔更长
    timeout: 30s

# 安全配置 - 开发环境宽松
security:
  # JWT 配置
  jwt:
    secret: "dev-faas-service-jwt-secret" # JWT 密钥
    expire_hours: 168                  # Token 过期时间(小时) - 开发环境更长
    
  # 加密配置
  encryption:
    key: "dev-faas-service-encryption-key" # 加密密钥
    
  # 访问控制 - 开发环境关闭
  access_control:
    enabled: false                     # 开发环境关闭访问控制
    allowed_origins: ["*"]             # 允许的来源
    allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    allowed_headers: ["*"]

# 存储配置 - 开发环境较小
storage:
  # 工作空间配置
  workspace:
    base_path: "./data/faas/workspaces" # 工作空间基础路径
    max_size: "2Gi"                    # 最大存储大小 - 开发环境较小
    cleanup_interval: 1800s            # 清理间隔 - 更频繁
    retention_days: 3                  # 保留天数 - 开发环境较短
    
  # 产物存储配置
  artifacts:
    base_path: "./data/faas/artifacts" # 产物存储基础路径
    max_size: "5Gi"                    # 最大存储大小 - 开发环境较小
    max_file_size: "50Mi"              # 单个文件最大大小
    retention_days: 7                  # 保留天数 - 开发环境较短

# 通知配置 - 开发环境关闭
notification:
  enabled: false                      # 开发环境关闭通知
  webhook_url: ""                     # Webhook URL
  timeout: 30s                        # 通知超时
  retry_count: 1                      # 重试次数 - 开发环境较少
  
  # 通知事件
  events:
    - "function.execution.failed"     # 函数执行失败

# 性能配置 - 开发环境优化
performance:
  # 缓存配置 - 开发环境关闭缓存
  cache:
    enabled: false                    # 开发环境关闭缓存
    ttl: 300s                         # 缓存过期时间 - 较短
    max_size: 100                     # 最大缓存条目数 - 较少
    
  # 预加载配置 - 开发环境关闭
  preload:
    enabled: false                    # 开发环境关闭预加载
    popular_functions: 3              # 预加载热门函数数量
    
  # 优化配置
  optimization:
    container_reuse: true             # 容器复用
    code_caching: false               # 代码缓存 - 开发环境关闭
    result_caching: false             # 结果缓存 - 开发环境关闭

# 开发配置
development:
  debug: true                         # 调试模式
  hot_reload: true                    # 热重载
  mock_services: false                # 模拟服务
  profiling: true                     # 性能分析

# 开发环境特殊配置
dev_features:
  # 函数代码热更新
  hot_code_update: true
  
  # 详细错误信息
  verbose_errors: true
  
  # 跳过认证
  skip_auth: true
  
  # 允许危险操作
  allow_dangerous_operations: true
  
  # 测试数据
  test_data:
    enabled: true
    sample_functions: true
