[{"id": "user-list-mock", "name": "用户列表 Mock", "description": "模拟用户列表API响应，支持分页和搜索", "path": "/api/v1/users", "method": "GET", "enabled": true, "priority": 100, "conditions": [{"type": "query", "field": "page", "operator": "exists"}], "response": {"status_code": 200, "headers": {"Content-Type": "application/json", "X-Total-Count": "{{faker.int}}"}, "body_type": "schema", "schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 0}, "message": {"type": "string", "example": "success"}, "data": {"type": "array", "length": 10, "items": {"type": "object", "properties": {"id": {"type": "integer", "faker": "id"}, "name": {"type": "string", "faker": "name"}, "email": {"type": "string", "faker": "email"}, "phone": {"type": "string", "faker": "phone"}, "company": {"type": "string", "faker": "company"}, "department": {"type": "string", "faker": "department"}, "job_title": {"type": "string", "faker": "job_title"}, "created_at": {"type": "string", "faker": "datetime"}, "updated_at": {"type": "string", "faker": "datetime"}, "status": {"type": "string", "enum": ["active", "inactive", "pending"]}}}}, "pagination": {"type": "object", "properties": {"page": {"type": "integer", "example": 1}, "size": {"type": "integer", "example": 10}, "total": {"type": "integer", "faker": "int"}, "pages": {"type": "integer", "faker": "int"}}}}}}, "delay": {"type": "random", "min": 100, "max": 500}, "scenarios": [{"name": "empty_result", "description": "空结果场景", "enabled": true, "probability": 0.1, "weight": 10, "conditions": [{"type": "query", "field": "search", "operator": "equals", "value": "empty"}], "response": {"status_code": 200, "body": {"code": 0, "message": "success", "data": [], "pagination": {"page": 1, "size": 10, "total": 0, "pages": 0}}}}, {"name": "error_scenario", "description": "错误场景", "enabled": true, "probability": 0.05, "weight": 5, "conditions": [{"type": "query", "field": "error", "operator": "equals", "value": "true"}], "response": {"status_code": 500, "body": {"code": -1, "message": "内部服务器错误", "error": "数据库连接失败"}}}], "tags": ["user", "list", "pagination"], "created_by": "developer", "metadata": {"version": "1.0", "category": "user-management"}}, {"id": "user-detail-mock", "name": "用户详情 Mock", "description": "模拟用户详情API响应", "path": "/api/v1/users/{id}", "method": "GET", "enabled": true, "priority": 90, "response": {"status_code": 200, "body_type": "schema", "schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 0}, "message": {"type": "string", "example": "success"}, "data": {"type": "object", "properties": {"id": {"type": "integer", "faker": "id"}, "name": {"type": "string", "faker": "name"}, "email": {"type": "string", "faker": "email"}, "phone": {"type": "string", "faker": "phone"}, "avatar": {"type": "string", "faker": "url"}, "company": {"type": "string", "faker": "company"}, "department": {"type": "string", "faker": "department"}, "job_title": {"type": "string", "faker": "job_title"}, "address": {"type": "string", "faker": "address"}, "bio": {"type": "string", "faker": "paragraph"}, "created_at": {"type": "string", "faker": "datetime"}, "updated_at": {"type": "string", "faker": "datetime"}, "last_login": {"type": "string", "faker": "datetime"}, "status": {"type": "string", "enum": ["active", "inactive", "pending"]}, "permissions": {"type": "array", "items": {"type": "string", "enum": ["read", "write", "admin", "delete"]}}}}}}}, "scenarios": [{"name": "user_not_found", "description": "用户不存在场景", "enabled": true, "probability": 0.1, "weight": 10, "conditions": [{"type": "path", "field": "id", "operator": "equals", "value": "999999"}], "response": {"status_code": 404, "body": {"code": -1, "message": "用户不存在", "error": "User not found"}}}], "tags": ["user", "detail"], "created_by": "developer", "metadata": {"version": "1.0", "category": "user-management"}}, {"id": "user-create-mock", "name": "创建用户 Mock", "description": "模拟创建用户API响应", "path": "/api/v1/users", "method": "POST", "enabled": true, "priority": 80, "conditions": [{"type": "header", "field": "Content-Type", "operator": "contains", "value": "application/json"}, {"type": "body", "field": "name", "operator": "exists"}, {"type": "body", "field": "email", "operator": "exists"}], "response": {"status_code": 201, "body_type": "template", "template": "{\n  \"code\": 0,\n  \"message\": \"用户创建成功\",\n  \"data\": {\n    \"id\": {{faker.id}},\n    \"name\": \"{{request.body.name}}\",\n    \"email\": \"{{request.body.email}}\",\n    \"phone\": \"{{request.body.phone}}\",\n    \"created_at\": \"{{time.now}}\",\n    \"updated_at\": \"{{time.now}}\",\n    \"status\": \"active\"\n  }\n}"}, "delay": {"type": "fixed", "fixed": 200}, "scenarios": [{"name": "email_exists", "description": "邮箱已存在场景", "enabled": true, "probability": 0.2, "weight": 20, "conditions": [{"type": "body", "field": "email", "operator": "equals", "value": "<EMAIL>"}], "response": {"status_code": 400, "body": {"code": -1, "message": "邮箱已存在", "error": "Email already exists", "field": "email"}}}, {"name": "validation_error", "description": "验证错误场景", "enabled": true, "probability": 0.1, "weight": 10, "conditions": [{"type": "body", "field": "email", "operator": "not_contains", "value": "@"}], "response": {"status_code": 400, "body": {"code": -1, "message": "数据验证失败", "errors": [{"field": "email", "message": "邮箱格式不正确"}]}}}], "tags": ["user", "create", "validation"], "created_by": "developer", "metadata": {"version": "1.0", "category": "user-management"}}]