{"path": "/api/v1/users", "method": "GET", "enabled": true, "description": "用户列表API转换规则 - 响应数据脱敏和格式化", "request_rules": {"headers": [{"action": "add", "name": "X-Request-ID", "value": "{{uuid}}", "condition": ""}], "query_params": [{"action": "convert", "name": "page", "data_type": "int", "condition": ""}, {"action": "convert", "name": "size", "data_type": "int", "condition": ""}, {"action": "add", "name": "include_deleted", "value": false, "condition": ""}]}, "response_rules": {"headers": [{"action": "add", "name": "X-Response-Time", "value": "{{response_time}}ms", "condition": ""}, {"action": "remove", "name": "X-Internal-Token", "condition": ""}], "body": {"content_type": "application/json", "transform": "json_transform", "rules": [{"action": "mask", "path": "data.*.phone", "sensitive": true, "mask_type": "phone", "condition": ""}, {"action": "mask", "path": "data.*.email", "sensitive": true, "mask_type": "email", "condition": ""}, {"action": "remove", "path": "data.*.password_hash", "condition": ""}, {"action": "remove", "path": "data.*.internal_notes", "condition": ""}, {"action": "add", "path": "meta.processed_at", "value": "{{timestamp}}", "condition": ""}], "wrapper": {"enabled": true, "code_field": "code", "message_field": "message", "data_field": "data", "timestamp_field": "timestamp", "success_code": 0, "error_code": -1}}}}