{"path": "/api/v1/apps/{id}/deploy", "method": "POST", "enabled": true, "description": "应用部署API转换规则 - 请求参数标准化和响应格式化", "request_rules": {"headers": [{"action": "add", "name": "X-Deploy-Source", "value": "api-gateway", "condition": ""}, {"action": "rename", "name": "X-User-ID", "new_name": "X-Operator-ID", "condition": ""}], "path_params": [{"action": "convert", "name": "id", "data_type": "string", "condition": ""}], "body": {"content_type": "application/json", "transform": "json_transform", "rules": [{"action": "add", "path": "deploy_time", "value": "{{timestamp}}", "condition": ""}, {"action": "convert", "path": "instances", "data_type": "int", "condition": ""}, {"action": "add", "path": "environment", "value": "production", "condition": ""}, {"action": "replace", "path": "version", "pattern": "^v?(.+)$", "replacement": "v$1", "condition": ""}]}}, "response_rules": {"headers": [{"action": "add", "name": "X-Deploy-ID", "value": "{{deploy_id}}", "condition": "success"}], "body": {"content_type": "application/json", "transform": "json_transform", "rules": [{"action": "add", "path": "deploy_url", "value": "https://console.example.com/apps/{{app_id}}/deployments/{{deploy_id}}", "condition": "success"}, {"action": "remove", "path": "internal_config", "condition": ""}, {"action": "rename", "path": "deployment_id", "new_path": "deploy_id", "condition": ""}], "wrapper": {"enabled": true, "code_field": "status", "message_field": "message", "data_field": "result", "timestamp_field": "timestamp", "success_code": 200, "error_code": 500}}, "status_code": {"from": 201, "to": 200, "condition": "success"}}}