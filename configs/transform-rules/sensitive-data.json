{"path": "*", "method": "*", "enabled": true, "description": "全局敏感数据脱敏规则 - 适用于所有API响应", "response_rules": {"body": {"content_type": "application/json", "transform": "json_transform", "rules": [{"action": "mask", "path": "*.password", "sensitive": true, "mask_type": "custom", "mask_pattern": ".*", "condition": ""}, {"action": "mask", "path": "*.secret", "sensitive": true, "mask_type": "custom", "mask_pattern": ".*", "condition": ""}, {"action": "mask", "path": "*.token", "sensitive": true, "mask_type": "custom", "mask_pattern": "(.{4}).*(.{4})", "condition": ""}, {"action": "mask", "path": "*.api_key", "sensitive": true, "mask_type": "custom", "mask_pattern": "(.{8}).*(.{4})", "condition": ""}, {"action": "mask", "path": "*.phone", "sensitive": true, "mask_type": "phone", "condition": ""}, {"action": "mask", "path": "*.mobile", "sensitive": true, "mask_type": "phone", "condition": ""}, {"action": "mask", "path": "*.email", "sensitive": true, "mask_type": "email", "condition": ""}, {"action": "mask", "path": "*.id_card", "sensitive": true, "mask_type": "id_card", "condition": ""}, {"action": "mask", "path": "*.credit_card", "sensitive": true, "mask_type": "credit_card", "condition": ""}, {"action": "remove", "path": "*.internal_id", "condition": ""}, {"action": "remove", "path": "*.debug_info", "condition": ""}, {"action": "remove", "path": "*.stack_trace", "condition": ""}]}}}