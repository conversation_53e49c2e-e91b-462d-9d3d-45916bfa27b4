# 负载均衡服务配置文件

# 🌐 服务器配置
server:
  port: 8086                    # 服务端口
  host: "0.0.0.0"              # 监听地址
  read_timeout: 30s            # 读取超时
  write_timeout: 30s           # 写入超时

# 📝 日志配置
logging:
  level: "info"                # 日志级别: debug, info, warn, error
  format: "json"               # 日志格式: json, text
  output: "stdout"             # 输出: stdout, stderr, file
  file: "logs/loadbalancer.log" # 日志文件路径

# 🔧 服务注册中心配置
registry:
  # 存储类型: memory, file, etcd, consul
  storage_type: "memory"
  
  # 内存存储配置
  memory:
    cleanup_interval: "5m"     # 清理间隔
    stale_threshold: "10m"     # 过期阈值
    
  # 文件存储配置
  file:
    data_dir: "data/registry"  # 数据目录
    backup_interval: "1h"      # 备份间隔
    
  # 服务发现配置
  discovery:
    enabled: true              # 启用服务发现
    broadcast_interval: "30s"  # 广播间隔

# ⚖️ 负载均衡配置
load_balancer:
  # 默认算法: round_robin, weighted_round_robin, least_connections, ip_hash, random
  default_algorithm: "round_robin"
  
  # 算法配置
  algorithms:
    round_robin:
      enabled: true
    weighted_round_robin:
      enabled: true
      default_weight: 1
    least_connections:
      enabled: true
    ip_hash:
      enabled: true
      hash_key: "client_ip"
    random:
      enabled: true
      
  # 连接池配置
  connection_pool:
    max_idle_conns: 100        # 最大空闲连接数
    max_idle_conns_per_host: 10 # 每个主机最大空闲连接数
    idle_conn_timeout: "90s"   # 空闲连接超时

# 🏥 健康检查配置
health_check:
  # 全局配置
  global:
    enabled: true              # 启用健康检查
    default_interval: "30s"    # 默认检查间隔
    default_timeout: "5s"      # 默认检查超时
    default_path: "/health"    # 默认检查路径
    
  # 检查器配置
  checker:
    worker_count: 10           # 工作协程数
    queue_size: 1000          # 队列大小
    batch_size: 50            # 批处理大小
    
  # 失败处理
  failure_handling:
    max_failures: 3           # 最大失败次数
    failure_window: "5m"      # 失败窗口期
    recovery_check_interval: "10s" # 恢复检查间隔

# 📊 监控配置
monitoring:
  metrics_enabled: true       # 启用指标收集
  metrics_port: 9091         # 指标端口
  stats_interval: "10s"      # 统计间隔
  
  # 统计信息保留
  stats_retention:
    max_entries: 10000        # 最大条目数
    cleanup_interval: "1h"    # 清理间隔

# 🔒 安全配置
security:
  api_key: ""                 # API 密钥
  jwt_secret: ""              # JWT 密钥
  
  # IP 白名单
  ip_whitelist:
    enabled: false            # 启用IP白名单
    allowed_ips:
      - "127.0.0.1"
      - "::1"
      
# 🚦 限流配置
rate_limiting:
  enabled: true               # 启用限流
  requests_per_minute: 10000  # 每分钟请求数
  burst_size: 1000           # 突发大小
  
  # 按服务限流
  per_service:
    enabled: true             # 启用按服务限流
    default_limit: 1000       # 默认限制
    
# 💾 缓存配置
cache:
  enabled: true               # 启用缓存
  ttl: "5m"                  # 缓存TTL
  max_size: 10000            # 最大缓存大小
  
# 📋 访问日志配置
access_log:
  enabled: true               # 启用访问日志
  format: "json"              # 日志格式
  file: "logs/access.log"     # 日志文件
  rotation:
    max_size: "100MB"         # 最大文件大小
    max_files: 10             # 最大文件数
    max_age: "7d"             # 最大保留时间

# 🔧 服务特定配置
services:
  # API 网关配置
  api-gateway:
    algorithm: "round_robin"
    health_check:
      enabled: true
      path: "/health"
      interval: "30s"
      timeout: "5s"
    circuit_breaker:
      enabled: true
      failure_threshold: 5
      recovery_timeout: "30s"
      
  # 应用管理服务配置
  app-manager:
    algorithm: "least_connections"
    health_check:
      enabled: true
      path: "/health"
      interval: "30s"
      timeout: "5s"
    rate_limiting:
      requests_per_minute: 1000
      
  # 用户服务配置
  user-service:
    algorithm: "ip_hash"
    health_check:
      enabled: true
      path: "/health"
      interval: "30s"
      timeout: "5s"
    session_affinity: true

# 🔄 故障转移配置
failover:
  enabled: true               # 启用故障转移
  retry_attempts: 3          # 重试次数
  retry_delay: "1s"          # 重试延迟
  circuit_breaker:
    enabled: true            # 启用断路器
    failure_threshold: 5     # 失败阈值
    recovery_timeout: "30s"  # 恢复超时
    half_open_requests: 3    # 半开状态请求数

# 🌍 多区域配置
multi_region:
  enabled: false             # 启用多区域
  regions:
    - name: "us-east-1"
      weight: 50
      endpoints:
        - "lb-us-east-1.example.com:8086"
    - name: "us-west-2"
      weight: 50
      endpoints:
        - "lb-us-west-2.example.com:8086"

# 🔍 调试配置
debug:
  enabled: false             # 启用调试模式
  profiling: false          # 启用性能分析
  trace_requests: false     # 跟踪请求
  dump_config: false        # 转储配置

# 📈 性能调优
performance:
  # 连接配置
  max_connections: 10000     # 最大连接数
  keep_alive_timeout: "60s"  # Keep-Alive 超时
  
  # 缓冲区配置
  read_buffer_size: 4096     # 读缓冲区大小
  write_buffer_size: 4096    # 写缓冲区大小
  
  # 超时配置
  dial_timeout: "10s"        # 连接超时
  response_header_timeout: "10s" # 响应头超时
  
# 🚨 告警配置
alerting:
  enabled: false             # 启用告警
  webhook_url: ""           # Webhook URL
  thresholds:
    error_rate: 0.05         # 错误率阈值 (5%)
    response_time: "1s"      # 响应时间阈值
    availability: 0.99       # 可用性阈值 (99%)
