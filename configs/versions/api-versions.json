{"versions": [{"version": "v1", "name": "API Version 1.0", "description": "第一个稳定版本的API，提供基础功能", "status": "stable", "is_default": true, "is_stable": true, "release_date": "2023-01-01T00:00:00Z", "base_url": "http://localhost:8080", "service_map": {"users": "user-service-v1", "apps": "app-service-v1", "auth": "auth-service-v1"}, "metadata": {"maintainer": "API Team", "documentation": "https://docs.example.com/api/v1", "changelog": "https://docs.example.com/api/v1/changelog"}}, {"version": "v2", "name": "API Version 2.0", "description": "第二版API，增加了新功能和改进", "status": "stable", "is_default": false, "is_stable": true, "release_date": "2023-06-01T00:00:00Z", "base_url": "http://localhost:8080", "backward_compatible": ["v1"], "service_map": {"users": "user-service-v2", "apps": "app-service-v2", "auth": "auth-service-v2"}, "metadata": {"maintainer": "API Team", "documentation": "https://docs.example.com/api/v2", "changelog": "https://docs.example.com/api/v2/changelog", "breaking_changes": "https://docs.example.com/api/v2/breaking-changes"}}, {"version": "v3", "name": "API Version 3.0 Beta", "description": "第三版API测试版本，包含实验性功能", "status": "beta", "is_default": false, "is_stable": false, "release_date": "2024-01-01T00:00:00Z", "base_url": "http://localhost:8080", "backward_compatible": ["v2"], "service_map": {"users": "user-service-v3", "apps": "app-service-v3", "auth": "auth-service-v3"}, "metadata": {"maintainer": "API Team", "documentation": "https://docs.example.com/api/v3-beta", "experimental": "true", "feedback_url": "https://feedback.example.com/api/v3"}}, {"version": "v0.9", "name": "API Version 0.9 (Legacy)", "description": "遗留版本，即将废弃", "status": "deprecated", "is_default": false, "is_stable": false, "release_date": "2022-06-01T00:00:00Z", "deprecated_date": "2023-01-01T00:00:00Z", "sunset_date": "2024-01-01T00:00:00Z", "base_url": "http://localhost:8080", "forward_compatible": ["v1"], "service_map": {"users": "user-service-legacy", "apps": "app-service-legacy", "auth": "auth-service-legacy"}, "metadata": {"maintainer": "Legacy Support Team", "migration_guide": "https://docs.example.com/migration/v0.9-to-v1", "support_end_date": "2024-01-01"}}], "routes": [{"path": "/api/*/users", "method": "GET", "versions": ["v1", "v2", "v3"], "default_version": "v2", "strategy": "header", "fallback_policy": "compatible", "version_map": {"v0.9": "v1"}, "compatibility_rules": [{"from_version": "v0.9", "to_version": "v1", "type": "backward", "transforms": [{"type": "rename", "source": "user_id", "target": "id"}, {"type": "default", "target": "created_at", "default_value": "2023-01-01T00:00:00Z"}]}], "description": "用户列表API，支持多版本", "tags": ["users", "list"], "metadata": {"rate_limit": "1000/hour", "cache_ttl": "300s"}}, {"path": "/api/*/apps", "method": "POST", "versions": ["v1", "v2", "v3"], "default_version": "v2", "strategy": "path", "fallback_policy": "latest", "compatibility_rules": [{"from_version": "v1", "to_version": "v2", "type": "forward", "transforms": [{"type": "add", "target": "deployment_config", "default_value": {"strategy": "rolling", "replicas": 1}}, {"type": "mapping", "source": "language", "target": "runtime", "mapping": {"nodejs": "node:18", "python": "python:3.9", "go": "golang:1.19"}}]}], "description": "应用创建API，支持版本兼容", "tags": ["apps", "create"], "metadata": {"rate_limit": "100/hour", "requires_auth": "true"}}, {"path": "/api/*/auth/login", "method": "POST", "versions": ["v1", "v2", "v3"], "default_version": "v2", "strategy": "header", "fallback_policy": "default", "compatibility_rules": [{"from_version": "v1", "to_version": "v2", "type": "backward", "transforms": [{"type": "rename", "source": "username", "target": "email"}, {"type": "add", "target": "login_type", "default_value": "password"}]}], "description": "用户登录API，支持多种认证方式", "tags": ["auth", "login"], "metadata": {"rate_limit": "10/minute", "security_level": "high"}}], "policies": [{"name": "deprecation_warning", "description": "废弃版本警告策略", "priority": 100, "enabled": true, "rules": [{"condition": "version == 'v0.9'", "action": "log", "target": "warning", "priority": 1}], "metadata": {"category": "deprecation"}}, {"name": "beta_access_control", "description": "Beta版本访问控制", "priority": 200, "enabled": true, "rules": [{"condition": "version == 'v3' && !has_beta_access", "action": "reject", "target": "error", "priority": 1}], "metadata": {"category": "access_control"}}, {"name": "version_migration", "description": "版本迁移策略", "priority": 50, "enabled": true, "rules": [{"condition": "version == 'v0.9'", "action": "transform", "target": "v1", "priority": 1}], "metadata": {"category": "migration"}}], "config": {"detection_methods": ["header", "path", "query"], "default_version": "v1", "version_format": "major", "strict_validation": false, "enable_compatibility": true, "max_compatible_versions": 3, "enable_cache": true, "cache_ttl": "5m", "enable_metrics": true, "enable_tracing": true}}