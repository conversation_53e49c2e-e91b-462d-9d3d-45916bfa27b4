# API 参数验证规则配置
# 定义各个 API 端点的参数验证规则

# 🔧 验证配置
validation:
  enabled: true                    # 启用验证
  strict_mode: false              # 严格模式（返回详细错误信息）
  log_validation: true            # 记录验证日志
  cache_results: false            # 缓存验证结果
  max_cache_size: 1000           # 最大缓存大小
  cache_ttl: 300                 # 缓存TTL（秒）
  
  # 跳过验证的路径
  skip_paths:
    - "/health"
    - "/ready" 
    - "/metrics"
    - "/swagger/*"
    - "/api/v1/auth/login"
    - "/api/v1/auth/register"

# 📋 验证规则定义
rules:
  # 🔐 用户认证相关 API
  - path: "/api/v1/auth/login"
    method: "POST"
    headers:
      Content-Type: "application/json"
    body:
      content_type: "application/json"
      required: true
      max_size: 1024
      schema: "login_request"
    business:
      - name: "rate_limit_check"
        description: "登录频率限制检查"
        condition: "request_count < 5 per minute"
        message: "登录请求过于频繁，请稍后再试"

  - path: "/api/v1/auth/register"
    method: "POST"
    headers:
      Content-Type: "application/json"
    body:
      content_type: "application/json"
      required: true
      max_size: 2048
      schema: "register_request"

  # 📱 应用管理相关 API
  - path: "/api/v1/apps"
    method: "POST"
    headers:
      Authorization: "required"
      Content-Type: "application/json"
    body:
      content_type: "application/json"
      required: true
      max_size: 10240
      schema: "create_app_request"
    business:
      - name: "tenant_access"
        description: "租户访问权限检查"
        message: "无权限访问该租户资源"

  - path: "/api/v1/apps"
    method: "GET"
    headers:
      Authorization: "required"
    query_params:
      page:
        type: "int"
        required: false
        min: 1
        max: 1000
        default: 1
        description: "页码"
      size:
        type: "int"
        required: false
        min: 1
        max: 100
        default: 20
        description: "每页大小"
      status:
        type: "string"
        required: false
        enum: ["created", "building", "running", "stopped", "error"]
        description: "应用状态过滤"
      tenant_id:
        type: "string"
        required: false
        pattern: "^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$"
        description: "租户ID"

  - path: "/api/v1/apps/{id}"
    method: "GET"
    headers:
      Authorization: "required"
    path_params:
      id:
        type: "string"
        required: true
        pattern: "^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$"
        description: "应用ID"
    business:
      - name: "resource_access"
        description: "资源访问权限检查"
        message: "无权限访问该应用"

  - path: "/api/v1/apps/{id}"
    method: "PUT"
    headers:
      Authorization: "required"
      Content-Type: "application/json"
    path_params:
      id:
        type: "string"
        required: true
        pattern: "^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$"
        description: "应用ID"
    body:
      content_type: "application/json"
      required: true
      max_size: 10240
      schema: "update_app_request"
    business:
      - name: "resource_owner"
        description: "资源所有者检查"
        message: "只有应用所有者可以修改应用"

  - path: "/api/v1/apps/{id}/deploy"
    method: "POST"
    headers:
      Authorization: "required"
      Content-Type: "application/json"
    path_params:
      id:
        type: "string"
        required: true
        pattern: "^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$"
        description: "应用ID"
    body:
      content_type: "application/json"
      required: true
      max_size: 4096
      schema: "deploy_request"

  - path: "/api/v1/apps/{id}/scale"
    method: "POST"
    headers:
      Authorization: "required"
      Content-Type: "application/json"
    path_params:
      id:
        type: "string"
        required: true
        pattern: "^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$"
        description: "应用ID"
    body:
      content_type: "application/json"
      required: true
      max_size: 1024
      schema: "scale_request"

  # 🔧 脚本执行相关 API
  - path: "/api/v1/scripts"
    method: "POST"
    headers:
      Authorization: "required"
      Content-Type: "application/json"
    body:
      content_type: "application/json"
      required: true
      max_size: 51200  # 50KB
      schema: "create_script_request"

  - path: "/api/v1/scripts/{id}/execute"
    method: "POST"
    headers:
      Authorization: "required"
      Content-Type: "application/json"
    path_params:
      id:
        type: "string"
        required: true
        pattern: "^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$"
        description: "脚本ID"
    body:
      content_type: "application/json"
      required: false
      max_size: 4096
      schema: "execute_script_request"

  # 👥 用户管理相关 API
  - path: "/api/v1/users"
    method: "GET"
    headers:
      Authorization: "required"
    query_params:
      page:
        type: "int"
        required: false
        min: 1
        max: 1000
        default: 1
      size:
        type: "int"
        required: false
        min: 1
        max: 100
        default: 20
      role:
        type: "string"
        required: false
        enum: ["admin", "user", "developer", "viewer"]
        description: "用户角色过滤"
    business:
      - name: "admin_only"
        description: "管理员权限检查"
        message: "只有管理员可以查看用户列表"

  - path: "/api/v1/users/{id}"
    method: "PUT"
    headers:
      Authorization: "required"
      Content-Type: "application/json"
    path_params:
      id:
        type: "string"
        required: true
        pattern: "^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$"
        description: "用户ID"
    body:
      content_type: "application/json"
      required: true
      max_size: 2048
      schema: "update_user_request"
    business:
      - name: "self_or_admin"
        description: "只能修改自己的信息或管理员权限"
        message: "无权限修改该用户信息"

# 📄 JSON Schema 定义
schemas:
  # 登录请求 Schema
  login_request:
    description: "用户登录请求"
    schema:
      type: "object"
      required: ["username", "password"]
      properties:
        username:
          type: "string"
          minLength: 3
          maxLength: 50
          description: "用户名或邮箱"
        password:
          type: "string"
          minLength: 6
          maxLength: 128
          description: "密码"
        remember_me:
          type: "boolean"
          description: "记住登录状态"

  # 注册请求 Schema
  register_request:
    description: "用户注册请求"
    schema:
      type: "object"
      required: ["username", "email", "password"]
      properties:
        username:
          type: "string"
          minLength: 3
          maxLength: 50
          pattern: "^[a-zA-Z0-9_-]+$"
          description: "用户名"
        email:
          type: "string"
          format: "email"
          description: "邮箱地址"
        password:
          type: "string"
          minLength: 8
          maxLength: 128
          description: "密码"
        confirm_password:
          type: "string"
          description: "确认密码"
        tenant_name:
          type: "string"
          minLength: 2
          maxLength: 100
          description: "租户名称"

  # 创建应用请求 Schema
  create_app_request:
    description: "创建应用请求"
    schema:
      type: "object"
      required: ["name", "language", "version"]
      properties:
        name:
          type: "string"
          minLength: 1
          maxLength: 100
          pattern: "^[a-zA-Z0-9_-]+$"
          description: "应用名称"
        description:
          type: "string"
          maxLength: 500
          description: "应用描述"
        language:
          type: "string"
          enum: ["nodejs", "python", "go", "java"]
          description: "编程语言"
        framework:
          type: "string"
          maxLength: 50
          description: "框架名称"
        version:
          type: "string"
          pattern: "^\\d+\\.\\d+\\.\\d+$"
          description: "版本号"
        git_repo:
          type: "string"
          format: "uri"
          description: "Git 仓库地址"
        git_branch:
          type: "string"
          maxLength: 100
          default: "main"
          description: "Git 分支"
        config:
          type: "object"
          description: "应用配置"

  # 更新应用请求 Schema
  update_app_request:
    description: "更新应用请求"
    schema:
      type: "object"
      properties:
        name:
          type: "string"
          minLength: 1
          maxLength: 100
          pattern: "^[a-zA-Z0-9_-]+$"
        description:
          type: "string"
          maxLength: 500
        framework:
          type: "string"
          maxLength: 50
        version:
          type: "string"
          pattern: "^\\d+\\.\\d+\\.\\d+$"
        git_repo:
          type: "string"
          format: "uri"
        git_branch:
          type: "string"
          maxLength: 100
        config:
          type: "object"

  # 部署请求 Schema
  deploy_request:
    description: "应用部署请求"
    schema:
      type: "object"
      properties:
        version:
          type: "string"
          pattern: "^\\d+\\.\\d+\\.\\d+$"
          description: "部署版本"
        branch:
          type: "string"
          maxLength: 100
          description: "Git 分支"
        environment:
          type: "string"
          enum: ["development", "staging", "production"]
          default: "development"
          description: "部署环境"
        config_overrides:
          type: "object"
          description: "配置覆盖"

  # 扩缩容请求 Schema
  scale_request:
    description: "应用扩缩容请求"
    schema:
      type: "object"
      required: ["instances"]
      properties:
        instances:
          type: "integer"
          minimum: 0
          maximum: 100
          description: "实例数量"
        reason:
          type: "string"
          maxLength: 200
          description: "扩缩容原因"

  # 创建脚本请求 Schema
  create_script_request:
    description: "创建脚本请求"
    schema:
      type: "object"
      required: ["name", "runtime", "content"]
      properties:
        name:
          type: "string"
          minLength: 1
          maxLength: 100
          pattern: "^[a-zA-Z0-9_-]+$"
          description: "脚本名称"
        description:
          type: "string"
          maxLength: 500
          description: "脚本描述"
        runtime:
          type: "string"
          enum: ["python", "nodejs", "go", "java", "shell"]
          description: "运行时环境"
        content:
          type: "string"
          minLength: 1
          maxLength: 50000
          description: "脚本内容"
        timeout:
          type: "integer"
          minimum: 1
          maximum: 3600
          default: 300
          description: "超时时间（秒）"
        environment:
          type: "object"
          description: "环境变量"

  # 执行脚本请求 Schema
  execute_script_request:
    description: "执行脚本请求"
    schema:
      type: "object"
      properties:
        parameters:
          type: "object"
          description: "脚本参数"
        environment:
          type: "object"
          description: "环境变量覆盖"
        timeout:
          type: "integer"
          minimum: 1
          maximum: 3600
          description: "超时时间（秒）"

  # 更新用户请求 Schema
  update_user_request:
    description: "更新用户请求"
    schema:
      type: "object"
      properties:
        username:
          type: "string"
          minLength: 3
          maxLength: 50
          pattern: "^[a-zA-Z0-9_-]+$"
        email:
          type: "string"
          format: "email"
        roles:
          type: "array"
          items:
            type: "string"
            enum: ["admin", "user", "developer", "viewer"]
          description: "用户角色"
        status:
          type: "string"
          enum: ["active", "inactive", "suspended"]
          description: "用户状态"
        profile:
          type: "object"
          properties:
            display_name:
              type: "string"
              maxLength: 100
            avatar_url:
              type: "string"
              format: "uri"
            phone:
              type: "string"
              pattern: "^1[3-9]\\d{9}$"
