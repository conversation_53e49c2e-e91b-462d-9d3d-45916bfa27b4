# FaaS 服务配置文件 - 生产环境
# Function as a Service 执行器配置

# 服务器配置
server:
  port: 8087                          # 服务端口
  host: "0.0.0.0"                     # 监听地址
  mode: release                       # 运行模式: debug, release, test
  read_timeout: 60s                   # 读取超时
  write_timeout: 60s                  # 写入超时
  idle_timeout: 120s                  # 空闲超时

# 数据库配置
database:
  driver: postgres                    # 数据库驱动: postgres, sqlite
  dsn: "host=localhost user=paas_user password=paas_password dbname=paas_faas port=5432 sslmode=disable TimeZone=Asia/Shanghai"
  max_open_conns: 50                  # 最大连接数
  max_idle_conns: 10                  # 最大空闲连接数
  conn_max_lifetime: 3600             # 连接最大生存时间(秒)
  log_level: warn                     # 数据库日志级别: silent, error, warn, info

# Redis 配置
redis:
  addr: "localhost:6379"              # Redis 地址
  password: "paas_redis_password"     # Redis 密码
  db: 0                               # 数据库编号
  pool_size: 20                       # 连接池大小
  min_idle_conns: 5                   # 最小空闲连接数
  dial_timeout: 5s                    # 连接超时
  read_timeout: 3s                    # 读取超时
  write_timeout: 3s                   # 写入超时

# FaaS 执行器配置
executor:
  # 并发控制
  max_concurrent_executions: 100      # 最大并发执行数
  default_timeout: 30s                # 默认超时时间
  max_execution_time: 300s            # 最大执行时间
  
  # 容器池配置
  container_pool_size: 20             # 容器池大小
  prewarm_containers: 5               # 预热容器数量
  cleanup_interval: 300s              # 清理间隔
  max_idle_time: 600s                 # 容器最大空闲时间
  
  # 资源限制
  resource_limits:
    cpu_limit: "1.0"                  # CPU 限制
    memory_limit: "1Gi"               # 内存限制
    disk_limit: "2Gi"                 # 磁盘限制
    
  # 运行时配置
  runtimes:
    nodejs:
      image: "node:18-alpine"
      default_handler: "main"
      file_extension: "js"
    python:
      image: "python:3.11-alpine"
      default_handler: "main"
      file_extension: "py"
    go:
      image: "golang:1.21-alpine"
      default_handler: "main"
      file_extension: "go"
    java:
      image: "openjdk:11-jre-slim"
      default_handler: "handle"
      file_extension: "java"

# Docker 配置
docker:
  host: "unix:///var/run/docker.sock" # Docker 守护进程地址
  api_version: "1.41"                 # Docker API 版本
  timeout: 60s                        # 操作超时时间
  registry: "localhost:5000"          # 私有镜像仓库地址

# 日志配置
logging:
  level: info                         # 日志级别: debug, info, warn, error
  format: json                        # 日志格式: json, text
  output: stdout                      # 输出目标: stdout, file
  file_path: "./logs/faas-service.log" # 日志文件路径
  max_size: 100                       # 日志文件最大大小(MB)
  max_backups: 10                     # 保留的日志文件数量
  max_age: 30                         # 日志文件保留天数
  compress: true                      # 是否压缩旧日志文件

# 监控配置
monitoring:
  enabled: true                       # 是否启用监控
  prometheus_addr: "localhost:9090"   # Prometheus 地址
  metrics_path: "/metrics"            # 指标路径
  push_gateway: "localhost:9091"      # Push Gateway 地址
  scrape_interval: 15s                # 抓取间隔
  
  # 健康检查配置
  health_check:
    enabled: true
    path: "/health"
    interval: 30s
    timeout: 10s

# 安全配置
security:
  # JWT 配置
  jwt:
    secret: "faas-service-jwt-secret"  # JWT 密钥
    expire_hours: 24                   # Token 过期时间(小时)
    
  # 加密配置
  encryption:
    key: "faas-service-encryption-key" # 加密密钥
    
  # 访问控制
  access_control:
    enabled: true
    allowed_origins: ["*"]             # 允许的来源
    allowed_methods: ["GET", "POST", "PUT", "DELETE"]
    allowed_headers: ["*"]

# 存储配置
storage:
  # 工作空间配置
  workspace:
    base_path: "./data/faas/workspaces" # 工作空间基础路径
    max_size: "10Gi"                   # 最大存储大小
    cleanup_interval: 3600s            # 清理间隔
    retention_days: 7                  # 保留天数
    
  # 产物存储配置
  artifacts:
    base_path: "./data/faas/artifacts" # 产物存储基础路径
    max_size: "50Gi"                   # 最大存储大小
    max_file_size: "100Mi"             # 单个文件最大大小
    retention_days: 30                 # 保留天数

# 通知配置
notification:
  enabled: true                       # 是否启用通知
  webhook_url: ""                     # Webhook URL
  timeout: 30s                        # 通知超时
  retry_count: 3                      # 重试次数
  
  # 通知事件
  events:
    - "function.execution.failed"     # 函数执行失败
    - "container.pool.exhausted"      # 容器池耗尽
    - "resource.limit.exceeded"       # 资源限制超出

# 性能配置
performance:
  # 缓存配置
  cache:
    enabled: true
    ttl: 3600s                        # 缓存过期时间
    max_size: 1000                    # 最大缓存条目数
    
  # 预加载配置
  preload:
    enabled: true
    popular_functions: 10             # 预加载热门函数数量
    
  # 优化配置
  optimization:
    container_reuse: true             # 容器复用
    code_caching: true                # 代码缓存
    result_caching: false             # 结果缓存

# 开发配置
development:
  debug: false                        # 调试模式
  hot_reload: false                   # 热重载
  mock_services: false                # 模拟服务
  profiling: false                    # 性能分析
