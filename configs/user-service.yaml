# 用户认证服务配置文件
# 专门负责用户管理、认证授权、权限控制等功能

# 服务器配置
server:
  port: 8085                          # 用户服务端口 (按架构文档应为8083，但避免与config-service冲突)
  mode: release                       # 运行模式: release (生产), debug (开发)
  read_timeout: 30s                   # 读取超时时间
  write_timeout: 30s                  # 写入超时时间
  idle_timeout: 60s                   # 空闲超时时间

# 数据库配置
database:
  driver: postgres                    # 数据库驱动: postgres, mysql, sqlite
  dsn: "host=localhost user=paas password=paas123 dbname=paas_users port=5432 sslmode=disable TimeZone=Asia/Shanghai"
  max_open_conns: 100                 # 最大打开连接数
  max_idle_conns: 20                  # 最大空闲连接数
  conn_max_lifetime: 3600             # 连接最大生存时间(秒)
  log_level: warn                     # 数据库日志级别

# Redis配置 (用于会话管理和缓存)
redis:
  addr: "localhost:6379"              # Redis地址
  password: ""                        # Redis密码
  db: 0                              # Redis数据库编号 (用户服务专用)
  pool_size: 20                      # 连接池大小
  min_idle_conns: 5                  # 最小空闲连接数
  dial_timeout: 5s                   # 连接超时时间
  read_timeout: 3s                   # 读取超时时间
  write_timeout: 3s                  # 写入超时时间

# 日志配置
log:
  level: info                         # 日志级别: debug, info, warn, error
  format: json                        # 日志格式: json, text
  output: stdout                      # 日志输出: stdout, stderr, file
  service_name: "user-service"        # 服务名称

# 🔒 安全配置
security:
  # JWT认证配置
  jwt:
    enabled: true                     # 是否启用 JWT 校验
    secret: "user-service-jwt-secret-key" # JWT 密钥 (生产环境请使用强密钥)
    expires_in: 24h                   # Access Token 过期时间
    refresh_expires_in: 168h          # Refresh Token 过期时间 (7天)

    # 🔧 开发模式配置
    dev_mode: false                   # 开发模式 (环境变量: PAAS_DEV_MODE)
    dev_token: "user-service-dev-token" # 开发模式令牌

    # 🧑‍💻 开发模式用户信息
    dev_user:
      id: "dev-user-001"              # 开发用户ID
      tenant_id: "dev-tenant-001"     # 开发租户ID
      username: "开发者"               # 开发用户名
      email: "developer@localhost"    # 开发用户邮箱
      roles: ["admin", "user_manager"] # 用户管理权限
      permissions: ["user:*", "auth:*"] # 用户和认证相关权限

  # 🆔 IDP (Identity Provider) 优先认证配置
  idp:
    enabled: true                     # 是否启用 IDP 集成 (环境变量: PAAS_IDP_ENABLED)
    idp_first: true                   # IDP 优先模式，普通用户仅支持 IDP 认证 (环境变量: PAAS_IDP_FIRST)
    allow_local_auth: true            # 是否允许本地认证 (环境变量: PAAS_IDP_ALLOW_LOCAL_AUTH)
    admin_only_local_auth: true       # 本地认证仅限管理员 (环境变量: PAAS_IDP_ADMIN_ONLY_LOCAL_AUTH)
    auto_create_user: true            # 是否自动创建 IDP 用户 (环境变量: PAAS_IDP_AUTO_CREATE_USER)
    auto_link_user: false             # 是否自动关联现有用户 (环境变量: PAAS_IDP_AUTO_LINK_USER)

    # 🔧 认证配置
    auth:
      token_header: "Authorization"   # 令牌头名称
      token_prefix: "Bearer "         # 令牌前缀
      cache_ttl: 300                  # 令牌验证缓存时间（秒）

      # 跳过认证的路径
      skip_paths:
        - "/health"
        - "/ready"
        - "/metrics"
        - "/swagger"
        - "/swagger/*"
        - "/api/v1/admin/login"        # 管理员登录
        - "/api/v1/idp/login/*"        # IDP 登录
        - "/api/v1/idp/callback/*"     # IDP 回调

      # 管理员专用路径（支持本地认证）
      admin_paths:
        - "/api/v1/admin"              # 管理员 API
        - "/api/v1/idp/admin"          # IDP 管理 API

    # 🔄 同步配置
    sync:
      enabled: true                   # 是否启用用户信息同步
      interval: "1h"                  # 同步间隔
      batch_size: 100                 # 批量同步大小
      timeout: "30s"                  # 同步超时时间

      # 同步字段映射
      field_mapping:
        update_email: true            # 是否更新邮箱
        update_name: true             # 是否更新姓名
        update_avatar: true           # 是否更新头像
        update_phone: false           # 是否更新电话
        update_roles: false           # 是否更新角色（谨慎启用）
    
    # 🛤️ 跳过认证的路径
    skip_paths:                       
      - "/health"                     # 健康检查
      - "/ready"                      # 就绪检查
      - "/metrics"                    # 监控指标
      - "/api/v1/auth/login"          # 登录接口
      - "/api/v1/auth/register"       # 注册接口
      - "/api/v1/auth/refresh"        # 刷新令牌
      - "/api/v1/auth/reset-password" # 重置密码
      - "/api/v1/auth/verify-email"   # 邮箱验证
      - "/swagger"                    # API文档
      - "/swagger/*"                  # API文档资源
  
  # 密码策略
  password:
    min_length: 8                     # 最小长度
    require_uppercase: true           # 需要大写字母
    require_lowercase: true           # 需要小写字母
    require_numbers: true             # 需要数字
    require_symbols: false            # 需要特殊字符
    max_age_days: 90                  # 密码最大有效期 (天)
    history_count: 5                  # 密码历史记录数量
  
  # 登录安全
  login:
    max_attempts: 5                   # 最大登录尝试次数
    lockout_duration: 15m             # 账户锁定时间
    session_timeout: 24h              # 会话超时时间
    require_captcha_after: 3          # 多少次失败后需要验证码
  
  # 会话管理
  session:
    enabled: true                     # 启用会话管理
    store_type: "redis"               # 会话存储类型: memory, redis
    max_concurrent: 5                 # 单用户最大并发会话数
    cleanup_interval: 1h              # 会话清理间隔
  
  # 权限控制
  rbac:
    enabled: true                     # 启用RBAC
    cache_enabled: true               # 启用权限缓存
    cache_ttl: 30m                    # 权限缓存过期时间
    default_role: "user"              # 默认角色
  
  # 审计日志
  audit:
    enabled: true                     # 启用审计
    log_login: true                   # 记录登录事件
    log_logout: true                  # 记录登出事件
    log_permission_check: false       # 记录权限检查 (可能产生大量日志)
    retention_days: 90                # 审计日志保留天数

# 用户管理配置
user:
  # 注册配置
  registration:
    enabled: true                     # 允许用户注册
    require_email_verification: true  # 需要邮箱验证
    require_admin_approval: false     # 需要管理员审批
    default_role: "user"              # 新用户默认角色
    auto_activate: false              # 自动激活账户
  
  # 用户资料
  profile:
    allow_username_change: false      # 允许修改用户名
    allow_email_change: true          # 允许修改邮箱
    require_current_password: true    # 修改资料需要当前密码
  
  # 账户管理
  account:
    soft_delete: true                 # 软删除用户
    retention_days: 30                # 软删除保留天数
    allow_reactivation: true          # 允许重新激活

# 租户管理配置
tenant:
  # 多租户支持
  enabled: true                       # 启用多租户
  isolation_level: "strict"           # 隔离级别: strict, loose
  default_tenant: "default"           # 默认租户
  
  # 租户配置
  settings:
    max_users_per_tenant: 1000        # 每个租户最大用户数
    max_roles_per_tenant: 50          # 每个租户最大角色数
    allow_custom_roles: true          # 允许自定义角色

# 通知配置
notification:
  # 邮件通知
  email:
    enabled: false                    # 启用邮件通知
    smtp_host: ""                     # SMTP服务器
    smtp_port: 587                    # SMTP端口
    smtp_username: ""                 # SMTP用户名
    smtp_password: ""                 # SMTP密码
    from_address: "<EMAIL>"  # 发件人地址
    
    # 邮件模板
    templates:
      welcome: "welcome.html"         # 欢迎邮件模板
      reset_password: "reset.html"    # 密码重置模板
      verify_email: "verify.html"     # 邮箱验证模板
  
  # 短信通知 (可选)
  sms:
    enabled: false                    # 启用短信通知
    provider: ""                      # 短信服务商
    api_key: ""                       # API密钥

# 缓存配置
cache:
  # 用户信息缓存
  user:
    enabled: true                     # 启用用户缓存
    ttl: 30m                         # 缓存过期时间
    max_size: 10000                  # 最大缓存用户数
  
  # 权限缓存
  permission:
    enabled: true                     # 启用权限缓存
    ttl: 15m                         # 缓存过期时间
    max_size: 50000                  # 最大缓存权限数
  
  # 会话缓存
  session:
    enabled: true                     # 启用会话缓存
    ttl: 24h                         # 缓存过期时间

# 监控配置
monitoring:
  # 指标收集
  metrics:
    enabled: true                     # 启用指标收集
    endpoint: "/metrics"              # 指标端点
    interval: "30s"                   # 收集间隔
    
    # 自定义指标
    custom_metrics:
      login_attempts: true            # 登录尝试次数
      active_sessions: true           # 活跃会话数
      permission_checks: true         # 权限检查次数
  
  # 健康检查
  health:
    check_interval: "30s"             # 健康检查间隔
    timeout: "5s"                     # 健康检查超时
    
    # 健康检查项
    checks:
      database: true                  # 数据库连接检查
      redis: true                     # Redis连接检查
      jwt_service: true               # JWT服务检查
  
  # 告警配置
  alerts:
    failed_login_threshold: 100       # 失败登录告警阈值 (每分钟)
    high_error_rate_threshold: 5.0    # 高错误率告警阈值 (%)
    response_time_threshold: 2s       # 响应时间告警阈值

# 集成配置
integrations:
  # LDAP集成 (企业环境)
  ldap:
    enabled: false                    # 启用LDAP集成
    server: ""                        # LDAP服务器
    port: 389                         # LDAP端口
    base_dn: ""                       # 基础DN
    bind_dn: ""                       # 绑定DN
    bind_password: ""                 # 绑定密码
  
  # OAuth2集成
  oauth2:
    enabled: false                    # 启用OAuth2
    providers: []                     # OAuth2提供商配置
  
  # 单点登录 (SSO)
  sso:
    enabled: false                    # 启用SSO
    provider: ""                      # SSO提供商
    metadata_url: ""                  # 元数据URL

# 🔧 开发环境特殊配置
development:
  # 调试功能
  debug: false                        # 调试模式 (生产环境关闭)
  pprof:
    enabled: false                    # 性能分析 (生产环境关闭)
    port: 6065                        # pprof端口
  
  # API文档
  swagger:
    enabled: false                    # Swagger文档 (生产环境关闭)
    path: "/swagger"                  # 文档路径
  
  # 测试功能
  test:
    enabled: false                    # 测试功能 (生产环境关闭)
    mock_users: false                 # 模拟用户数据
    test_endpoints: false             # 测试端点
